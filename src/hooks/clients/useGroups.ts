import { useAddTag<PERSON>pi } from '@/api/tags/add-tag';
import { useDeleteTagApi } from '@/api/tags/delete-tag';
import { useGetTagsQuery } from '@/api/tags/get-tags';
import { useUpdateTagApi } from '@/api/tags/update-tag';
import { useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';
import { useSupabaseSession } from '../auth/useUserSession';

export const useGroups = () => {
  const addDisclosure = useDisclosure();
  const viewDisclosure = useDisclosure();
  const [groupClients, setGroupClients] = useState<any[]>([]);
  const [tagName, setTagName] = useState('');
  const [viewData, setViewData] = useState<any>(null);
  const { UserFromQuery } = useSupabaseSession();
  const { mutateAsync, isLoading } = useAddTagApi();
  const { mutateAsync: deleteMutateAsync } = useDeleteTagApi();
  const { mutateAsync: updateMutateAsync, isLoading: updateIsLoading } =
    useUpdateTagApi();
  const { data } = useGetTagsQuery({
    category: 'group',
    user_id: UserFromQuery?.id,
  }) as any;

  const handleAddClient = (props: any, addMore?: string) => {
    if (addMore === 'addMore') {
      const isClientExists = viewData?.data?.some(
        (client: any) =>
          client.client_id?.id === props.id || client.client_id === props.id
      );

      if (!isClientExists) {
        setViewData((e: any) => ({
          name: e?.name,
          data: [
            ...(e?.data || []),
            {
              name: `${props.first_name} ${props.last_name}`,
              client_id: props.id,
            },
          ],
        }));
      }
      return;
    }

    const isClientExists = groupClients.some(
      (client) => client.client_id === props.id
    );

    if (!isClientExists) {
      setGroupClients((e) => [
        ...(e || []),
        { name: `${props.first_name} ${props.last_name}`, client_id: props.id },
      ]);
    }
  };

  const handleRemoveClient = async (id: any, dbRemove: boolean) => {
    if (dbRemove) {
      await deleteMutateAsync(id);
      return setViewData((prevViewData: any) => ({
        name: prevViewData?.name,
        data: prevViewData.data.filter(
          (item: any) =>
            item.client_id?.id !== id.client_id?.id ||
            item.client_id !== id.client_id
        ),
      }));
    }
    setGroupClients((prevClients) =>
      prevClients.filter((client) => client.client_id !== id)
    );
  };

  const handleCreateSubmit = async () => {
    const payload = groupClients?.map((item) => ({
      client_id: item.client_id,
      category: 'group',
      tag_name: tagName,
    }));
    await mutateAsync(payload);
    addDisclosure.onClose();
    setGroupClients([]);
    setTagName('');
  };

  const handleUpdateSubmit = async () => {
    const payload = viewData?.data?.map((item: any) => ({
      client_id: item.client_id?.id || item.client_id,
      category: 'group',
      tag_name: tagName || viewData?.name,
      id: item.id,
      user_id: UserFromQuery?.id,
    }));
    await updateMutateAsync(payload);
    viewDisclosure.onClose();
    setGroupClients([]);
    setTagName('');
    setViewData(null);
  };

  return {
    data,
    addDisclosure,
    viewDisclosure,
    groupClients,
    submitIsLoading: isLoading,
    updateIsLoading,
    tagName,
    viewData,
    setViewData,
    handleAddClient,
    handleRemoveClient,
    handleCreateSubmit,
    handleUpdateSubmit,
    setTagName,
  };
};

export type TuseGroups = ReturnType<typeof useGroups>;
