// hooks/useContactStages.ts
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';

export interface ContactStageOption {
  label: string;
  value: string;
  color?: string;
}

interface UseContactStagesResult {
  contactStagesOptions: ContactStageOption[] | undefined;
  isLoading: boolean;
  error: Error | null;
}

export const useContactStages = (
  toLowerCaseValue = false
): UseContactStagesResult => {
  // Get user ID from localStorage
  const getUserId = (): number | null => {
    try {
      const raw = localStorage.getItem('UserState');
      if (!raw) return null;
      const data = JSON.parse(raw);

      // console.log('data--5', data);
      return data?.UserState?.id ? Number(data.UserState.id) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  };

  const userId = getUserId();

  // Fetch user data
  const {
    data: UserData,
    isLoading,
    error,
  } = useGetUserByIdQuery(userId, {
    enabled: Bo<PERSON>an(userId),
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });

  // console.log('UserData---5', UserData);

  // Transform contact stages
  const contactStagesOptions = UserData?.organization?.contact_stages?.map(
    (stage: { label: string; color?: string }) => ({
      label: stage.label,
      value: toLowerCaseValue ? stage.label.toLowerCase() : stage.label,
      color: stage?.color,
    })
  );

  return {
    contactStagesOptions,
    isLoading,
    error: error as Error | null,
  };
};
