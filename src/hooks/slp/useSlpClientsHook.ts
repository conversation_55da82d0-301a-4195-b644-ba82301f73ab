import { useGetSlpStatisticsQuery } from '@/api/slp/use-get-slp-statistics';
import { useGetTagsQuery } from '@/api/tags/get-tags';
import { useGetNonInvoicedClientsQuery } from '@/api/users/get-non-invoiced-clients';
import { useSlpIdViewQuery } from '@/api/users/slp-id-view';
import { toaster } from '@/components/ui/toaster';
import { IUser } from '@/shared/interface/user';
import { IsNONsInvoiceUserFilterState } from '@/store/filters/non-invoice-filter';
import { SLPUsersFilterState } from '@/store/filters/slp-user';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FiUserCheck, FiUserMinus, FiUsers, FiUserX } from 'react-icons/fi';
import { useRecoilState } from 'recoil';
import { useSupabaseSession } from '../auth/useUserSession';

export const useSlpClientsHook = ({ slp }: { slp: IUser }) => {
  const [filter, setFilter] = useRecoilState(SLPUsersFilterState);
  const [filterTwo, setFilterTwo] = useRecoilState(
    IsNONsInvoiceUserFilterState
  );
  const { UserFromQuery } = useSupabaseSession();

  const { data: groups } = useGetTagsQuery({
    category: 'group',
    user_id: UserFromQuery?.id,
  }) as any;
  const searchParams = useSearchParams();
  const [isCopied, setIsCopied] = useState(false);
  const [show, setShow] = useState(false);

  const toggleSwitch = () => {
    setShow((prev) => !prev);
  };

  const urlParams =
    searchParams.get('filter_date') || searchParams.get('filter_source');
  const [sorting, setSorting] = useState([
    {
      id: 'last-sessions',
      desc: true,
    },
  ]);

  const { data: arrayData } = useGetSlpStatisticsQuery(
    slp?.status || 'Active',
    { enabled: Boolean(slp) }
  );

  const RPCData = arrayData?.data?.find(
    (item: any) => item?.user_id === slp?.id
  );

  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  console.log('slp', slp);
  const { data: SlpViewData, isLoading: SVpending } = useSlpIdViewQuery(
    slp?.id,
    slp?.organization_id,
    filter
  );
  console.log('SlpViewData----3', SlpViewData);
  const { data: NonInvData, isLoading: NonInvDataLoading } =
    useGetNonInvoicedClientsQuery(slp?.id, slp?.organization_id, filterTwo);

  console.log('NonInvData', NonInvData);

  // console.log('arrayData', arrayData);

  // console.log('NonInvData', NonInvData);
  // console.log('RPPCDta', RPCData);

  // const stats = [
  //   {
  //     name: 'Active Clients',
  //     stat: SlpViewData?.pagination?.active_client_count || '-',
  //   },
  //   {
  //     name: 'Total Clients',
  //     stat: SlpViewData?.pagination?.total_client_count || '--',
  //   },
  //   { name: 'Repeat Rate', stat: RPCData?.repeat_percentage || '--.-%' },
  // ];
  const stats = [
    {
      name: 'Total Clients',
      stat: SlpViewData?.pagination?.total_client_count || 0,
      description: 'All clients',
      icon: FiUsers,
      iconColor: 'blue.500',
    },
    {
      name: 'Active Clients',
      stat: SlpViewData?.pagination?.active_client_count || 0,
      description: 'Clients with active status',
      icon: FiUserCheck,
      iconColor: 'green.500',
    },
    {
      name: 'On Hold',
      stat: SlpViewData?.pagination?.onHoldCount || 0,
      description: 'Clients with on hold status',
      icon: FiUserX,
      iconColor: 'yellow.500',
    },
    {
      name: 'Do Not Contact',
      stat: SlpViewData?.pagination?.doNotContactCount || 0,
      description: 'Clients with do not contact status',
      icon: FiUserMinus,
      iconColor: 'red.500',
    },
  ];

  const handleExportCSV = () => {
    if (SlpViewData?.data.length === 0) {
      toaster.create({
        description: 'No contacts selected to export as CSV!',
        type: 'error',
      });
      return;
    }

    const csvContent =
      'data:text/csv;charset=utf-8,' +
      SlpViewData?.data
        .map((contact: any) => {
          return `${contact.first_name},${contact.last_name},${contact.email}`;
        })
        .join('\n');

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', 'contacts.csv');
    document.body.appendChild(link);
    link.click();
  };
  const handleCopyLoginLink = async (e: any) => {
    e.stopPropagation();

    if (typeof window !== 'undefined') {
      try {
        await navigator.clipboard.writeText(
          `${window?.location.origin}/client/${slp.organization?.slug}/login`
        );
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000); // Reset after 2 seconds
      } catch (err) {
        console.error('Failed to copy: ', err);
      }
    }
  };

  useEffect(() => {
    if (urlParams) {
      setFilter({
        ...filter,
        date_filter_status: searchParams.get('filter_source') as string,
        filter_date: searchParams.get('filter_date') as string,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [urlParams]);
  // useEffect(() => {
  //   if (urlParams) {
  //     setFilterTwo({
  //       ...filter,
  //       date_filter_status: searchParams.get('filter_source') as string,
  //       filter_date: searchParams.get('filter_date') as string,
  //     });
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [urlParams]);

  // useEffect(() => {
  //   setFilter({
  //     ...filter,
  //     show_non_invoiced_clients: show,
  //   });
  // }, [show]);

  return {
    data: SlpViewData?.data || [],
    SlpViewData,
    stats,
    handleExportCSV,
    isLoading: SVpending || NonInvDataLoading,
    sorting,
    setSorting,
    show,
    pagination,
    setPagination,
    toggleSwitch,
    NonInvData,
    NonInvDataLoading,
    total_count: SlpViewData?.pagination?.total_count || 0,
    total_nonInvoiced_count:
      NonInvData?.pagination?.total_non_invoiced_count || 0,
    filter,
    setFilter,
    filterTwo,
    setFilterTwo,
    groups,
    RPCData,
    handleCopyLoginLink,
    isCopied,
  };
};
