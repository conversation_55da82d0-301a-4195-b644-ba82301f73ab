/* eslint-disable react-hooks/exhaustive-deps */

// Imports: External Libraries
import { useFormik } from 'formik';
import moment from 'moment';
import { useCallback, useEffect, useState } from 'react';
// Import * as Yup from 'yup';
import { useDisclosure } from '@chakra-ui/react';
import { useQueries, useQueryClient } from '@tanstack/react-query';
import { usePathname, useRouter } from 'next/navigation';

// Imports: Application-Specific
import { useCreateBookingMutation } from '@/api/bookings/create-booking';
import {
  fetchClientById,
  useGetClientByIdQuery,
} from '@/api/clients/get-client-by-id';
import { useGetLinkedClientsQuery } from '@/api/linked_clients/get-linked-clients';
import { useCreateSlpNoteMutation } from '@/api/slp_notes/create-slp-note';
import { useGetSoapNoteQuery } from '@/api/slp_notes/get-soap-note';
import { useUpdateSlpNoteMutation } from '@/api/slp_notes/update-slp-note';
import { SlpSessionDisplay } from '@/app/(dashboard)/slp/[id]/sessions/columnDef';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { slpSessionOptions } from '@/data/options/sessions';
// import { useUpdatePackageMutation } from '@/api/packages/update-package';
import { useUpdateBookingMutation } from '@/api/bookings/update-booking';
import { useCreateAndSendInvoiceMutation } from '@/api/invoices/create-and-send-invoice';
import { useGetMaxInvoiceNumberQuery } from '@/api/invoices/max-invoice-number';
import { useGetAllProductsQuery } from '@/api/products/get-all-products';
import { useGetAllReferralsQuery } from '@/api/referrals/get-all-referrals';
import { useUpdateReferralCreditMutation } from '@/api/referrals/updateReferralCredit';
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';
import { toaster } from '@/components/ui/toaster';
// import { useRouter } from 'next/navigation';
import { validatePayload } from '@/app/(dashboard)/slp/[id]/create-invoice/[booking]/utils';
import { getLinkedClientOptionsFromArray } from '@/utils/get-options-from-array';
import { getPrimaryEmail } from '@/utils/helper';
export const useCreateInvoiceHook = ({
  initialBooking,
  onClose,

  isOpen,
}: {
  initialBooking: SlpSessionDisplay | undefined;
  onClose?: any;
  isOpen?: any;
  abbr?: any;
}) => {
  // =========== Initializations & State Management ===========

  const queryClient = useQueryClient();
  const path = usePathname();
  const slp_id = path.split('/')[2];
  const router = useRouter();
  const [noClientPackage, setNoClientPackage] = useState<any>(false);
  const [isNewBooking, setIsNewBooking] = useState<any>(false);
  const [allPackages, setAllPackages] = useState([]);
  const [selectSearch, setSelectSearch] = useState(false);
  const [handleCreateInvoiceLoading, setHandleCreateInvoiceLoading] =
    useState(false);
  const [
    handleCreateInvoiceRequestLoading,
    setHandleCreateInvoiceRequestLoading,
  ] = useState(false);
  // const [submitLoading, setSubmitLoading] = useState(false);
  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const [saveInvoiceDraft, setSaveInvoiceDraft] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<any>();
  const [booking, setBooking] = useState<any | undefined>(
    isNewBooking
      ? undefined
      : { ...initialBooking, linked_clients: initialBooking?.clients }
  );
  const [invoiceDataToSendAsEmail, setInvoiceDataToSendAsEmail] =
    useState<any>();

  const CSEmailDisclosure = useDisclosure();

  // console.log('booking--55', booking);

  // =========== Disclosures for Modals ===========
  const {
    onClose: onNoClientPackageClose,
    onOpen: onNoClientPackageOpen,
    open: isOpenClientNoPackage,
  } = useDisclosure();

  // =========== Derived Data & Memos ===========

  const { data: AllProducts, isLoading: AllProductsLoading } =
    useGetAllProductsQuery();

  const productOptions =
    AllProducts && !AllProductsLoading
      ? AllProducts.allProducts
          .filter((product: any) => product?.status?.toLowerCase() === 'active') // Filter active products
          .map((product: any) => ({
            ...product,
            label: product.name,
            value: product.name,
          }))
      : [];

  const incompletePackages = (allPackages ?? []) // Ensure it's always an array
    .filter(
      (item: any) =>
        item?.status?.toLowerCase() === 'incomplete' ||
        item?.status?.toLowerCase() === 'active' ||
        item?.status?.toLowerCase() === 'paid'
    )
    .sort(
      (a: any, b: any) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

  console.log('incompletePackages', incompletePackages);
  const isIncompletePackageMoreThanOne = incompletePackages.length > 1;

  // Ensure currentIncompletePackage is always defined and won't break the code
  const currentIncompletePackage: any =
    incompletePackages.length > 0
      ? incompletePackages[0] // The oldest one
      : incompletePackages[0];

  // console.log('allPackage', allPackages);
  // console.log('cuurrr', currentIncompletePackage);

  const { data: LinkedClients, isLoading: LinkedClientLoading } =
    useGetLinkedClientsQuery(booking?.client_id as any, {
      enabled: Boolean(booking?.client_id) && isOpen,
    });

  // console.log('linkedClients', LinkedClients);

  const finalSessionOptions = currentIncompletePackage
    ? slpSessionOptions
    : slpSessionOptions.map((item: any) => {
        if (item.value?.toLowerCase() === 'package') {
          return {
            label: 'Package',
            value: 'Package',
            isDisabled: false,
          };
        } else {
          return item;
        }
      });

  const linkedClientOptions: any = getLinkedClientOptionsFromArray(
    LinkedClients || []
  );

  // =========== Data Fetching: Queries ==========='

  const { data: Slp } = useGetUserByIdQuery(Number(slp_id), {
    enabled: Boolean(slp_id),
  });

  const { data: MaxInvoiceNumber } = useGetMaxInvoiceNumberQuery(
    Number(Slp?.organization?.id),
    { enabled: Boolean(Slp?.organization?.id) }
  );

  // console.log('linkedclinet', linkedClientOptions);
  const packageQueries: any = useQueries({
    queries:
      linkedClientOptions?.length === 0
        ? [
            {
              queryKey: [queryKey.client.getById, booking?.client_id],
              queryFn: () => fetchClientById(booking?.client_id),
              enabled: isOpen && Boolean(booking?.client_id),
            },
          ]
        : linkedClientOptions?.map((item: any) => ({
            queryKey: [queryKey.client.getById, item.value],
            queryFn: () => fetchClientById(item.value),
            enabled: isOpen && Boolean(item.value),
          })),
  });

  // console.log('pacakgesQueries', packageQueries);

  // const invalidateAndRefetchPackages = () => {
  //   linkedClientOptions?.forEach((item) => {
  //     queryClient.invalidateQueries([queryKey.client.getById, item.value]);
  //   });
  // };

  const { data: SoapNotes, isLoading: SoapNotesLoading } = useGetSoapNoteQuery(
    {
      clientId:
        selectedClientId ||
        initialBooking?.slp_notes?.client_id ||
        Number(booking?.client_id),
    },
    { enabled: Boolean(booking?.client_id) && isOpen }
  );
  const { data: Referrals } = useGetAllReferralsQuery(
    selectedClientId ||
      initialBooking?.slp_notes?.client_id ||
      Number(booking?.client_id),

    { enabled: Boolean(booking?.client_id) && isOpen }
  ) as any;

  const validReferral = Referrals?.filter((item: any) => {
    const currentClient = selectedClientId || Number(booking?.client_id);
    if (item.referrer_id === Number(currentClient) && !item.referrer_invoice) {
      return true;
    }
    if (item.referee_id === Number(currentClient) && !item.referee_invoice) {
      return true;
    }
    return false;
  });

  const validReferralCredit = validReferral?.reduce(
    (sum: any, obj: any) => sum + (obj.credit || 0),
    0
  );

  // acti({ selectedClientId });

  // console.log({ linkedClientOptions });

  const { data: CurrentClient, isLoading: ClientLoading } =
    useGetClientByIdQuery(
      selectedClientId ||
        Number(
          initialBooking?.slp_notes?.client_id ||
            initialBooking?.client_id ||
            booking?.client_id
        ),

      { enabled: Boolean(booking?.client_id) && isOpen }
    );

  // =========== Mutations ===========
  const { mutateAsync: UpdateSlpNote, isLoading: UpdateSlpNoteLoading } =
    useUpdateSlpNoteMutation();
  const { mutateAsync: CreateSlpNote, isLoading: CreateSlpNoteLoading } =
    useCreateSlpNoteMutation();
  const { mutateAsync: CreateBooking, isLoading: CreateBookingLoading } =
    useCreateBookingMutation();
  // const { mutateAsync: UpdatePackage, isLoading: UpdatePackageLoading } =
  //   useUpdatePackageMutation();
  const { mutateAsync: UpdateBooking, isLoading: UpdateBookingLoading } =
    useUpdateBookingMutation();
  const {
    mutateAsync: CreateAndSendInvoice,
    isLoading: CreateAndSendInvoiceLoading,
  } = useCreateAndSendInvoiceMutation({ disableToast: true });
  const { mutateAsync: URcapi } = useUpdateReferralCreditMutation({
    disableToast: true,
  });

  // =========== Formik for Form Management ===========

  // const createInvoiceInitialValues = {
  //   invoice_date: booking?.appointment
  //     ? new Date(booking?.appointment)
  //     : new Date(),
  //   booking_id: booking?.id,
  //   client_id: booking?.slp_notes?.client_id || booking?.client_id,
  //   slp_id: booking?.slp_id || Number(slp_id),
  //   // slp_id: editProfileSoap ? booking?.slp_id : Number(slp_id),
  //   soap_note: booking?.slp_notes?.soap_note ?? undefined,
  //   status: 'Pending',
  //   session_type: booking?.event
  //     ? booking?.event.indexOf('Assessment') > -1
  //       ? 'Assessment'
  //       : booking.event.indexOf('ackage') > -1
  //         ? 'Package'
  //         : 'Payg'
  //     : '',
  //   duration: booking?.slp_notes?.duration ?? '',
  //   invoice_memo: booking?.slp_notes?.invoice_memo ?? '',
  //   split_ax: booking?.slp_notes?.split_ax ?? 'false',
  //   referral: booking?.slp_notes?.referral ?? 'false',
  //   no_show: booking?.slp_notes?.no_show ?? 'false',
  //   package_id: booking?.slp_notes?.package_id,
  //   session_count: booking?.slp_notes?.session_count || 1,
  // };
  // console.log('currentIncomplete', currentIncompletePackage);
  // const validationSchema = Yup.object().shape({
  //   slp_id: Yup.number(),
  //   client_id: Yup.number().required('Client ID is required'),
  //   invoice_memo: Yup.string(),
  //   soap_note: Yup.string(),
  //   referral: Yup.string(),
  //   no_show: Yup.string(),
  //   split_ax: Yup.string(),
  //   session_type: Yup.string(),
  //   duration: Yup.string(),
  //   session_count: Yup.string(),
  // });
  const {
    values,
    handleSubmit,
    errors,
    handleChange,
    touched,
    handleBlur,
    setFieldValue,
    setValues,
    submitCount,
  } = useFormik({
    initialValues: {},
    // initialValues: createInvoiceInitialValues,
    // enableReinitialize: true,
    onSubmit: async (values: any) => {
      // return console.log('values', values)
      const toastStack = [];
      try {
        setHandleCreateInvoiceRequestLoading(true);

        const insert: any = {
          slp_id: values?.slp_id || slp_id,
          booking_id: booking ? booking.id : undefined,
          client_id: values.client_id || booking.client_id,
          // linked_client_id: values.linked_client_id,
          status: 'Pending',
          invoice_memo: values.invoice_memo,
          soap_note: values.soap_note,
          referral: values.referral,
          no_show: values.no_show,
          split_ax: values.split_ax,
          duration:
            values.session_type?.toLowerCase() === 'package'
              ? Number(values.session_count || 0) *
                Number(currentIncompletePackage?.session_duration)
              : values.duration,
          package_id: currentIncompletePackage
            ? currentIncompletePackage?.id
            : null,
          session_count: values.session_count,
          organization_id: Slp?.organization_id,
        };
        const sessionTypeIsPackage =
          values.session_type?.toLowerCase() === 'package';
        // console.log('insert is ', insert);
        // console.log('values is ', values);

        // return;

        //============ VALIDATIONS ============
        if (
          sessionTypeIsPackage &&
          Number(allPackages?.length) === 0 &&
          !noClientPackage
        ) {
          onNoClientPackageOpen();
          setNoClientPackage(false);
          setHandleCreateInvoiceRequestLoading(false);
          return;
        }

        if (sessionTypeIsPackage && !currentIncompletePackage) {
          if (
            !values.duration ||
            !values.session_count ||
            !values.session_type
          ) {
            throw new Error('Fill in all Fields.');
          }
        }

        if (!sessionTypeIsPackage) {
          if (!values.duration) {
            throw new Error('Duration is required.');
          }
        }
        // if (
        //   !values.session_type ||
        //   (sessionTypeIsPackage && values?.session_count <= 0) ||
        //   !insert.duration
        // ) {
        //   throw new Error('Duration and Session type is required.');
        // }

        // //============ CREATE BOOKING FOR COMPLETELY NEW SESSION ============

        // initially before implementing the product fucntionality we make use of the session_type for the event

        if (!insert?.booking_id) {
          if (isNewBooking) {
            delete booking.clients;
            const newBooking: any = { ...booking };
            delete newBooking.linked_clients;
            newBooking.appointment = values.invoice_date;
            newBooking.appointment_raw = moment
              .utc(values.invoice_date)
              .format('MM/DD/YYYY HH:mm:ss');
            newBooking.assigned_to = Slp ? Slp?.email : '';

            newBooking.event = values.session_type;
            newBooking.client_id = insert.client_id;
            newBooking.email = Array.isArray(booking?.email)
              ? getPrimaryEmail(booking?.email)
              : booking?.email;
            newBooking.organization_id = Slp?.organization_id;

            // console.log('newBooking', newBooking);
            const data = await CreateBooking(newBooking);

            console.log('data', data);

            insert.booking_id = data?.id;
            insert.client_id = newBooking.client_id;
            insert.slp_id = newBooking.slp_id;
          }
        }

        if (!insert.client_id || !insert.slp_id)
          throw new Error('Something went wrong, Error: Missing Ids');

        //============ UPDATE BOOKING APPOINTMENT IF DATE CHANGES ============
        // if(mo){

        await UpdateBooking({
          data: {
            assigned_to: Slp ? Slp?.email : '',
            appointment: values.invoice_date,
            ...(!booking.event ? { event: values.session_type } : {}),
          },
          id: Number(insert.booking_id),
        });
        // }

        //============ UPDATE THE PACKAGE BALANCE============
        // if (sessionTypeIsPackage) {
        //   let amountToSubtract = values.session_count;

        //   // If booking has `slp_notes`, calculate the difference
        //   if (booking?.slp_notes) {
        //     const previousCount = booking.slp_notes.session_count || 0;
        //     amountToSubtract -= previousCount;
        //   }

        //   const newPackageBalance =
        //     Number(currentIncompletePackage.balance) - Number(amountToSubtract);

        //   await UpdatePackage({
        //     data: { balance: newPackageBalance },
        //     id: currentIncompletePackage.id,
        //   });
        //   await invalidateAndRefetchPackages();
        // }

        if (insert?.booking_id) {
          //============ EDIT SLP NOT IF ITS ALREADY AVAILABLE ============
          if (booking?.slp_notes) {
            console.log('booking slp_notes', booking);
            await UpdateSlpNote({ data: insert, id: booking?.slp_notes?.id });
            toastStack.push({
              type: 'success',
              description: 'Slp Note updated',
            });
          } else {
            await CreateSlpNote({
              ...insert,
              note_date: values.invoice_date,
            });
            toastStack.push({
              type: 'success',
              description: 'Slp Note created',
            });
          }
        }
        await queryClient.invalidateQueries({
          queryKey: [
            queryKey.bookings.getSlpBookings,
            {
              date: moment(values?.invoice_date).format('YYYY-MM-DD'),
              slpId: Number(slp_id),
            },
          ],
        });
        await queryClient.invalidateQueries({
          queryKey: [
            queryKey.slpNotes.getByClientId,
            {
              clientId: Number(insert?.client_id),
            },
          ],
        });

        await queryClient.invalidateQueries({
          queryKey: [queryKey.bookings.getById, insert.booking_id],
        });
        if (toastStack.length) {
          toastStack.forEach((toast) => {
            toaster.create(toast);
          });
        }
        handleCloseModal();
      } catch (error: any) {
        toaster.create({
          description: error.message || ToastMessages.somethingWrong,
          type: 'error',
        });
        setHandleCreateInvoiceRequestLoading(false);
      } finally {
        // setSubmitLoading(false);
        setHandleCreateInvoiceRequestLoading(false);
      }
      return values;
    },
  });

  // =========== Utility Functions ===========
  const handleCloseNoPackageModal = () => {
    onNoClientPackageClose();
    setNoClientPackage(true);
  };

  const handleCloseSendInvoiceEmailModalAndNavigate = useCallback(() => {
    CSEmailDisclosure.onClose();
    handleCloseModal();
    const formattedDate = moment(values?.invoice_date).format('YYYY-MM-DD');

    router.push(`/slp/${values.slp_id}?date=${formattedDate}`);
  }, [CSEmailDisclosure, values?.slp_id, values?.invoice_date, router]);
  const handleCloseSendInvoiceEmailModal = useCallback(() => {
    CSEmailDisclosure.onClose();
  }, [CSEmailDisclosure]);

  const handleSearchSelect = async (client: any) => {
    setSelectSearch(true);
    if (!client) return;
    const b: any = {};
    b.client_id = client.id;
    b.email = client?.emails;
    b.slp_id = slp_id;
    setFieldValue('client_id', client.id);
    setIsNewBooking(true);
    setBooking({
      client_id: client.id,
      email: client?.emails,
      slp_id: Number(slp_id),
      clients: client,
      linked_clients: client,
    });
  };

  // console.log('values', values);

  const handleFormSubmit = (event?: React.FormEvent) => {
    if (event) {
      event.preventDefault(); // Prevent default form submission behavior
    }
    handleSubmit();
  };

  const handleCloseModal = () => {
    if (isNewBooking) {
      setBooking(null);
    }
    setIsNewBooking(false);
    onClose();
  };

  // TOTAL DURATION
  const totalDuration =
    Number(values.session_count || 0) *
    Number(currentIncompletePackage?.session_duration);
  // =========== Create And Send Invoice Functionality ===========
  // console.log('booking is ', booking);

  // console.log('values--invd', values?.invoice_date);
  // console.log('booking date', booking?.appointment);
  // console.log('currentIncomplete', currentIncompletePackage);
  // console.log('validReferral', validReferral);
  // console.log('slectedid', selectedClientId);
  // console.log('incompleteBoolean', !!incompletePackages);

  const handleSaveInvoiceDraft = () => {
    setSaveInvoiceDraft(true);
    handleCreateAndSendInvoice('saveAsDraft');
  };

  // This is for new users create and send invoice flow
  const handleCreateAndSendInvoice = async (prop = 'saveAndSend') => {
    //Validations

    const unredeemedReferrals = validReferral
      // ?.filter((item: any) => !item.referrer_invoice)
      ?.sort(
        (a: any, b: any) =>
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );

    // Get the oldest unredeemed referral
    const oldestReferral = unredeemedReferrals?.[0];

    // console.log('oldestReferral is ', oldestReferral);
    // const isPackageIncomplete = !!currentIncompletePackage;
    // console.log('ispacakagg0---3', isPackageIncomplete);
    const validateResult = validatePayload(
      values,
      booking
      // oldestReferral,
      // isPackageIncomplete,
      // Slp
    );
    if (!validateResult.isValid) {
      toaster.create({
        type: 'error',
        description: validateResult.message,
      });
      return;
    }

    // console.log('booking is ', booking);
    // console.log('values is ', values);
    // return;
    const productDetail = AllProducts?.allProducts?.find(
      (item: any) => item?.name === values?.product
    );

    // console.log('currentIncomplete', currentIncompletePackage);
    // console.log('saveinvoiceDrafe----55', saveInvoiceDraft);

    // if (
    //   values?.session_type === 'Assessment' &&
    //   (values?.referral === 'true' || values?.split_ax === 'true')
    // ) {
    //   toaster.create({
    //     type: 'error',
    //     description:
    //       "you can't do this yet, this still has to be handled manually.",
    //   });

    //   return;
    // }

    // Filter and sort unredeemed referrals by created_at

    const payload: any = {};

    payload.slp_id = booking?.slp_id;
    payload.name = `${booking?.clients?.first_name} ${booking?.clients?.last_name}`;
    payload.client_id = Number(
      selectedClientId || values?.client_id || booking?.client_id
    );
    payload.email = Array.isArray(booking?.email)
      ? getPrimaryEmail(booking?.email)
      : booking?.email;
    payload.product = values?.product || booking?.product;
    payload.qty = values?.session_count || 1;
    payload.package = null;
    payload.memo =
      values?.referral === 'true'
        ? 'FREE REFERRAL'
        : values?.invoice_memo || '';
    payload.organization_id =
      booking?.organization_id ||
      values?.organization_id ||
      booking?.clients?.organization_id;
    // i  think if session_type is payg then we should use the duration
    payload.total_hours = productDetail?.minutes || 0;
    payload.referral = values?.referral;
    payload.duration = productDetail?.minutes || 0;

    // i also believe that the interval should be the duration  if its payg
    payload.interval = productDetail?.minutes || 0;

    payload.invoice_number = Number(MaxInvoiceNumber) + 1;
    payload.invoice_date = moment(values?.invoice_date).format('YYYY-MM-DD');
    payload.session_type = null;
    payload.total_price = productDetail?.price * (values?.session_count || 1);
    payload.package_size = null;
    payload.package_id = null;

    // (payload.package_id =
    //   values.session_type === 'package' && currentIncompletePackage
    //     ? currentIncompletePackage?.id
    //     : null),
    // return;
    // return;
    // total_price:
    //   values?.session_type === 'Assessment'
    //     ? 230
    //     : values?.session_type?.toLowerCase() === 'payg'
    //       ? 100
    //       : currentIncompletePackage
    //         ? (Number(currentIncompletePackage.total) /
    //             Number(currentIncompletePackage.session_quantity || 1)) *
    //           Number(values?.session_count || 0)
    //         : '',

    // create soap note for invoice

    const soapNotePayload = {
      slp_id: booking?.slp_id,
      booking_id: booking?.id,
      client_id: payload.client_id,
      status: 'Pending',
      invoice_memo: values?.invoice_memo,
      soap_note: values.soap_note,
      referral: values.referral,
      no_show: values.no_show,
      split_ax: values.split_ax,
      duration: payload?.duration,
      package_id: payload.package_id,
      session_count: values.session_count,
      note_date: values?.invoice_date,
    };

    try {
      setHandleCreateInvoiceLoading(true);

      // we want to only send the already created invoice receipt via email
      if (prop === 'sendCreatedInvoice') {
        CSEmailDisclosure.onOpen();

        setInvoiceDataToSendAsEmail(booking?.slp_notes?.invoice);
        setHandleCreateInvoiceLoading(false);
        return;
      }

      if (booking?.slp_notes) {
        console.log('booking slp_notes----4', booking);
        await UpdateSlpNote({
          data: soapNotePayload,
          id: booking?.slp_notes?.id,
        });

        toaster.create({
          type: 'success',
          description: 'Slp Note updated',
        });

        await queryClient.refetchQueries({
          queryKey: [
            queryKey.slpNotes.getByClientId,
            {
              clientId: Number(payload?.client_id),
            },
          ],
        });
        await queryClient.refetchQueries({
          queryKey: [
            queryKey.bookings.getSlpBookings,
            {
              date: moment(values?.invoice_date).format('YYYY-MM-DD'),
              slpId: Number(slp_id),
            },
          ],
        });
        return;
      } else {
        if (prop === 'saveAndSend') {
          CSEmailDisclosure.onOpen();
        }

        if (!soapNotePayload?.booking_id) {
          if (isNewBooking) {
            delete booking.clients;
            delete booking.linked_clients;
            const newBooking: any = { ...booking };
            newBooking.appointment = values.invoice_date;
            newBooking.assigned_to = Slp ? Slp?.email : '';
            newBooking.appointment_raw = moment
              .utc(values.invoice_date)
              .format('MM/DD/YYYY HH:mm:ss');

            newBooking.event = values.product;
            newBooking.email = Array.isArray(booking?.email)
              ? getPrimaryEmail(booking?.email)
              : booking?.email;
            // newBooking.organization_id = Slp?.organization_id;
            const data = await CreateBooking(newBooking);

            soapNotePayload.booking_id = data?.id;
            soapNotePayload.client_id = newBooking.client_id;
            soapNotePayload.slp_id = newBooking.slp_id;
          } else {
            //============ UPDATE BOOKING APPOINTMENT IF DATE CHANGES ============
            await UpdateBooking({
              data: {
                assigned_to: Slp ? Slp?.email : '',

                appointment: values.invoice_date,
                ...(!booking.event ? { event: values.product } : {}),
              },
              id: Number(soapNotePayload?.booking_id),
            });
          }
        }

        const soapNoteData: any = await CreateSlpNote({
          ...soapNotePayload,
        });
        payload.soap_note_id = soapNoteData?.data[0]?.id;
      }

      const response = await CreateAndSendInvoice(payload);

      // open invoice receipt modal

      if (response) {
        if (oldestReferral && values.referral === 'true') {
          // const referralPayload: any = {
          //   invoiceId: response?.id,
          //   refreeId: oldestReferral?.referrer?.id,
          //   id: oldestReferral?.id,
          //   isReferee: false,
          // };

          await URcapi({
            referral: oldestReferral,
            client_id: response.client_id,
            invoice_id: response.id,
          });
        }

        // send newly created invoice data
        if (!booking?.slp_notes && prop === 'saveAndSend') {
          setInvoiceDataToSendAsEmail(response);
        }

        await queryClient.refetchQueries({
          queryKey: [
            queryKey.slpNotes.getByClientId,
            {
              clientId: Number(payload?.client_id),
            },
          ],
        });

        await queryClient.refetchQueries({
          queryKey: [
            queryKey.referrals.getAllReferrals,
            selectedClientId ||
              initialBooking?.slp_notes?.client_id ||
              Number(booking?.client_id),
          ],
        });
        await queryClient.refetchQueries({
          queryKey: [
            queryKey.bookings.getSlpBookings,
            {
              date: moment(values?.invoice_date).format('YYYY-MM-DD'),
              slpId: Number(slp_id),
            },
          ],
        });
        ``;
        await queryClient.refetchQueries({
          queryKey: [
            queryKey.bookings.getById,
            Number(soapNotePayload?.booking_id),
          ],
        });
        await queryClient.refetchQueries({
          queryKey: ['get-max-invoice-number', Number(Slp?.organization.id)],
        });

        if (prop === 'saveAsDraft') {
          toaster.create({
            type: 'success',
            description: ToastMessages.invoices.createDraftSuccess,
          });
        }

        setHandleCreateInvoiceLoading(false);
      }
    } catch (error) {
      toaster.create({
        type: 'error',
        description: 'Something went wrong.',
      });
      setHandleCreateInvoiceLoading(false);
    } finally {
      setHandleCreateInvoiceLoading(false);
      if (prop === 'saveAsDraft') {
        handleCloseModal();
        setSaveInvoiceDraft(false);

        // navigate back to sessions table

        const updatedDate = payload?.invoice_date;
        router.push(`/slp/${payload?.slp_id}?date=${updatedDate}`);
      }
    }
  };

  // console.log('allPackages', allPackages);
  // console.log('incompelte', incompletePackages);
  // console.log('currentIncompletePacake', currentIncompletePackage);

  // end

  // =========== Effects ===========

  // filter products from selected session_type

  // const filteredProducts = useMemo(() => {
  //   if (!values.session_type) return productOptions;

  //   const filterMap: any = {
  //     Package: 'Package',
  //     Assessment: 'Assessment',
  //     Payg: 'Pay as you go',
  //   };

  //   const searchTerm = filterMap[values.session_type];

  //   const result = productOptions.filter((item: any) =>
  //     item.value.includes(searchTerm)
  //   );

  //   // console.log('result', result);

  //   // console.log('product ', productOptions);

  //   if (result.length === 0) return productOptions;

  //   return result;
  // }, [productOptions, values.session_type]);

  // useEffect(() => {
  //   if (values.session_type && filteredProducts.length > 0) {
  //     const firstProduct = filteredProducts[0];

  //     // Only update if no product is selected or the current product is not in filteredProducts
  //     const isProductValid = filteredProducts.some(
  //       (item: any) => item.value === values.product
  //     );

  //     if (!values.product || !isProductValid) {
  //       setFieldValue('product', firstProduct.value);
  //     }
  //   }
  // }, [values.session_type, filteredProducts, values.product, setFieldValue]);

  // check if the session_type is isPaygOrAssesssment

  // useEffect(() => {
  //   setNoClientPackage(true);
  // }, [onNoClientPackageClose]);

  useEffect(() => {
    if (values.product) {
      const productDetail = AllProducts?.allProducts?.find(
        (item: any) => item?.name === values?.product
      );

      setFieldValue('duration', productDetail?.minutes);
    }
  }, [values.product]);

  useEffect(() => {
    if (!isOpen) return;

    // Check if all queries have succeeded
    if (packageQueries.every((query: any) => query.isSuccess)) {
      const newArr = packageQueries
        .filter((query: any) => query.isSuccess) // Only consider successful queries
        .flatMap((query: any) => query.data?.packages || []); // Combine all packages into a single array

      // Only update state if the new array is different
      setAllPackages((prevPackages: any) => {
        const isEqual =
          prevPackages.length === newArr.length &&
          prevPackages.every((pkg: any, index: any) => pkg === newArr[index]);

        if (isEqual) {
          return prevPackages; // No change, avoid state update
        }
        return newArr;
      });
    }
  }, [packageQueries, isOpen]);

  useEffect(() => {
    if (isOpen) {
      setBooking({
        ...initialBooking,
        linked_clients: initialBooking?.clients,
      });
      setSelectedClientId(null);
    }

    setValues({
      invoice_date: initialBooking?.appointment
        ? new Date(initialBooking?.appointment)
        : new Date(),
      booking_id: initialBooking?.id,
      client_id:
        initialBooking?.slp_notes?.client_id || initialBooking?.client_id,
      slp_id: initialBooking?.slp_id || Number(slp_id),
      // slp_id: editProfileSoap ? booking?.slp_id : Number(slp_id),
      soap_note: initialBooking?.slp_notes?.soap_note ?? undefined,
      status: 'Pending',
      session_type: initialBooking?.event
        ? initialBooking?.event.indexOf('Assessment') > -1
          ? 'Assessment'
          : initialBooking.event.indexOf('ackage') > -1
            ? 'Package'
            : 'Payg'
        : '',
      duration: initialBooking?.slp_notes?.duration ?? '',
      invoice_memo: initialBooking?.slp_notes?.invoice_memo ?? '',
      split_ax: initialBooking?.slp_notes?.split_ax ?? 'false',
      referral: initialBooking?.slp_notes?.referral ?? 'false',
      no_show: initialBooking?.slp_notes?.no_show ?? 'false',
      session_count: booking?.slp_notes?.session_count || 1,
      organization_id: Slp?.organization_id,
      product: initialBooking?.slp_notes?.invoice?.product,
    });
  }, [initialBooking, isOpen, Slp]);
  return {
    values,
    handleSubmit,
    errors,
    onNoClientPackageClose,
    onNoClientPackageOpen,
    isOpenClientNoPackage,
    handleChange,
    touched,
    submitCount,
    handleBlur,
    handleSaveInvoiceDraft,
    setFieldValue,
    booking,
    setBooking,
    // onClose,
    handleFormSubmit,
    selectSearch,
    // submitLoading,
    setSearchResult,
    searchResult,
    handleSearchSelect,
    linkedClientOptions,

    setSelectedClientId,
    selectedClientId,
    handleCloseModal,
    LinkedClientLoading,
    currentClient: booking?.clients,
    SoapNotes,
    SoapNotesLoading,
    CreateSlpNoteLoading,
    handleCreateInvoiceLoading,
    handleCreateInvoiceRequestLoading,
    UpdateSlpNoteLoading,
    CreateBookingLoading,

    isNewBooking,
    productOptions,
    CreateAndSendInvoiceLoading,
    Slp,
    setIsNewBooking,
    CSEmailDisclosure,
    saveInvoiceDraft,
    handleCloseSendInvoiceEmailModalAndNavigate,
    invoiceDataToSendAsEmail,
    handleCloseSendInvoiceEmailModal,
    handleCreateAndSendInvoice,
    CurrentClient,
    handleCloseNoPackageModal,
    ClientLoading,
    totalDuration,
    allPackages,
    package_id: booking?.slp_notes?.package_id,
    isIncompletePackageMoreThanOne,
    incompletePackages,
    currentIncompletePackage,
    finalSessionOptions,
    // UpdatePackageLoading,
    UpdateBookingLoading,
    validReferralCredit,
    noClientPackage,
    setNoClientPackage,
    setHandleCreateInvoiceRequestLoading,
    AllProducts,
    MaxInvoiceNumber,
  };
};

export type TCreateInvoiceHook = ReturnType<typeof useCreateInvoiceHook>;
