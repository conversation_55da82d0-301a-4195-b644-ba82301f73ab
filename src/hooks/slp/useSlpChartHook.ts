import { useFindSlpInvoiceChartApi } from '@/api/slp';
import { useSlpIdViewQuery } from '@/api/users/slp-id-view';
import { useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';
import { usePayScheduleHook } from './usePayScheduleHook';
import { useGetInvoicesByUserQuery } from '@/api/invoices/find-by-user';
import { filterAndCalculateTotalHours } from '@/utils/invoiceUtils';
import { useUpdateSLPMutation } from '@/api/slp/update-slp';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { convertMinuteToHours } from '@/utils/date-formatter';
import { useGetSlpStatisticsQuery } from '@/api/slp/use-get-slp-statistics';

export const useSlpChartHook = ({ slpData }: { slpData: any }) => {
  const apiPayload = {
    id: Number(slpData?.id),
    organization_id: Number(slpData?.organization_id),
  };
  const [filter, setFilter] = useState({
    items_per_page: 50,
    page_number: 1,
    filter_date: '',
    date_filter_status: '',
    non_active_client: false,
  });

  const { data: arrayData } = useGetSlpStatisticsQuery(
    slpData?.status || 'Active',
    { enabled: Boolean(slpData) }
  );
  const RPCData = arrayData?.data?.find(
    (item: any) => item?.user_id === slpData?.id
  );

  const { data, isLoading: InvoiceChartLoading } =
    useFindSlpInvoiceChartApi(apiPayload);
  const [payload, setPayload] = useState<number>(slpData?.target_hr_per_month);
  const [timezone, setTimezone] = useState(slpData?.timezone);
  const { open, onClose, onOpen } = useDisclosure();
  const timezoneDisclosure = useDisclosure();
  const repeatDisclosure = useDisclosure();
  const { mutateAsync, isLoading: UpdatetargetLoading } =
    useUpdateSLPMutation();
  const { data: SlpIdViewData, isLoading: slpIdViewDataLoading } =
    useSlpIdViewQuery(slpData.id, slpData?.organization_id, filter, {
      enabled: !!slpData?.id,
    });
  const { paySchedules: payHour, isLoading: SlpPayScheduleLoading } =
    usePayScheduleHook({
      slp: slpData,
    });

  const queryClient = useQueryClient();

  // const  payHour= []÷
  const { data: GIapi } = useGetInvoicesByUserQuery({
    id: slpData?.id,
    filter: { org_id: slpData?.organization_id },
  }) as any;

  const payPeriodFunc = () => {
    const rawInvoiceData = GIapi?.filter((item: any) => item.status !== 'Void');

    const calculatedTotalhours =
      rawInvoiceData?.length && filterAndCalculateTotalHours(rawInvoiceData);
    const invoicesData = payHour?.slice(-9)?.map((session: any) => {
      const totalHoursForPeriod =
        rawInvoiceData?.length && calculatedTotalhours[session.pay_period_raw];
      return {
        // ...session,
        ax: totalHoursForPeriod
          ?.filter(
            (item: any) => String(item?.session_type).toLowerCase() === 'ax'
          )
          ?.reduce(
            (sum: any, invoice: any) =>
              sum + convertMinuteToHours(invoice.total_hours),
            0
          ),
        tx: totalHoursForPeriod?.reduce(
          (sum: any, invoice: any) =>
            sum + convertMinuteToHours(invoice.total_hours),
          0
        ),
        target_hr: slpData?.target_hr_per_month / 2,

        // tx: totalHoursForPeriod
        //   ?.filter(
        //     (item: any) => String(item?.session_type).toLowerCase() === 'tx'
        //   )
        //   ?.reduce((sum: any, invoice: any) => sum + invoice.total_hours, 0),
        id: session?.id,
        date: session?.pay_period,
      };
    });

    return invoicesData;
  };

  const updatedData = payPeriodFunc();

  const handleChange = (e: any) => {
    const value = e.target.value;
    if (isNaN(value)) {
      return;
    }
    setPayload(value);
  };

  const order = [
    'Ax Only',
    '1-2 Sessions',
    '3-5 Sessions',
    '6-10 Sessions',
    '11-15 Sessions',
    '16+ Sessions',
  ];

  // console.log('data is ', data);

  const convertedArray =
    data?.[0] &&
    Object.entries(data?.[0]?.client_session_categories_data)?.map(
      ([label, value]) => ({
        label,
        value,
      })
    );

  const sortedArray = convertedArray?.sort((a: any, b: any) => {
    return order.indexOf(a.label) - order.indexOf(b.label);
  });

  // const updatedData = data?.[0]?.invoice_date_ranges.map((item: any) => ({
  //   ...item,
  //   // date_period: item.date_period.replace(/\s+/g, ' ').trim(),
  //   session_types: {
  //     Ax: item.session_types.Ax || 0,
  //     Tx: item.session_types.Tx || 0,
  //     ...item.session_types,
  //   },
  // }));

  const handleSubmit = async () => {
    await mutateAsync({
      data: { target_hr_per_month: payload as number, timezone },
      id: slpData?.id,
    });
    await queryClient.invalidateQueries({
      queryKey: [queryKey.users.getById, slpData?.id],
    });
    onClose();
    timezoneDisclosure.onClose();
  };

  const getRepeatRateForSlp = () => {
    const seen = new Set();
    const uniqueArray = SlpIdViewData?.data?.filter((obj: any) => {
      const fullName = `${obj.first_name} ${obj.last_name}`;
      if (seen.has(fullName)) {
        return false;
      } else {
        seen.add(fullName);
        return true;
      }
    });

    const repeatRate =
      (
        (uniqueArray?.filter((client: any) => client.total_sessions > 1)
          ?.length *
          100) /
        uniqueArray?.length
      )?.toFixed(1) + '%';
    const derivative = `(${uniqueArray?.filter((client: any) => client.total_sessions > 1)?.length}/${uniqueArray?.length})`;

    return `${repeatRate} ${derivative}`;
  };

  const paySchedules = data?.[0]?.recent_pay_schedules?.map((item: any) => ({
    ...item,
    target_hr: slpData?.target_hr_per_month / 2,
  }));

  // useEffect(() => {
  //   const fetchSlpData = async () => {
  //     const data = await SVapi(slpData?.id);
  //     return setSlpIdData(data);
  //   };

  //   if (slpData?.id) {
  //     fetchSlpData();
  //   }

  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [slpData?.id]);

  return {
    isLoading:
      UpdatetargetLoading || SlpPayScheduleLoading || slpIdViewDataLoading,
    repeatRate: getRepeatRateForSlp(),
    targetHour: slpData?.target_hr_per_month,
    slpTimezone: slpData?.timezone,
    totalClients: data?.[0]?.total_clients_count,
    activeClients: data?.[0]?.active_clients_count,
    provinceData: data?.[0]?.province_counts_data,
    paySchedules,
    payHour,
    updatedData,
    numberOfSessions: sortedArray,
    payload,
    isOpen: open,
    isPending: InvoiceChartLoading,
    timezoneDisclosure,
    repeatDisclosure,
    timezone,
    SlpIdViewData,
    RPCData,
    filter,
    onClose,
    onOpen,
    handleChange,
    handleSubmit,
    setTimezone,
    setFilter,
  };
};
