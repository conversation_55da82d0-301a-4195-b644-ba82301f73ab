import {
  useGetInvoicesByUserQuery,
  TFilter,
} from '@/api/invoices/find-by-user';
import { useGetInvoiceSummaryQuery } from '@/api/invoices/get-summary';
import { IUser } from '@/shared/interface/user';
import { useState } from 'react';

interface PaginationMetadata {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export const useSlpInvoiceHook = ({ slp }: { slp: IUser }) => {
  const [filter, setFilter] = useState<TFilter>({
    date_to: '',
    date_from: '',
    invoice_no: undefined,
    status: '',
    limit: 50,
    page: 1,
    client_id: undefined,
    org_id: slp?.organization_id,
  });

  const {
    data: invoiceResponse,
    isLoading: invoicesLoading,
    refetch: refetchInvoices,
  } = useGetInvoicesByUserQuery({
    id: Number(slp?.id),
    filter: {
      ...filter,
      org_id: slp?.organization_id,
    },
  });

  // Extract data and pagination from response
  // Handle both old format (array) and new format (object with data and pagination)
  const GIapi = Array.isArray(invoiceResponse)
    ? invoiceResponse
    : invoiceResponse?.data || [];

  const pagination: PaginationMetadata | null = Array.isArray(invoiceResponse)
    ? null
    : invoiceResponse?.pagination || null;

  const {
    data: summaryData,
    isLoading: summaryLoading,
    refetch: refetchSummary,
  } = useGetInvoiceSummaryQuery({
    organization_id: slp?.organization_id,
    slp_id: Number(slp?.id),
  });

  const updateFilter = (
    keys: keyof TFilter | (keyof TFilter)[],
    values: any | any[]
  ) => {
    if (Array.isArray(keys) && Array.isArray(values)) {
      setFilter((prev) => {
        const updatedFilter = { ...prev };
        keys.forEach((key, index) => {
          updatedFilter[key] = values[index];
        });
        return updatedFilter;
      });
      return;
    }
    const key = keys as keyof TFilter;
    const value = values;
    setFilter((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const refetch = async () => {
    await Promise.all([refetchInvoices(), refetchSummary()]);
  };

  return {
    isLoading: invoicesLoading,
    isSummaryLoading: summaryLoading,
    GIapi,
    pagination,
    summaryData,
    refetch,
    filter,
    updateFilter,
  };
};
