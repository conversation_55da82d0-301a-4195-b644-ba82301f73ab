// import { useInvoiceFlow } from './useInvoiceFlow';
// Imports: External Libraries
import { useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { usePathname } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

// Imports: Application-Specific
// import { useUpdateBookingMutation } from '@/api/bookings/update-booking';
import { useCreateAndSendInvoiceMutation } from '@/api/invoices/create-and-send-invoice';
import { useUpdateInvoiceMutation } from '@/api/invoices/update-invoice';
import { useGetPurchasedPackagesQuery } from '@/api/packages/get-purchased-packages';
import { useCreateRedeemSessionMutation } from '@/api/redeem-session/redeem-session';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
// import supabase from '@/lib/supabase/client';
import { getPrimaryEmail } from '@/utils/helper';
import { useFormik } from 'formik';
import moment from 'moment';
import { TCreateInvoiceHook } from '../useCreateInvoiceHook';
import { useUpdateSlpNoteMutation } from '@/api/slp_notes/update-slp-note';

type useCreateSlpNoteType = {
  abbr?: any;
  booking: any;
  soapNoteHook: TCreateInvoiceHook | undefined;
};
export const useInvoiceHook = ({
  booking,
  soapNoteHook,
}: useCreateSlpNoteType) => {
  //=========== Initializations & State Management ===========

  // console.log('booking is ', booking);
  // const invoiceFlow = useInvoiceFlow();

  const queryClient = useQueryClient();
  const path = usePathname();
  const slp_id = path.split('/')[2];
  const isEdit = booking?.slp_notes?.invoice?.id;

  // console.log('booking', booking);
  const { mutateAsync: UpdateSlpNote } = useUpdateSlpNoteMutation();

  const [loading, setLoading] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<any>();

  const {
    data: ClientPurchasedPackages,
    isLoading: ClientPackagesLoading,
    // refetch: RefetchPurchasedPackages,
  } = useGetPurchasedPackagesQuery(
    Number(selectedClientId || booking?.client_id),
    {
      enabled: Boolean(Number(selectedClientId || booking?.client_id)),
    }
  );
  const { data: ServicesData } = useGetServicesQuery(booking?.organization_id, {
    enabled: Boolean(booking?.organization_id),
  });

  // console.log('services data is ', ServicesData);
  // console.log('slp id is ', slp_id);

  const { onClose, onOpen, open: isOpen } = useDisclosure();
  const {
    onClose: onCloseSendInvoice,
    onOpen: onOpenSendInvoice,
    open: isSendInoiceOpen,
  } = useDisclosure();

  // =========== Mutations ===========

  const { mutateAsync: CreateAndSendInvoice } = useCreateAndSendInvoiceMutation(
    { disableToast: true }
  );
  // const { mutateAsync: UpdateInvoice } = useUpdateInvoiceMutation();
  const {
    // mutateAsync: CreateRedeemSession,
    isLoading: CreateRedeemSessionLoading,
  } = useCreateRedeemSessionMutation();

  const { mutateAsync: updateInvoices, isLoading: updateInvoiceLoading } =
    useUpdateInvoiceMutation();
  // const { mutateAsync: updateBooking } = useUpdateBookingMutation();

  const {
    values,
    handleSubmit,
    errors,
    handleChange,
    touched,
    handleBlur,
    setFieldValue,
    setValues,
    submitCount,
  } = useFormik({
    initialValues: {
      session_count: booking?.slp_notes?.invoice?.qty || 1,
      service_id: booking?.service_id,
      is_package: booking?.slp_notes?.invoice?.purchased_package_id
        ? true
        : false,
      package: booking?.slp_notes?.invoice?.purchased_package,
      duration: 60,
    },
    onSubmit: async (values: any) => {
      // return console.log('values', values)
      // const toastStack: any[] = [];
      try {
        setLoading(true);

        const serviceDetails = ServicesData?.services?.find(
          (item: any) => item?.id === values?.service_id
        );

        const payload: any = {
          slp_id: booking?.slp_id,
          name: `${booking?.clients?.first_name} ${booking?.clients?.last_name}`,
          client_id: Number(
            selectedClientId || values?.client_id || booking?.client_id
          ),
          email: Array.isArray(booking?.email)
            ? getPrimaryEmail(booking?.email)
            : booking?.email,
          //product: values?.product || booking?.product,
          service_id: values?.service_id,
          status: values?.status || 'AWAITING_PAYMENT',
          qty: values?.session_count || 1,
          package: null,
          internal_memo: values?.internal_memo,
          memo:
            values?.referral === 'true'
              ? 'FREE REFERRAL'
              : values?.invoice_memo || '',
          organization_id:
            booking?.organization_id ||
            values?.organization_id ||
            booking?.clients?.organization_id,
          total_hours: serviceDetails?.duration_minutes || 0,
          referral: values?.referral,
          duration: serviceDetails?.duration_minutes || values?.duration || 60,

          interval: serviceDetails?.duration_minutes || 0,

          invoice_number: Number(soapNoteHook?.MaxInvoiceNumber) + 1,
          invoice_date: moment(booking?.appointment).format('YYYY-MM-DD'),
          due_date:
            values?.due_date ||
            moment(booking?.appointment).format('YYYY-MM-DD'),
          //invoice_date: moment(values?.invoice_date).format(
          //   'YYYY-MM-DD'
          // ),
          session_type: null,
          total_price: serviceDetails?.price * (values?.session_count || 1),
          package_size: null,
          package_id: null,
          soap_note_id: booking?.slp_notes?.id,
          booking_id: booking?.id,
          purchased_package_id: values?.is_package ? values?.package?.id : null,
        };

        console.log('values is ', values);
        console.log('payload is ', payload);

        //============ VALIDATIONS ============

        // const sessionTypeIsPackage = values.is_package;
        // const sessionTypeIsPackage =
        //   values.session_type?.toLowerCase() === 'package';
        // console.log('insert is ', insert);
        // console.log('values is ', values);

        // return;

        //============ VALIDATIONS ============
        // const thePackageItemSelected =
        //   values?.package?.purchased_package_items?.find(
        //     (item: any) => item?.service?.id === values?.service_id
        //   );
        // if (sessionTypeIsPackage) {
        //   if (!values?.package) {
        //     throw new Error('Please select a package');
        //   }
        //   // Check if product selected is inside the package

        //   if (
        //     !thePackageItemSelected
        //     // thePackageItemSelected?.remaining <= 0
        //   ) {
        //     throw new Error('Please select avaliable service in the package');
        //   }
        //   if (
        //     !isEdit &&
        //     thePackageItemSelected?.remaining < values?.session_count
        //   ) {
        //     throw new Error('Package balance limit reached!');
        //   }
        //   if (
        //     isEdit &&
        //     thePackageItemSelected?.quantity < values?.session_count
        //   ) {
        //     throw new Error('Package balance limit reached!');
        //   }
        // }

        // return;

        // if (!sessionTypeIsPackage) {
        //   if (!payload?.duration) {
        //     throw new Error('Duration is required.');
        //   }
        // }
        // let invoiceResponse;
        // if (isEdit) {
        //   invoiceResponse = await UpdateInvoice({
        //     data: {
        //       ...payload,
        //       booking_id: undefined,
        //       slp_id: undefined,
        //       name: undefined,
        //       client_id: undefined,
        //       email: undefined,
        //       organization_id: undefined,
        //       soap_note_id: undefined,
        //       invoice_number: undefined,
        //     },
        //     id: booking?.slp_notes?.invoice.id,
        //   });

        //   // await invoiceFlow.runInvoiceEditFlow({
        //   //   booking,
        //   //   invoiceResponse,
        //   //   thePackageItemSelected,
        //   //   payload,
        //   // });

        //   // update client activity

        //   // 1. Fetch the current details
        //   const { data: activity, error: activityError } = await supabase
        //     .from('client_activities')
        //     .select('details')
        //     .eq('details->>booking_id', booking?.id)
        //     .single();

        //   // console.log('activity--33', activity);
        //   console.log('activityError--33', activityError);

        //   if (activity) {
        //     // 2. Merge the new status into existing details
        //     const updatedDetails = {
        //       ...activity.details,
        //       status: values?.status,
        //     };

        //     // console.log('updatedDetails', updatedDetails);
        //     // console.log('activity', activity);

        //     // 3. Update only the modified details
        //     await supabase
        //       .from('client_activities')
        //       .update({ details: updatedDetails })
        //       .eq('details->>booking_id', booking?.id);

        //     // console.log('data--33', data);
        //   }
        // } else {
        const invoiceResponse = await CreateAndSendInvoice(payload);
        await UpdateSlpNote({
          data: { invoice_id: invoiceResponse?.invoice_id },
          id: booking?.slp_notes.id,
        });
        console.log('response is ', invoiceResponse);
        // return;

        // update client activity

        // 1. Fetch the current details
        // const { data: activity, error: activityError } = await supabase
        //   .from('client_activities')
        //   .select('details')
        //   .eq('details->>booking_id', booking?.id)
        //   .single();

        // // console.log('activity--33', activity);
        // console.log('activityError--33', activityError);

        // if (activity) {
        //   // 2. Merge the new status into existing details
        //   const updatedDetails = {
        //     ...activity.details,
        //     status: values?.status,
        //   };

        //   // console.log('updatedDetails', updatedDetails);
        //   // console.log('activity', activity);

        //   // 3. Update only the modified details
        //   await supabase
        //     .from('client_activities')
        //     .update({ details: updatedDetails })
        //     .eq('details->>booking_id', booking?.id);

        //   // console.log('data--33', data);
        // }

        // create client activity
        // console.log('invoice response is ', invoiceResponse);

        // if (sessionTypeIsPackage && !isEdit) {
        //   await CreateRedeemSession({
        //     purchased_package_item_id: thePackageItemSelected?.id,
        //     client_id: payload.client_id,
        //     redeemed_at: new Date(),
        //     invoice_id: invoiceResponse?.id,
        //     booking_id: booking?.id,
        //     quantity: payload?.qty,
        //   });
        //   await RefetchPurchasedPackages();
        // }
        // }
        // if (invoiceResponse?.service_id !== booking?.service_id) {
        //   await updateBooking({
        //     data: {
        //       service_id: invoiceResponse?.service_id,
        //       // event: invoiceResponse?.product,
        //     },
        //     id: booking?.id,
        //   });
        // }

        // soapNoteHook?.setBooking({
        //   ...booking,
        //   slp_notes: {
        //     ...booking?.slp_notes,
        //     invoice_id: invoiceResponse?.id,
        //     invoice: invoiceResponse,
        //   },
        // });
        // toastStack.push({
        //   type: 'success',
        //   description: isEdit ? 'Invoice Updated' : 'Invoice created',
        // });

        await queryClient.invalidateQueries({
          queryKey: [
            queryKey.bookings.getSlpBookings,
            {
              slpId: Number(slp_id),
            },
          ],
          exact: false,
        });
        queryClient.invalidateQueries([
          queryKey.client.getActivities,
          payload?.client_id,
        ]);

        await queryClient.invalidateQueries({
          queryKey: [
            queryKey.slpNotes.getByClientId,
            {
              clientId: Number(payload?.client_id),
            },
          ],
        });

        await queryClient.invalidateQueries({
          queryKey: [queryKey.bookings.getById, booking?.id],
        });

        toaster.create({
          type: 'success',
          description: 'Invoice Created',
        });

        handleCloseModal();
      } catch (error: any) {
        toaster.create({
          description: error.message || ToastMessages.somethingWrong,
          type: 'error',
        });

        setLoading(false);
      } finally {
        setLoading(false);
      }
      return values;
    },
  });

  const toggleForm = () => {
    if (isOpen) {
      return onClose();
    }
    onOpen();
  };

  const toggleSendInvoiceForm = () => {
    if (isSendInoiceOpen) {
      return onCloseSendInvoice();
    }
    onOpenSendInvoice();
  };

  const onEmailSuccess = async (updatedInvoice?: any) => {
    onCloseSendInvoice();
    if (updatedInvoice) {
      soapNoteHook?.setBooking({
        ...booking,
        slp_notes: {
          ...booking?.slp_notes,
          invoice: updatedInvoice,
        },
      });
    }
    await queryClient.invalidateQueries({
      queryKey: [
        queryKey.bookings.getSlpBookings,
        {
          // date: moment(values?.invoice_date).format('YYYY-MM-DD'),
          slpId: Number(slp_id),
        },
      ],
      exact: false,
    });

    await queryClient.invalidateQueries({
      queryKey: [
        queryKey.slpNotes.getByClientId,
        {
          clientId: Number(booking?.client_id),
        },
      ],
    });

    await queryClient.invalidateQueries({
      queryKey: [queryKey.bookings.getById, booking?.id],
    });
  };
  // console.log('values', values);

  const handleFormSubmit = (event?: React.FormEvent) => {
    if (event) {
      event.preventDefault(); // Prevent default form submission behavior
    }
    handleSubmit();
  };

  const handleCloseModal = () => {
    onClose();
  };

  const markAsSent = async () => {
    try {
      const updatedInvoice = await updateInvoices({
        data: { sent_at: new Date() },
        id: booking?.slp_notes?.invoice?.id,
      });
      onEmailSuccess(updatedInvoice);
    } catch (error) {
      toaster.create({
        type: 'success',
        description: 'Slp Note created',
      });
    }
  };
  //const allUsed = packageData?.purchased_package_items?.every(
  //   (item: any) => item.remaining === 0
  // );

  // const activePackageOptions = ClientPurchasedPackages?.filter(
  //   (item: any) =>
  //     !item?.purchased_package_items?.every((item: any) => item.remaining === 0)
  // ).map((item: any) => ({
  //   label: item?.package_offering?.name,
  //   value: item,
  // }));
  const packageItemId =
    booking?.slp_notes?.invoice?.redeemed_sessions?.[0]
      ?.purchased_package_item_id;
  const activePackageOptions = ClientPurchasedPackages?.map((item: any) => {
    const isValid =
      !item?.purchased_package_items?.every(
        (item: any) => item.remaining === 0
      ) ||
      item?.purchased_package_items?.some(
        (item: any) => item.id === packageItemId
      );
    if (isValid) {
      return {
        label: item?.package_offering?.name,
        value: item,
      };
    }
  }).filter(Boolean);

  const servicesOption = useMemo(() => {
    if (!values?.package?.id) {
      return ServicesData?.services?.map((item: any) => ({
        label: item?.name,
        value: item?.id,
      }));
    }
    const serviceIds = values?.package?.purchased_package_items.map(
      (item: any) => {
        return item?.service?.id;
      }
    );
    const services = ServicesData?.services
      ?.map((item: any) => {
        if (serviceIds?.includes(item?.id)) {
          return {
            label: item?.name,
            value: item?.id,
          };
        }
      })
      .filter(Boolean);
    return services;
  }, [
    ServicesData,
    values?.package?.id,
    values?.package?.purchased_package_items,
  ]);
  useEffect(() => {
    if (isEdit) {
      setValues({
        ...booking?.slp_notes?.invoice,
        invoice_memo: booking?.slp_notes?.invoice?.memo,
        session_count: booking?.slp_notes?.invoice?.qty,
        due_date: booking?.slp_notes?.invoice?.due_date,
        is_package: !!booking?.slp_notes?.invoice?.purchased_package_id,
        package: booking?.slp_notes?.invoice?.purchased_package,
      });
    }
  }, [booking?.id, booking?.slp_notes?.invoice, setValues, isEdit]);

  return {
    values,
    handleSubmit,
    errors,
    handleChange,
    touched,
    submitCount,
    isOpen,
    onOpen,
    toggleForm,
    handleBlur,
    setFieldValue,
    onClose,
    markAsSent,
    handleFormSubmit,
    onEmailSuccess,
    setSelectedClientId,
    selectedClientId,
    handleCloseModal,
    currentClient: booking?.clients,
    loading,
    setLoading,
    updateInvoiceLoading,
    package_id: booking?.slp_notes?.package_id,
    toggleSendInvoiceForm,
    isSendInoiceOpen,
    onOpenSendInvoice,
    onCloseSendInvoice,
    ClientPackagesLoading,
    activePackageOptions,
    servicesOption,
    ServicesData,
    CreateRedeemSessionLoading,
    isEdit,
  };
};

export type TInvoiceHook = ReturnType<typeof useInvoiceHook>;
