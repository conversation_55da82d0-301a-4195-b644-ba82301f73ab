// Imports: External Libraries
import { useCreateBookingMutation } from '@/api/bookings/create-booking';
import { useCreateSlpNoteMutation } from '@/api/slp_notes/create-slp-note';
import { useUpdateSlpNoteMutation } from '@/api/slp_notes/update-slp-note';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { getPrimaryEmail } from '@/utils/helper';
import { useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import moment from 'moment';
import { usePathname, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { TCreateInvoiceHook } from '../useCreateInvoiceHook';

type useSlpNoteType = {
  abbr?: any;
  soapNoteHook: TCreateInvoiceHook;
  booking: any;
};
export const useSlpNoteHook = ({ booking, soapNoteHook }: useSlpNoteType) => {
  //=========== Initializations & State Management ===========

  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const queryClient = useQueryClient();
  const path = usePathname();
  const slp_id = path.split('/')[2];

  const [loading, setLoading] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<any>();

  //   console.log('booking--55', booking);

  const { onClose, onOpen, open: isOpen } = useDisclosure();

  // =========== Mutations ===========
  const { mutateAsync: UpdateSlpNote, isLoading: UpdateSlpNoteLoading } =
    useUpdateSlpNoteMutation();
  const { mutateAsync: CreateSlpNote, isLoading: CreateSlpNoteLoading } =
    useCreateSlpNoteMutation();
  const { mutateAsync: CreateBooking, isLoading: CreateBookingLoading } =
    useCreateBookingMutation();

  const searchParams = useSearchParams();
  const organizationIdFromParams = searchParams.get('organization_id');

  console.log('organizationIdFromParams', organizationIdFromParams);

  const {
    values,
    handleSubmit,
    errors,
    handleChange,
    touched,
    handleBlur,
    setFieldValue,
    // setValues,
    submitCount,
  } = useFormik({
    initialValues: {
      client_id: booking?.client_id,
      note_date: booking?.appointment || new Date(),
      // note_date: booking?.slp_notes?.note_date || new Date(),
    },
    onSubmit: async (values: any) => {
      // return console.log('values', values)
      const toastStack = [];
      try {
        setLoading(true);
        const insert: any = {
          // note_date: moment(values?.note_date).format('YYYY-MM-DD'),
          // note_date: values?.note_date,
          note_date: values?.note_date || booking.appointment,
          slp_id: values?.slp_id || slp_id,
          booking_id: booking ? booking.id : undefined,
          client_id: values.client_id || booking.client_id,
          // linked_client_id: values.linked_client_id,
          status: 'Pending',
          // invoice_memo: values.invoice_memo,
          soap_note: values.soap_note,
          organization_id:
            Number(organizationIdFromParams) ||
            Number(org?.id) ||
            Number(booking?.organization_id),
          // referral: values.referral,
          // no_show: values.no_show,
          // split_ax: values.split_ax,
          // duration:
          //     values.session_type?.toLowerCase() === 'package'
          //         ? Number(values.session_count || 0) *
          //         Number(currentIncompletePackage?.session_duration)
          //         : values.duration,
          // package_id: currentIncompletePackage
          //     ? currentIncompletePackage?.id
          //     : null,
          // session_count: values.session_count,
          // organization_id: Slp?.organization_id,
        };

        // //============ CREATE BOOKING FOR COMPLETELY NEW SESSION ============

        // initially before implementing the product fucntionality we make use of the session_type for the event
        // console.log('insert >>', insert);

        if (!insert.client_id || !insert.slp_id)
          throw new Error('Something went wrong, Error: Missing Ids');

        //============ UPDATE BOOKING APPOINTMENT IF DATE CHANGES ============

        if (insert?.booking_id) {
          //============ EDIT SLP NOT IF ITS ALREADY AVAILABLE ============
          if (booking?.slp_notes?.id) {
            await UpdateSlpNote({
              data: insert,
              id: booking?.slp_notes?.id,
            });
            toastStack.push({
              type: 'success',
              description: 'Slp Note updated',
            });
            soapNoteHook.setBooking({
              ...booking,
              slp_notes: {
                ...booking?.slp_notes,
                ...insert,
              },
            });
          } else {
            const slpNote = await CreateSlpNote({
              ...insert,
              // note_date: values.note_date,
            });

            soapNoteHook.setBooking({
              ...booking,
              slp_notes: slpNote,
            });
            toastStack.push({
              type: 'success',
              description: 'Slp Note created',
            });
          }
        } else {
          const newBooking: any = { ...booking };
          delete newBooking.clients;
          delete newBooking.linked_clients;
          newBooking.appointment = values.note_date;
          newBooking.appointment_raw = moment
            .utc(values.note_date)
            .format('MM/DD/YYYY HH:mm:ss');
          newBooking.assigned_to = soapNoteHook?.Slp
            ? soapNoteHook?.Slp?.email
            : '';

          // newBooking.event = values.session_type;
          newBooking.client_id = insert.client_id;
          newBooking.email = Array.isArray(booking?.email)
            ? getPrimaryEmail(booking?.email)
            : booking?.email;
          // newBooking.organization_id = Slp?.organization_id;

          // console.log('newBooking', newBooking);
          const data = await CreateBooking(newBooking);

          // console.log('data >>>', data);

          insert.booking_id = data?.id;
          insert.client_id = newBooking.client_id;
          insert.slp_id = newBooking.slp_id;
          const slpNote = await CreateSlpNote({
            ...insert,
            // note_date: values.note_date,
          });
          // console.log('slpNote >>>', slpNote);
          soapNoteHook.setBooking({
            ...booking,
            id: data?.id,
            slp_notes: slpNote?.data[0],
          });
          toastStack.push({
            type: 'success',
            description: 'Slp Note created',
          });
        }
        await queryClient.invalidateQueries({
          queryKey: [
            queryKey.bookings.getSlpBookings,
            {
              date: moment(values?.invoice_date).format('YYYY-MM-DD'),
              slpId: Number(slp_id),
            },
          ],
          exact: false,
        });
        await queryClient.invalidateQueries({
          queryKey: [
            queryKey.slpNotes.getByClientId,
            {
              clientId: Number(insert?.client_id),
            },
          ],
        });

        await queryClient.invalidateQueries({
          queryKey: [queryKey.bookings.getById, insert.booking_id],
        });
        if (toastStack.length) {
          toastStack.forEach((toast) => {
            toaster.create(toast);
          });
        }
        handleCloseModal();
      } catch (error: any) {
        toaster.create({
          description: error.message || ToastMessages.somethingWrong,
          type: 'error',
        });
        setLoading(false);
      } finally {
        // setSubmitLoading(false);
        setLoading(false);
      }
      return values;
    },
  });

  const toggleForm = () => {
    if (isOpen) {
      return onClose();
    }
    onOpen();
  };

  // console.log('values', values);

  const handleFormSubmit = (event?: React.FormEvent) => {
    if (event) {
      event.preventDefault(); // Prevent default form submission behavior
    }
    handleSubmit();
  };

  const handleCloseModal = () => {
    onClose();
  };

  return {
    values,
    handleSubmit,
    errors,
    handleChange,
    touched,
    submitCount,
    isOpen,
    onOpen,
    toggleForm,
    handleBlur,
    setFieldValue,
    onClose,
    handleFormSubmit,
    UpdateSlpNoteLoading,
    setSelectedClientId,
    selectedClientId,
    handleCloseModal,
    currentClient: booking?.clients,
    CreateSlpNoteLoading,
    CreateBookingLoading,
    loading,
    setLoading,
    package_id: booking?.slp_notes?.package_id,
  };
};

export type TSlpNoteHook = ReturnType<typeof useSlpNoteHook>;
