/* eslint-disable react-hooks/exhaustive-deps */
import { useGetInvoicesByUserQuery } from '@/api/invoices/find-by-user';
import { useGetPayScheduleBySlpIdQuery } from '@/api/pay_schedules/get-by-slp-id';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import { ToastMessages } from '@/constants/toast-messages';
import supabase from '@/lib/supabase/client';
import { IUser } from '@/shared/interface/user';
import {
  calculateTotalHoursBySLP,
  convertDate,
  filterAndCalculateTotalHours,
} from '@/utils/invoiceUtils';
import { useEffect, useState } from 'react';

export const usePayScheduleHook = ({ slp }: { slp: IUser }) => {
  const [paySchedules, setPaySchedules] = useState<any>([]);
  const [availableYears, setAvailableYears] = useState<any>([]);
  const [bgUpdateloading, setBgUpdateLoading] = useState<any>(false);
  const [selectedYear, setSelectedYear] = useState();
  const { data: AIapi, isSuccess: GetInvoiceSuccess } =
    useGetInvoicesByUserQuery({
      id: slp?.id,
      filter: { org_id: slp?.organization_id },
    }) as any;

  console.log('AIapi is ', AIapi);

  const [stats, setStats] = useState<any>({
    totalHours: 0,
    totalPay: 0,
    totalBasePay: 0,
    total_ax_pay: 0,
    total_assessments: 0,
  });

  const {
    data: SlpPaySchedules,
    isLoading: SlpPayScheduleLoading,
    isSuccess: SlpPayScheduleSuccess,
    refetch: RefetchPaySchedule,
  } = useGetPayScheduleBySlpIdQuery({
    slpId: slp?.id,
    year: selectedYear,
    org_id: slp?.organization_id,
  });

  const fetchPaySchedules = async () => {
    if (!slp) {
      return;
    }
    let totalHours = 0;
    let totalPay = 0;
    let totalBasePay = 0;
    let total_ax_pay = 0;
    let total_assessments = 0;

    const data = SlpPaySchedules;
    let uniqueYears: any = [];
    if (!availableYears.length) {
      // const data = SlpPaySchedules;
      uniqueYears = Array.from(
        new Set(
          data
            .filter((item: any) => {
              if (item.slp_id === slp.id && item.total_hours > 0) {
                return true;
              }
            })
            .map((item: any) => {
              return item.year;
            })
        )
      );

      uniqueYears.sort((a: any, b: any) => b - a);
      setAvailableYears(uniqueYears);
    }

    const currentYearData = SlpPaySchedules.filter(
      (item: any) =>
        String(item.year) ===
        (selectedYear
          ? String(selectedYear)
          : uniqueYears?.length > 0
            ? String(uniqueYears[0])
            : String(new Date().getFullYear()))
    );

    currentYearData.forEach((item: any) => {
      totalHours += item.total_hours;
      totalBasePay += item.base_pay;
      total_ax_pay += item.ax_pay;
      total_assessments += item.assesments;
      totalPay += item.total_pay;
    });
    setStats({
      totalHours,
      totalPay,
      totalBasePay,
      total_ax_pay,
      total_assessments,
    });

    const sortedData = currentYearData.sort((a: any, b: any) => {
      // Parsing the dates
      const dateA = new Date(a.pay_period_raw);
      const dateB = new Date(b.pay_period_raw);

      // Comparing the dates
      if (dateA < dateB) {
        return -1;
      }
      if (dateA > dateB) {
        return 1;
      }
      return 0;
    });
    setPaySchedules(sortedData);
  };
  const updatePaySchedule = async (slp_id: number) => {
    try {
      setBgUpdateLoading(true);
      if (!slp || !AIapi) {
        return;
      }

      const allExistingPaySchedule = SlpPaySchedules.filter(
        (item: any) =>
          String(item.year) ===
          (selectedYear
            ? String(selectedYear)
            : String(new Date().getFullYear()))
      );
      const pay_rate = Number(slp.pay_rate);
      const arr: Array<any> = [];
      const dataInvocies = AIapi;
      const data = dataInvocies?.filter(
        (item: any) =>
          item.status !== 'Void' && item?.organizations?.plan === 'TEAM'
      );
      console.log('All invoices are', data);
      const calculatedTotalhours = filterAndCalculateTotalHours(data);

      Object.keys(calculatedTotalhours).map((groupKey) => {
        const wholeDataInTheGroup = calculatedTotalhours[groupKey];
        const allAxInvoies = wholeDataInTheGroup?.filter(
          (item: any) => item.session_type === 'Ax'
        );
        const total_hours = calculateTotalHoursBySLP(wholeDataInTheGroup);
        const total_hours_for_Ax = calculateTotalHoursBySLP(allAxInvoies);
        const pay_period = convertDate(groupKey);
        const base_pay = total_hours * pay_rate;
        const ax_pay = parseFloat(
          (total_hours_for_Ax * pay_rate * 0.2).toFixed(2)
        );
        const axPayAvailable = new Date(groupKey) > new Date('2024-04-15');
        arr.push({
          slp_id,
          pay_rate,
          total_hours,
          pay_period: pay_period.date,
          year: pay_period.year,
          base_pay,
          pay_period_raw: groupKey,
          ax_pay: axPayAvailable ? ax_pay : 0,
          assesments: axPayAvailable ? total_hours_for_Ax : 0,
          total_pay: axPayAvailable ? base_pay + ax_pay : base_pay,
          organization_id: slp.organization_id,
          total_hours_for_Ax: axPayAvailable ? total_hours_for_Ax : 0,
          // status:"paid"
        });
      });

      // console.log('arr is ', arr);
      // console.log('allExistingPaySchedule is ', allExistingPaySchedule);

      const newToBeUpsert = arr
        .filter(
          (item) =>
            Number(item.year) ===
            Number(
              selectedYear
                ? String(selectedYear)
                : String(new Date().getFullYear())
            )
        )
        .map((item) => {
          // console.log('item is ', item);

          const existingItem = allExistingPaySchedule?.find(
            (alIt: any) => alIt.pay_period_raw === item.pay_period_raw
          );
          // console.log("existing item is ", existingItem);

          // let payload = item;

          //PAY SCHEDULE ALREADY EXIST
          if (existingItem) {
            if (existingItem.is_ax_enabled) {
              const ax_pay = parseFloat(
                (
                  item?.total_hours_for_Ax *
                  existingItem.pay_rate *
                  0.2
                ).toFixed(2)
              );
              delete item.total_hours_for_Ax;
              return {
                ...item,
                is_ax_enabled: true,
                id: existingItem.id,
                pay_rate: existingItem.pay_rate,
                base_pay: existingItem.pay_rate * existingItem.total_hours,
                total_pay:
                  existingItem.pay_rate * existingItem.total_hours + ax_pay,
              };
            } else {
              delete item.total_hours_for_Ax;

              return {
                ...item,
                id: existingItem.id,
                pay_rate: existingItem.pay_rate,
                ax_pay: 0,
                assesments: 0,
                base_pay: existingItem.pay_rate * existingItem.total_hours,
                total_pay: existingItem.pay_rate * existingItem.total_hours,
              };
            }
          } else {
            delete item.total_hours_for_Ax;

            if (slp.ax_comp_enabled) {
              return {
                ...item,
                is_ax_enabled: true,
              };
            } else {
              return {
                ...item,
                ax_pay: 0,
                assesments: 0,
                total_pay: item.base_pay,
                is_ax_enabled: false,
              };
            }
            // return {
            //   ...item,
            //   is_ax_enabled: slp.ax_comp_enabled,
            // };
          }

          // return existingItem
          //   ? {
          //       ...item,
          //       id: existingItem.id,
          //       pay_rate: existingItem.pay_rate,

          //       // ax_pay: existingItem.ax_pay,
          //       // assesments: existingItem.assesments,
          //       // total_pay: existingItem.total_pay,
          //     }
          //   : item;
        });
      // console.log('new to be inserted is ', newToBeUpsert);
      // console.log('i am UPDATING ===', slp.id);

      await supabase
        .from(tableNames.pay_schedules)
        .upsert(newToBeUpsert, { onConflict: 'id' });
      await RefetchPaySchedule();
      await fetchPaySchedules();
    } catch (error) {
      console.log('error occured when updating payschedules ', error);
    } finally {
      setBgUpdateLoading(false);
    }
  };

  const init = async () => {
    try {
      // await updatePaySchedule(slp.id);
      await fetchPaySchedules();
    } catch (error: any) {
      toaster.create({
        description: error.message || ToastMessages.somethingWrong,
        type: 'error',
      });
    }
  };
  useEffect(() => {
    if (!SlpPaySchedules) return;
    init();
  }, [selectedYear, SlpPaySchedules]);

  useEffect(() => {
    if (GetInvoiceSuccess && SlpPayScheduleSuccess) {
      updatePaySchedule(slp.id);
    }
  }, [AIapi, SlpPaySchedules]);

  // const uniqueData = paySchedules?.reduce((acc: any, curr: any) => {
  //   const existingItem = acc.find(
  //     (item: any) => item.pay_period === curr.pay_period
  //   );
  //   if (!existingItem) {
  //     acc.push(curr);
  //   }
  //   return acc;
  // }, []);

  return {
    paySchedules: paySchedules,
    selectedYear,
    availableYearsOptions:
      availableYears.map((item: any) => ({ label: item, value: item })) || [],
    isLoading: SlpPayScheduleLoading,
    stats,
    setSelectedYear,
    updatePaySchedule,
    bgUpdateloading,
  };
};
