/* eslint-disable react-hooks/exhaustive-deps */
import { useGetInvoicesByUserQuery } from '@/api/invoices/find-by-user';
import { PayScheduleDisplay } from '@/app/(dashboard)/slp/[id]/payschedules/columnDef';
import { filterAndCalculateTotalHours } from '@/utils/invoiceUtils';
import { useEffect, useState } from 'react';
import { ToastMessages } from '@/constants/toast-messages';
import moment from 'moment';
import { convertMinuteToHours } from '@/utils/date-formatter';
import { toaster } from '@/components/ui/toaster';
import { IUser } from '@/shared/interface/user';

export const useSlpInvoiceModal = ({
  slp,
  data,
  isOpen,
}: {
  slp: IUser;
  data: PayScheduleDisplay;
  isOpen: any;
}) => {
  const { data: GIapi, isLoading } = useGetInvoicesByUserQuery(
    { id: slp.id, filter: { org_id: slp?.organization_id } },
    {
      enabled: isOpen,
    }
  ) as any;
  const [totalHoursSum, setTotalHoursSum] = useState(0);
  const [totalAxHours, setTotalAxHours] = useState(0);
  const [invoices, setInvoices] = useState([]);
  // console.log('data  from modal is is', GIapi);

  const init = async () => {
    try {
      const rawInvoiceData = GIapi?.filter(
        (item: any) =>
          item.status !== 'Void' && item?.organizations?.plan === 'TEAM'
      );
      // console.log('rawInvoiceData is ', rawInvoiceData);

      const calculatedTotalhours = filterAndCalculateTotalHours(rawInvoiceData);
      const invoicesData = calculatedTotalhours[data.pay_period_raw];
      // console.log('data is ', data);
      // console.log('calculatedTotalhours is ', calculatedTotalhours);
      // console.log('invoicesData  is ', invoicesData);

      setInvoices(invoicesData);
      setTotalHoursSum(
        invoicesData?.reduce(
          (sum: any, invoice: any) =>
            sum + convertMinuteToHours(invoice.total_hours),
          0
        )
      );
      setTotalAxHours(
        invoicesData
          ?.filter(
            (item: any) => String(item?.session_type).toLowerCase() === 'ax'
          )
          ?.reduce(
            (sum: any, invoice: any) =>
              sum + convertMinuteToHours(invoice.total_hours),
            0
          )
      );
    } catch (error: any) {
      toaster.create({
        description: error.message || ToastMessages.somethingWrong,
        type: 'error',
      });
    }
  };

  useEffect(() => {
    if (isLoading) return;
    init();
  }, [GIapi]);
  // console.log('invoices is ', invoices);

  const sortInvoices = invoices?.sort((a: any, b: any) => {
    const dateA = moment(a?.invoice_date).startOf('day');
    const dateB = moment(b?.invoice_date).startOf('day');
    return dateB.valueOf() - dateA.valueOf();
  });

  return {
    isLoading,
    GIapi,
    totalHoursSum,
    totalAxHours,
    // invoices: invoices?.sort(
    //   (a: any, b: any) =>
    //     Number(new Date(b?.invoice_date_raw).getDate()) -
    //     Number(new Date(a?.invoice_date_raw).getDate())
    // ),
    invoices: sortInvoices,
  };
};
