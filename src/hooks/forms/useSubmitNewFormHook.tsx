import {
  useCreateAnswersMutation,
  useCreateClientActivitiesMutation,
  useCreateClientEmailsMutation,
  useCreateClientMutation,
  useUpdateAnswersMutation,
} from '@/api/forms/upload-form-answers';
import { useAddNotableDateApi } from '@/api/notable_date/add-notable-date';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { findExistingClientByInitialEmail } from '@/utils/db';
import { useFormik } from 'formik';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import * as Yup from 'yup';

// Define proper types for better type safety
interface Question {
  id: string;
  page: number;
  type: string;
  qt: string;
  required: boolean;
  default?: string;
  notableDate?: boolean;
  event?: string;
  other?: string;
}

interface FormDetails {
  id: string;
  title: string;
  organization_id: string;
  organization_name: string;
  slug: string;
  questions: Question[];
}

const useMultiPageFormHook = ({
  formDetails,
}: {
  formDetails: FormDetails;
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [submissionId, setSubmissionId] = useState<string | null>(null);
  const [submittedPages, setSubmittedPages] = useState<Set<number>>(new Set());
  const router = useRouter();

  const { mutateAsync: createAnswers } = useCreateAnswersMutation();
  const { mutateAsync: updateAnswers } = useUpdateAnswersMutation();
  const { mutateAsync: createClientEmails } = useCreateClientEmailsMutation();
  const { mutateAsync: createClientActivities } =
    useCreateClientActivitiesMutation();
  const { mutateAsync: createClient } = useCreateClientMutation();
  const { mutateAsync: createNotableDateAsync } = useAddNotableDateApi();

  // Get unique pages from questions - now properly typed
  const pages = useMemo(() => {
    const pageNumbers = [
      ...new Set(formDetails.questions.map((q: Question) => q.page)),
    ].sort((a: number, b: number) => a - b);
    return pageNumbers;
  }, [formDetails.questions]);

  // Get questions for current page
  const currentPageQuestions = useMemo(() => {
    return formDetails.questions.filter(
      (q: Question) => q.page === currentPage
    );
  }, [formDetails.questions, currentPage]);

  // Create initial values for current page only
  const initialValues = useMemo(() => {
    const values: any = {};
    currentPageQuestions.forEach((question: Question) => {
      values[`question_${question.id}`] =
        question.type === 'Multiple choice' ? [] : '';
    });
    return values;
  }, [currentPageQuestions]);

  // Create validation schema for current page only
  const validationSchema = useMemo(() => {
    const schema: any = {};
    currentPageQuestions.forEach((question: Question) => {
      // Handle default email validation (usually on first page)
      if (question.default === 'true') {
        schema[`question_${question.id}`] = Yup.string()
          .email('Invalid email format')
          .required('Email is required');
      }
      // Handle other required fields
      else if (question.required) {
        if (question.type === 'Multiple choice') {
          schema[`question_${question.id}`] = Yup.array()
            .min(1, 'At least one option must be selected')
            .required('This field is required');
        } else if (question.type === 'Date') {
          schema[`question_${question.id}`] = Yup.date()
            .typeError('Invalid date format')
            .required('This field is required');
        } else if (question.type === 'Number') {
          schema[`question_${question.id}`] = Yup.number()
            .typeError('Must be a number')
            .required('This field is required');
        } else {
          schema[`question_${question.id}`] = Yup.string()
            .trim()
            .required('This field is required');
        }
      }
    });
    return Yup.object().shape(schema);
  }, [currentPageQuestions]);

  const determineEvent = (values: any) => {
    if (values?.event == 'OTHER') {
      return values?.other;
    }
    return values?.event;
  };

  const handleClientActivity = async (
    data: any,
    answerDetails: any,
    method: 'update' | 'insert'
  ) => {
    console.log('data', data);
    console.log('answerDetails--333', answerDetails);
    if (data || method === 'update') {
      console.log('method', method);
      const client_id = data?.id;
      const activity_payload = {
        client_id: client_id,
        activity_type: 'form_submitted',
        activity_date: new Date().toISOString(),
        details: {
          form_id: formDetails?.id,
          form_name: formDetails?.title,
          answer_details: answerDetails,
          page: currentPage,
        },
        organization_id: formDetails?.organization_id || data?.organization_id,
      };

      if (method === 'insert') {
        // For first page submission - INSERT new activity
        const { error: activityUploadError, data: activityUploadData } =
          await supabase
            .from(tableNames.client_activities)
            .insert(activity_payload)
            .select();

        console.log('activityUploadError', activityUploadError);
        console.log('activityUploadData', activityUploadData);

        if (activityUploadError) {
          throw activityUploadError;
        }

        // Store the activity ID for future updates
        if (activityUploadData && activityUploadData.length > 0) {
          localStorage.setItem(
            'activityId',
            activityUploadData[0]?.id?.toString()
          );
        }
      } else if (method === 'update') {
        console.log('update flow');

        const activityId = localStorage.getItem('activityId');
        console.log('activityId', activityId);
        // For subsequent pages - UPDATE existing activity using stored ID

        if (activityId) {
          const { error: updateError, data: updateData } = await supabase
            .from(tableNames.client_activities)
            .update({
              activity_date: new Date().toISOString(),
              details: {
                form_id: formDetails?.id,
                form_name: formDetails?.title,
                answer_details: answerDetails[0]?.answer_details,
                page: currentPage,
              },
            })
            .eq('id', Number(activityId))
            .select();

          console.log('activityUpdateError', updateError);
          console.log('updateData', updateData);

          if (updateError) {
            throw updateError;
          }
        }
      }
    }
  };

  const handleNotableDates = async (values: any, clientData: any) => {
    const notableDateQuestions = currentPageQuestions
      ?.map((question: Question) => {
        if (question?.type === 'Date' && question?.notableDate) {
          return {
            ...question,
            answer: values[`question_${question.id}`],
          };
        }
        return null;
      })
      .filter(Boolean);

    if (notableDateQuestions?.length && clientData) {
      const promises = notableDateQuestions?.map((question: any) => {
        return createNotableDateAsync({
          event: determineEvent({
            event: question?.event,
            other: question?.other,
          }),
          date: question?.answer,
          client_id: clientData?.id,
          organization_id: clientData?.organization_id,
        });
      });
      await Promise.all(promises);
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    validateOnBlur: true,
    validateOnChange: true,
    enableReinitialize: true, // Important for page changes

    onSubmit: async (values) => {
      try {
        setIsLoading(true);

        console.log('values', values);

        // Find the email question (where default is "true") - typically on page 1
        const emailQuestion = formDetails.questions.find(
          (q: Question) => q.default === 'true'
        );

        let email = '';
        if (emailQuestion) {
          // If email question is on current page
          if (emailQuestion.page === currentPage) {
            email = values[`question_${emailQuestion.id}`];
          } else {
            // If email question is on a different page, we need to handle this
            // For now, we'll skip email lookup for subsequent pages
            email = '';
          }
        }

        // Find existing client only if we have an email (usually page 1)
        const clientData = email
          ? await findExistingClientByInitialEmail(email)
          : null;

        console.log('clientData', clientData);
        // create new client if not found
        if (!clientData && currentPage === 1) {
          const insert = {
            initial_email: email?.toLowerCase() || null,
            organization_id: formDetails?.organization_id,
          };

          const CreatedClientData = await createClient(insert);

          console.log('data----555', CreatedClientData);

          if (CreatedClientData) {
            // create client emails
            const insertClientEmail = {
              client_id: CreatedClientData[0]?.id,
              email: CreatedClientData[0].initial_email,
              organization_id: CreatedClientData[0]?.organization_id,
            };
            const createdClientEmail =
              await createClientEmails(insertClientEmail);

            if (!createdClientEmail) {
              toaster.create({
                description: 'Something went wrong.',
                type: 'error',
              });
              throw Error('Error creating client email');
            }
            // create client activity
            const activitiesPayload = {
              client_id: CreatedClientData[0]?.id,
              activity_type: 'client_created',
              activity_date: new Date().toISOString(),
              details: {
                created_by: 'manual',
              },
              organization_id: CreatedClientData[0]?.organization_id,
            };
            const createActivityData =
              await createClientActivities(activitiesPayload);
            if (!createActivityData) {
              toaster.create({
                description: 'Something went wrong.',
                type: 'error',
              });
              throw Error('Error creating client activity');
            }
          }
        }

        // Create answer details for current page questions only
        const answer_details =
          currentPageQuestions?.map((question: Question) => ({
            id: question.id,
            qt: question.qt,
            show_answers: 'true',
            page: question.page,
            ans:
              question.type === 'Multiple choice'
                ? values[`question_${question.id}`]
                : values[`question_${question.id}`] || '',
          })) || [];

        // First page submission (INSERT)
        if (currentPage === 1) {
          const insertData = {
            form_id: formDetails?.id,
            client_id: clientData ? clientData?.id : null,
            form_title: formDetails?.title,
            organization_id: formDetails?.organization_id,
            answer_details,
            first_name: clientData ? clientData?.first_name : '',
            last_name: clientData ? clientData?.last_name : '',
            email: clientData ? clientData?.initial_email : email,
            current_page: currentPage,
            is_complete: false,
          };

          const result = await createAnswers(insertData);
          // console.log('result', result);
          setSubmissionId(result[0]?.id); // Store the submission ID for future updates

          // Handle client activity
          await handleClientActivity(clientData, answer_details, 'insert');
        }
        // Subsequent pages (UPDATE)
        else {
          if (!submissionId) {
            throw new Error(
              'No submission ID found. Please start from page 1.'
            );
          }

          const updateData = {
            id: submissionId,
            answer_details, // This will be merged with existing answers
            current_page: currentPage,
            is_complete: currentPage === Math.max(...pages), // Mark complete on last page
          };

          const updatedRes = await updateAnswers(updateData);

          console.log('updatedRes', updatedRes);

          // Handle client activity
          await handleClientActivity(clientData, updatedRes, 'update');
        }

        // Handle notable dates
        await handleNotableDates(values, clientData);

        // Mark current page as submitted
        setSubmittedPages((prev) => new Set([...prev, currentPage]));

        // Show success message
        toaster.create({
          description: `Page ${currentPage} submitted successfully`,
          type: 'success',
        });

        // Navigate to next page or success page
        if (currentPage < Math.max(...pages)) {
          setCurrentPage(currentPage + 1);
          formik.resetForm(); // Reset form for next page
        } else {
          // Final page submitted, redirect to success page
          router.push(
            `/form/${formDetails?.organization_name}/${formDetails?.slug}/success`
          );
        }
      } catch (error) {
        console.error('Error submitting form page:', error);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
      } finally {
        setIsLoading(false);
      }
    },
  });

  // console.log('submissionId', submissionId);

  const goToPage = (pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= Math.max(...pages)) {
      setCurrentPage(pageNumber);
      formik.resetForm();
    }
  };

  const goToNextPage = () => {
    if (currentPage < Math.max(...pages)) {
      setCurrentPage(currentPage + 1);
      formik.resetForm();
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
      formik.resetForm();
    }
  };

  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === Math.max(...pages);
  const isPageSubmitted = submittedPages.has(currentPage);

  return {
    formik,
    isLoading,
    currentPage,
    pages,
    currentPageQuestions,
    isFirstPage,
    isLastPage,
    isPageSubmitted,
    submissionId,
    goToPage,
    goToNextPage,
    goToPreviousPage,
  };
};

export default useMultiPageFormHook;
