import { useAddTodoApi } from '@/api/todo/add-todo';
import { useDeleteTodoApi } from '@/api/todo/delete-todo';
import { useGetTodosQuery } from '@/api/todo/get-todo';
import { useUpdateTodoApi } from '@/api/todo/update-todo';
import { useDisclosure } from '@chakra-ui/react';
import { useState } from 'react';
import { useSupabaseSession } from '../auth/useUserSession';

export const useTodo = (id?: number) => {
  const { open, onClose, onOpen } = useDisclosure();
  const [sort, setSort] = useState('desc');
  const [hidden, setHidden] = useState(false);
  const [form, setForm] = useState({
    task: '',
    date: new Date().toISOString(),
    client_id: null,
    client_name: '',
  });
  const { UserFromQuery } = useSupabaseSession();
  const { data, refetch } = useGetTodosQuery({
    sort,
    id: id,
    user_id: UserFromQuery?.id,
  }) as any;
  const { mutateAsync, isLoading } = useAddTodoApi();
  const { mutateAsync: deleteMutateAsync } = useDeleteTodoApi();
  const { mutateAsync: updateMutateAsync } = useUpdateTodoApi();

  const handleAddTodo = async (e: any) => {
    if ((e?.key && e.key !== 'Enter') || e !== 'submit') return;
    if (!form.task) return;
    await mutateAsync({ ...form, user_id: id });
    refetch();
    setForm({
      task: '',
      date: new Date().toISOString(),
      client_id: null,
      client_name: '',
    });
    onClose();
  };

  const handleDeleteTodo = async (deleteId: any) => {
    await deleteMutateAsync(deleteId);
  };

  const handleUpdateTodo = async (body: any) => {
    await updateMutateAsync({
      ...body,
      clients: undefined,
      status: body?.status === 'undone' ? 'done' : 'undone',
    });
  };

  const showData: any[] = data
    ? data?.filter((item: any) => item.status === 'undone') || []
    : [];
  const hiddenData: any[] = data
    ? data?.filter((item: any) => item.status === 'done') || []
    : [];

  return {
    form,
    data,
    showData,
    hiddenData,
    open,
    hidden,
    sort,
    isLoading,
    setHidden,
    setSort,
    onClose,
    onOpen,
    handleAddTodo,
    setForm,
    handleDeleteTodo,
    handleUpdateTodo,
  };
};
