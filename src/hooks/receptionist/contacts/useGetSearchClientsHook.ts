import { useEffect, useState } from 'react';
// import { useGetAllClientsQuery } from '@/api/clients/get-all-clients';
import { useNewSearchClientsQuery } from '@/api/clients/new-search';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';

const dataRanking = ['Customer', 'SQL', 'Prospect'];

export const useGetSearchClientsHook = () => {
  const [searchFilter, setSearchFilter] = useState('');
  const [search, setSearch] = useState('');
  const [isQueryEnabled, setIsQueryEnabled] = useState(false);
  const { UserFromQuery } = useSupabaseSession();

  const { data: Clients, isLoading: ClientsLoading } = useNewSearchClientsQuery(
    { search_text: searchFilter, org_id: UserFromQuery?.organization_id },
    'navbar',
    {
      enabled: isQueryEnabled,
      staleTime: 5 * 60 * 1000,
      cacheTime: 60 * 60 * 1000,
    }
  );
  // const { data: Clients, isLoading: ClientsLoading } = useGetAllClientsQuery(
  //   { search: searchFilter },
  //   'navbar',
  //   {
  //     enabled: isQueryEnabled, // Query is disabled initially
  //     staleTime: 5 * 60 * 1000, // 5 minutes
  //     cacheTime: 60 * 60 * 1000, // 1 hour
  //   }
  // );

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const input = event.target.value;
    setSearch(input);
    if (!isQueryEnabled && input.trim().length > 0) {
      setIsQueryEnabled(true); // Enable query once input starts
    }
  };

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      setSearchFilter(search);
    }, 600);

    return () => clearTimeout(debounceTimer);
  }, [search]);

  const sortedClients = Clients?.data?.sort((a: any, b: any) => {
    const rankA = dataRanking.indexOf(a?.stage);
    const rankB = dataRanking.indexOf(b?.stage);

    if (rankA !== -1 && rankB !== -1) return rankA - rankB;
    if (rankA !== -1) return -1;
    if (rankB !== -1) return 1;
    return 0;
  });

  const payload = { data: sortedClients, total_count: Clients?.total_count };

  return {
    data: payload, // Return sorted clients
    isPending: ClientsLoading,
    handleInputChange,
    search,
    setSearch,
  };
};
