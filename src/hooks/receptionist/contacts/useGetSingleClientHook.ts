import { useGetClientByIdQuery } from '@/api/clients/get-client-by-id';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import { ToastMessages } from '@/constants/toast-messages';
import supabase from '@/lib/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export const useGetSingleClientHook = ({
  id,
  enabled,
}: {
  id: string;
  enabled?: boolean;
}) => {
  const queryClient = useQueryClient();
  const [setPrimaryLoading, setSetPrimaryLoading] = useState({
    email: '',
    loading: false,
  });
  const {
    data: Client,
    isLoading: ClientLoading,
    isSuccess,
    refetch,
  } = useGetClientByIdQuery(Number(id), { enabled });

  //Check if window is defined before accessing localStorage
  const raw =
    typeof window !== 'undefined' ? localStorage.getItem('UserState') : null;
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;

  const { data: AllSlp } = useGetAllSlpQuery({
    status: 'Active',
    role: 'therapist',
    organization_id: org?.id,
  });

  const updateSlp = AllSlp?.map((item: any) => ({
    label: `${item.first_name || ''} ${item.last_name || ''}`,
    value: item.id,
  }));

  const [hasMultiplePrimaryEmail, setHasMultiplePrimaryEmail] = useState(false);

  const setPrimaryEmail = async (clientId: number, email: string) => {
    try {
      setSetPrimaryLoading({ email, loading: true });
      const { data: isPrimaryEmail } = await supabase
        .from(tableNames.client_emails)
        .select('*')
        .eq('client_id', clientId)
        .eq('is_primary_email', true)
        .single();

      if (isPrimaryEmail) {
        await supabase
          .from(tableNames.client_emails)
          .update({ id: isPrimaryEmail?.id, is_primary_email: false })
          .eq('id', isPrimaryEmail?.id);
      }

      const { data, error } = await supabase
        .from(tableNames.client_emails)
        .update({ is_primary_email: true }) // Mark the selected email as primary
        .eq('client_id', clientId)
        .eq('email', email)
        .select();
      // Use eq for both conditions

      if (error) {
        toaster.create({
          description: 'Unable to update primary email',
          type: 'error',
        });
        return;
      }

      // Refetch data after updating
      if (data) {
        await refetch(); // Assuming `refetch` is defined in your component
      }

      toaster.create({
        description: ToastMessages.operationSuccess,
        type: 'success',
      });
    } catch (error) {
      toaster.create({ description: 'Something went wrong', type: 'error' });
    } finally {
      setSetPrimaryLoading({ email, loading: false });
    }
  };

  // const setPrimaryEmail = async (clientId: number, email: string) => {
  //   console.log('clientId', clientId, 'email', email);
  //   try {
  //     setSetPrimaryLoading({ email, loading: true });
  //     const { data: hasPrimary } = await supabase
  //       .from(tableNames.client_emails)
  //       .select('is_primary_email')
  //       .eq('client_id', clientId);

  //     console.log('hasPrimary', hasPrimary);
  //     const primary_email_arr = hasPrimary?.map(
  //       (item) => item.is_primary_email
  //     );
  //     const hasPrimaryEmail = primary_email_arr?.some(
  //       (value) => value === true
  //     );

  //     console.log('hasPrimaryEmail', hasPrimaryEmail);

  //     //User already have a primary email
  //     if (hasPrimaryEmail) {
  //       await supabase
  //         .from(tableNames.client_emails)
  //         .update({ is_primary_email: false })
  //         .eq('email', email);
  //       // set that one to false
  //     }
  //     const { data, error } = await supabase.rpc('update_primary_email', {
  //       id: clientId,
  //       email_id: email,
  //     });

  //     console.log('data--email', data);
  //     console.log('error', error);
  //     if (error) {
  //       toaster.create({
  //         description: 'Unable to update primary email',
  //         type: 'error',
  //       });
  //       return;
  //     }
  //     if (data === 1) {
  //       await refetch();
  //     }
  //     toaster.create({
  //       description: ToastMessages.operationSuccess,
  //       type: 'success',
  //     });
  //   } catch (error) {
  //     toaster.create({ description: 'Something went wrong', type: 'error' });
  //   } finally {
  //     setSetPrimaryLoading({ email, loading: false });
  //   }
  // };

  const removeAsPrimary = async (contactId: number, email: string) => {
    await supabase
      .from(tableNames.client_emails)
      .update({ is_primary_email: false })
      .eq('email', email)
      .eq('client_id', contactId);

    await queryClient.invalidateQueries({
      queryKey: ['get-client-by-id', Number(id)],
    });
    // refetch();
  };
  useEffect(() => {
    if (!Client?.client_emails?.length) return;
    const numberOfMultiplePrimaryEmail = Client?.client_emails?.filter(
      (item) => item.is_primary_email === true
    )?.length;
    setHasMultiplePrimaryEmail(numberOfMultiplePrimaryEmail > 1 ? true : false);
  }, [isSuccess, Client]);

  return {
    Client,
    ClientLoading,
    updateSlp,
    setPrimaryEmail,
    setPrimaryLoading,
    removeAsPrimary,
    refetch,
    hasMultiplePrimaryEmail,
  };
};
