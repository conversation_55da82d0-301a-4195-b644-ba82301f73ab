import { getSlugFromName, getSlugFromOrgName } from '@/utils/event';

export default function formTemplate(createdOrganization: any) {
  console.log('ccc---555', createdOrganization);
  const formPayload = [
    {
      description: 'To be filled out by the client',
      organization_id: createdOrganization?.id,
      organization_name: getSlugFromOrgName(createdOrganization?.name),
      title: 'New Form',
      slug: getSlugFromName('new-form'),
      questions: [
        {
          icon: 'TfiText',
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          qt: 'What is First Name',
          required: true,
          type: 'Textbox',
          heading: 'Short answer',
          page: 1,
          default: 'true',
        },
        {
          icon: 'TfiText',
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          qt: 'What is Last Name',
          required: true,
          heading: 'Short answer',
          default: 'true',
          page: 1,
          type: 'Textbox',
        },
        {
          icon: 'TfiText',
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          required: true,
          type: 'Textbox',
          heading: 'Short answer',
          default: 'true',
          page: 1,
          qt: 'What is your Email',
        },
        {
          icon: 'GoHash',
          id: Date.now() + Math.random().toString(36).substring(2, 9),
          qt: 'What is your Phone Number',
          required: false,
          heading: 'Number',
          default: 'true',
          page: 1,
          type: 'Textbox',
        },
      ],
    },
  ];

  return formPayload;
}
