//OPTIMIZED
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { ToastMessages } from '@/constants/toast-messages';
import { MutationConfig, useMutation } from '@/lib/react-query';

const createAnswers = async (body: any) => {
  const response = await fetch(`/api/public/form-answers`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error creating answers');
  return response.json();
};
const createClient = async (body: any) => {
  const response = await fetch(`/api/public/form-answers/client`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error creating answers');
  return response.json();
};
const createClientEmails = async (body: any) => {
  const response = await fetch(`/api/public/form-answers/client_emails`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error creating answers');
  return response.json();
};
const createClientActivities = async (body: any) => {
  const response = await fetch(`/api/public/form-answers/activities`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) throw new Error('Error creating answers');
  return response.json();
};

const updateAnswers = async ({ id, ...body }: any) => {
  const response = await fetch(`/api/public/form-answers`, {
    method: 'PUT',
    body: JSON.stringify({ id, ...body }),
  });
  if (!response.ok) throw new Error('Error updating answers');
  return response.json();
};

type CreateQueryFnType = typeof createAnswers;
type UpdateQueryFnType = typeof updateAnswers;

export const useCreateAnswersMutation = (
  config?: MutationConfig<CreateQueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: [queryKey.forms.uploadFormAnswers],
    mutationFn: createAnswers,
    ...config,
  });
};

export const useUpdateAnswersMutation = (
  config?: MutationConfig<UpdateQueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: [queryKey.forms.updateFormAnswers],
    mutationFn: updateAnswers,
    ...config,
  });
};
export const useCreateClientMutation = (
  config?: MutationConfig<UpdateQueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: [queryKey.forms.createClient],
    mutationFn: createClient,
    ...config,
  });
};
export const useCreateClientEmailsMutation = (
  config?: MutationConfig<UpdateQueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: [queryKey.forms.createClientEmails],
    mutationFn: createClientEmails,
    ...config,
  });
};
export const useCreateClientActivitiesMutation = (
  config?: MutationConfig<UpdateQueryFnType>
) => {
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    retry: false,
    mutationKey: [queryKey.forms.createClientActivities],
    mutationFn: createClientActivities,
    ...config,
  });
};
