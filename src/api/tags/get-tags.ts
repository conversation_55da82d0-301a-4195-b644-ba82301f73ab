import { queryKey } from '@/constants/query-key';
import { QueryConfigType, useQuery } from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function getTags(filter: any) {
  const baseUrl = `/api/tags`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);
  const response = await fetch(apiUrl, { method: 'GET' });
  const data = await response.json();
  return data;
}
type QueryFnType = typeof getTags;

export const useGetTagsQuery = (
  filter: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery({
    retry(failureCount: any, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.tags.getAll, filter],
    queryFn: () => getTags(filter),
    ...config,
  });
};
