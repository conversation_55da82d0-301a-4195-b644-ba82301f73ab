import { query<PERSON><PERSON> } from '@/constants/query-key';
import {
  ExtractFnReturnType,
  QueryConfigType,
  useQuery,
} from '@/lib/react-query';
import { buildUrlWithQueryParams } from '@/utils/build-url-query';

async function getUserClientCount(filter: any) {
  const baseUrl = `/api/users/dashboard`;
  const apiUrl = buildUrlWithQueryParams(baseUrl, filter);

  const response = await fetch(apiUrl, {
    method: 'GET',
    cache: 'no-store',
  });

  const json = await response.json();
  return json;
}

type QueryFnType = typeof getUserClientCount;

export const useGetUserClientCountQuery = (
  filter: any,
  config?: QueryConfigType<QueryFnType>
) => {
  return useQuery<ExtractFnReturnType<QueryFnType>>({
    retry(failureCount, error: any) {
      if ([404, 401].includes(error.status)) return false;
      else if (failureCount < 1) return true;
      else return false;
    },
    queryKey: [queryKey.users.getSlpClientsCount, filter],
    queryFn: () => getUserClientCount(filter),
    ...config,
  });
};
