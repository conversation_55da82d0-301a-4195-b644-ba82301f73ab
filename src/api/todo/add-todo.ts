import { MutationConfig, useMutation, useQueryClient } from '@/lib/react-query';
import { ToastMessages } from '@/constants/toast-messages';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

interface TodoFormData {
  task: string;
  date: string;
  client_id?: number | null;
  client_name?: string;
  user_id?: number;
  status?: 'undone' | 'done';
}

const addTodo = async (body: TodoFormData) => {
  const response = await fetch(`/api/todos`, {
    method: 'POST',
    body: JSON.stringify(body),
  });
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'Error adding todo item');
  }
  return response.json();
};

type QueryFnType = typeof addTodo;

export const useAddTodoApi = (config?: MutationConfig<QueryFnType>) => {
  const queryClient = useQueryClient();
  return useMutation({
    onError: (err: any) => {
      toaster.create({
        type: 'error',
        description: err?.message || ToastMessages.somethingWrong,
      });
    },
    onSuccess: () => {
      toaster.create({
        type: 'success',
        description: 'New Todo Item Added',
      });
      queryClient.invalidateQueries([queryKey.todos.getAll]);
    },
    retry: false,
    mutationKey: ['add-todo'],
    mutationFn: addTodo,
    ...config,
  });
};
