import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTable from '@/components/table/CustomTable';
import {
  Box,
  Center,
  Flex,
  Group,
  Spinner,
  Heading,
  Input,
  Text,
  InputAddon,
  VStack,
  HStack,
} from '@chakra-ui/react';
import React, { ReactNode } from 'react';
import { FaSearch } from 'react-icons/fa';
import { IGetInvoicesFilterState } from '@/store/filters/invoices';
import { useRecoilState } from 'recoil';
import EmptyState from '@/components/elements/EmptyState';

type IReuseableInvoices = {
  data: any;
  isLoading: boolean;
  section: 'raw' | 'contact';
  columnDef: any;
  isChecked: boolean;
  search?: string;
  onChange?: () => void;
  setSearch?: (e: string) => void;
  onBlur?: () => void;
  addInvoiceModal?: ReactNode;
  options?: ReactNode;
  onRowClick?: any;
};

export function ReuseableInvoices({
  data,
  isLoading,
  section,
  columnDef,
  search,
  setSearch,
  onBlur,
  addInvoiceModal,
  options,
  onRowClick,
}: IReuseableInvoices) {
  const [filter, setFilter] = useRecoilState(IGetInvoicesFilterState);
  const isRaw = section === 'raw';

  const actualData = isRaw ? data?.invoices : data;
  const isEmpty = !isLoading && data && actualData?.length === 0;

  return (
    <Box>
      <Flex
        justifyContent={'space-between'}
        w={'100%'}
        alignItems={{ base: 'flex-start', md: 'center' }}
        flexDirection={{ base: 'row', md: 'row' }}
        gap={{ base: '1rem', md: '2rem' }}
      >
        <VStack gap={'4'}>
          {isRaw ? (
            <Flex
              flexDirection={'column'}
              alignItems={'start'}
              mt={2}
              gap={'6px'}
              width={{ base: 'full', md: '25rem' }}
            >
              <Heading
                fontSize={{ base: '1.3rem', md: '2rem' }}
                fontWeight={'semibold'}
                mb={{ base: '1', lg: '2' }}
              >
                Invoices{' '}
              </Heading>

              <Text
                fontSize={{ base: 'sm', md: 'md' }}
                color={'gray.300'}
                fontWeight={'500'}
              >
                Total :{' '}
                {isLoading ? (
                  <Spinner size={'xs'} color={'#e97a5b'} />
                ) : (
                  `(${isRaw ? data?.total_count || 0 : data?.length || 0})`
                )}
              </Text>

              {/* <Text mb={'1rem'}>Raw of Invoices Synced with Quickbooks</Text> */}
              {/* {addInvoiceModal} */}
            </Flex>
          ) : null}
        </VStack>
        <HStack>
          {isRaw && (
            <Group attached>
              <Input
                placeholder="Search ..."
                value={search}
                onChange={(e) => setSearch && setSearch(e?.target.value)}
                onBlur={onBlur}
                rounded={'md'}
                border="1px solid"
                borderColor={'gray.100'}
              />
              <InputAddon
                bg={'#e97a5b'}
                cursor={'pointer'}
                roundedBottomRight={'md'}
                roundedTopRight={'md'}
                onClick={onBlur}
                borderColor={'#e97a5b'}
              >
                <FaSearch color={'white'} />
              </InputAddon>
            </Group>
          )}
          {addInvoiceModal}
        </HStack>
      </Flex>

      {options}

      {/* ===================TABLE====================== */}
      <Box>
        {isLoading ? (
          <Center h={'20rem'}>
            <AnimateLoader />
          </Center>
        ) : isEmpty ? (
          <EmptyState text="no invoices" />
        ) : (
          <CustomTable
            columnDef={columnDef}
            data={isRaw ? data.invoices : data || []}
            filter={{
              tableName: 'Followup',
            }}
            tableOptions={{
              pageCount: 1,

              enableRowSelection: true,
            }}
            enableSorting={true}
            total={data?.total_count ? data?.total_count : data?.length}
            pagination={{
              row: Number(filter?.size),
              page: Number(filter?.currentPage),
            }}
            setPagination={{
              onPageChange: (e) => setFilter({ ...filter, currentPage: e }),
              onRowChange: (e) => setFilter({ ...filter, size: e }),
            }}
            onRowClick={onRowClick}
            // tableFooter={isRaw && <TableFooter data={data} />}
          />
        )}
      </Box>
    </Box>
  );
}
