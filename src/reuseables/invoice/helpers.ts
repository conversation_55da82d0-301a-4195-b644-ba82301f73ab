import { tableNames } from '@/constants/table_names';
// import supabase from '@/lib/supabase/client';
// import { findAllSlpNotes } from '@/api/slp_notes';
import { TypedSupabaseClient } from '@/shared/types/supabaseclient';
import { getDateString } from '@/utils/date-formatter';
import { formatMemo } from '@/utils/string-formatter';
import { findAllSlpNotesByClient } from '@/app/service/slp_notes';

export const checkDuplicate = async (
  email: string,
  invoice_number: any,
  invoice_date: any,
  supabase: any
) => {
  const duplicate = await supabase
    .from(tableNames.invoices)
    .select(`*`)
    .eq('email', email?.toLowerCase())
    .eq('invoice_number', invoice_number)
    .eq('invoice_date', invoice_date);

  return duplicate;
};
export const createInvoice = async (invoice: any, supabase: any) => {
  const { data, error } = await supabase
    .from(tableNames.invoices)
    .insert(invoice)
    .select();
  if (error) throw error;

  return data;
};

export const updateInvoice = async (invoice: any, supabase: any) => {
  const temp = { ...invoice };
  const id = temp.id;
  delete temp.id;
  if (!id) throw new Error('Missing invoice id');

  const { data, error } = await supabase
    .from(tableNames.invoices)
    .update(temp)
    .eq('id', id)
    .select();
  if (error) throw error;
  return data;
};
export const insertIntoClients = async (data_to_insert: any, supabase: any) => {
  const [firstName, ...lastNameParts] = (data_to_insert.name || '').split(' ');
  const lastName = lastNameParts.join(' ') || null;
  console.log('trying to insert');

  const { data, error } = await supabase
    .from(tableNames.clients)
    .insert({
      initial_email: data_to_insert.email.toLowerCase(),
      first_name: firstName,
      last_name: lastName,
      display_name: data_to_insert.name,
      stage: data_to_insert.total_price > 0 ? 'Customer' : 'Prospect',
      slp_notes: 'Active',
      organization_id: data_to_insert.organization_id,
    })
    .select();
  console.log('client inserted', error);

  if (error) throw error;
  return data[0];
};

export const linkInvoiceWithSlpNote = async (
  invoice: any,
  supabase: TypedSupabaseClient
) => {
  const allSlpNote = await findAllSlpNotesByClient(
    invoice?.client_id,
    supabase
  );

  console.log('trying to link ', allSlpNote);

  // const arr: any = [];
  const slpNote = allSlpNote.find((slp_note: any) => {
    return (
      formatMemo(invoice?.memo) === formatMemo(slp_note?.invoice_memo) &&
      invoice?.slp_id === slp_note?.slp_id &&
      invoice.client_id === slp_note?.client_id &&
      getDateString(invoice.invoice_date) === getDateString(slp_note.note_date)
    );
  });

  console.log('slp note to link is found ', slpNote);

  if (!slpNote) {
    return;
  }
  // update the invoice
  if (slpNote?.package_id && slpNote?.referral?.toLowerCase() === 'false') {
    const { error } = await supabase
      .from(tableNames.invoices)
      .update({
        package_id: slpNote?.package_id,
        client_id: slpNote?.client_id,
      })
      .eq('id', invoice.id);

    const { data: packageData } = await supabase
      .from(tableNames.packages)
      .select('*')
      .eq('id', Number(slpNote?.package_id))
      .single();
    if (packageData) {
      console.log('packageData----4', packageData);
      const amountToSubtract = slpNote.session_count;
      const newPackageBalance =
        Number(packageData.balance) - Number(amountToSubtract);
      await supabase
        .from(tableNames.packages)
        .update({
          balance: newPackageBalance,
          status: newPackageBalance <= 0 ? 'COMPLETED' : packageData?.status,
        })
        .eq('id', Number(slpNote?.package_id));
    }

    if (error) throw error;
  }
  await supabase
    .from(tableNames.invoices)
    .update({
      client_id: slpNote?.client_id,
    })
    .eq('id', invoice.id);
  //update slp note
  const { error } = await supabase
    .from(tableNames.slp_notes)
    .update({ invoice_id: invoice?.id })
    .eq('id', slpNote.id);
  if (error) throw error;
};
export const linkInvoiceWithSlpNoteQuickBooksP = async (
  invoice: any,
  slpNoteId: any,
  supabase: TypedSupabaseClient
) => {
  const { data: slpNote } = await supabase
    .from(tableNames.slp_notes)
    .select('*')
    .eq('id', Number(slpNoteId))
    .single();

  if (!slpNote) {
    return;
  }

  const { error } = await supabase
    .from(tableNames.slp_notes)
    .update({
      invoice_id: invoice?.id,
      duration: invoice?.duration,
    })
    .eq('id', slpNote.id);

  if (error) throw error;
};

export const updateClient = async (data_to_insert: any, supabase: any) => {
  if (data_to_insert.session_type?.toLowerCase() === 'workshop') {
    return;
  }
  const slp_id = data_to_insert.slp_id;
  const clientId = data_to_insert.client_id;
  const { data, error } = await supabase
    .from(tableNames.clients)
    .update({ active_slp: Number(slp_id) })
    .eq('id', clientId)
    .select();
  if (error) throw error;
  return data;
};

export const determineInvoiceStatus = (data: {
  transactions: any[];
  total_price: number;
  discount?: any;
  tax_value?: number;
}) => {
  const totalPaid = data?.transactions?.reduce(
    (prev: number, currentTransaction: any) => {
      return prev + Number(currentTransaction?.amount || 0);
    },
    0
  );

  let discountAmount = 0;
  if (data.discount && typeof data.discount === 'object') {
    // New invoice system with discount object
    if (data.discount.value > 0) {
      if (data.discount.type === 'percentage') {
        const subtotal = data?.total_price;
        discountAmount = (subtotal * data.discount.value) / 100;
      } else {
        // Fixed amount discount
        discountAmount = data.discount.value;
      }
    }
  } else if (typeof data.discount === 'number' && data.discount > 0) {
    // Legacy discount as number
    discountAmount = data.discount;
  }

  // Calculate amount due: total_price - discount - totalPaid

  const taxAmount = data?.tax_value || 0;
  const amountWithTax = Number(data?.total_price) + Number(taxAmount);
  const amountWithDiscount = amountWithTax - discountAmount;
  // Calculate amount due: total_price - discount - totalPaid
  const amountDue = amountWithDiscount - Number(totalPaid);

  if (amountDue <= 0 && data?.transactions.length) {
    return 'PAID';
  }
  if (amountDue > 0 && data?.transactions.length) {
    return 'PARTIALLY_PAID';
  }
  if (amountDue > 0 && !data?.transactions.length) {
    return 'AWAITING_PAYMENT';
  }
};
