import { IOrganization } from '.';
import { Database } from '../types/supabase';

type UserRow = Database['public']['Tables']['users']['Row'];

export interface IUser extends UserRow {
  organization: IOrganization;
  organization_id: number;
  permissions?: Array<string>;
  last_login_dt?: string | null;
  is_test_user?: boolean;
  owned_organization?: any;
  subscription?: any;
}

export interface IUserPersmissionState {
  id: number;
}
