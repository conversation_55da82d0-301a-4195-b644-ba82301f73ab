import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';

import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  const supabase = createSupabaseServer();

  const body = await request.json();

  const { data, error } = await supabase
    .from(tableNames.onboarding)
    .insert({ details: body?.details, user_id: body.userId })
    .select('*')
    .single();

  await supabase
    .from(tableNames.users)
    .update({
      onboarding_id: data?.id || null,
    })
    .eq('id', Number(body.userId))
    .select('*');

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}
