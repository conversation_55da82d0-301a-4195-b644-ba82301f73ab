import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
// import supabase from '@/lib/supabase/client';
import { getNumberParam } from '@/utils/format-object';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseServer();

    const { id } = params;
    const { searchParams } = new URL(request.url);
    const limitQuery = getNumberParam(searchParams, 'limit', 0);
    const page = getNumberParam(searchParams, 'page', 1);
    const organization_id = searchParams.get('org_id') || '';

    // Filter parameters
    const date_from = searchParams.get('date_from') || '';
    const date_to = searchParams.get('date_to') || '';
    const status = searchParams.get('status') || '';
    const client_id = searchParams.get('client_id') || '';
    const invoice_no = searchParams.get('invoice_no') || '';

    // Helper function to apply filters to query
    const applyFilters = (query: any) => {
      // Base filters (always applied)
      query = query.eq('slp_id', id).eq('organization_id', organization_id);

      // Date range filters
      if (date_from) {
        query = query.gte('invoice_date', date_from);
      }
      if (date_to) {
        query = query.lte('invoice_date', date_to);
      }

      // Status filter
      if (status) {
        if (status === 'UNPAID') {
          // UNPAID is a compound status: PARTIALLY_PAID and AWAITING_PAYMENT
          query = query.in('status', ['PARTIALLY_PAID', 'AWAITING_PAYMENT']);
        } else {
          query = query.eq('status', status);
        }
      }

      // Client filter
      if (client_id) {
        query = query.eq('client_id', parseInt(client_id));
      }

      // Invoice number filter (partial match)
      if (invoice_no) {
        query = query.ilike('invoice_number', `%${invoice_no}%`);
      }

      return query;
    };
    let allData: any = [];

    if (limitQuery) {
      // Calculate pagination offset
      const offset = (page - 1) * limitQuery;

      // Build query with filters
      let query = supabase.from(tableNames.invoices).select(
        `*,
          clients(display_name),
          transactions!transactions_invoice_id_fkey(*),
          services(*),
          organizations(id,plan)
          `,
        { count: 'exact' }
      );

      // Apply filters
      query = applyFilters(query);

      // Apply pagination and ordering
      const { data, error, count } = await query
        .range(offset, offset + limitQuery - 1)
        .order('invoice_date', { ascending: false });

      if (error) {
        throw error;
      }

      allData = data;

      const updatedInvoices = allData?.map((invoice: any) => {
        let amountDue = 0;
        let discountAmount = 0;

        // Handle object-type discount
        if (invoice.discount && typeof invoice.discount === 'object') {
          if (invoice.discount.value > 0) {
            if (invoice.discount.type === 'percentage') {
              discountAmount =
                (invoice.total_price * invoice.discount.value) / 100;
            } else {
              discountAmount = invoice.discount.value;
            }
          }
        }

        // Handle numeric discount
        else if (typeof invoice.discount === 'number' && invoice.discount > 0) {
          discountAmount = invoice.discount;
        }

        // Handle transactions
        if (invoice.transactions && invoice.transactions.length > 0) {
          const totalPaid = invoice.transactions.reduce(
            (sum: number, transaction: any) =>
              sum + Number(transaction.amount || 0),
            0
          );
          amountDue = Number(invoice.total_price) - discountAmount - totalPaid;
        } else {
          amountDue = Number(invoice.total_price) - discountAmount;
        }

        const resultInvoice = {
          ...invoice,
          amount_due: Math.max(amountDue, 0),
        };

        return resultInvoice;
      });

      // Calculate pagination metadata
      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / limitQuery);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const response = {
        data: updatedInvoices,
        pagination: {
          page,
          limit: limitQuery,
          total: totalCount,
          totalPages,
          hasNextPage,
          hasPrevPage,
        },
      };

      return NextResponse.json(response, { status: 200 });
    }

    // Fallback: Fetch all data with filters (when no limit is provided)
    const fetchLimit = 1000; // Number of records to fetch per iteration
    let offset = 0;

    // eslint-disable-next-line no-constant-condition
    while (true) {
      // Build query with filters
      let query = supabase
        .from(tableNames.invoices)
        .select('*,organizations(plan,id)');

      // Apply filters
      query = applyFilters(query);

      // Apply pagination and ordering
      const { data, error } = await query
        .range(offset, offset + fetchLimit - 1)
        .order('invoice_date');

      if (error) {
        console.log('error is ', error);
        throw error;
      }

      if (data.length === 0) {
        break; // No more data, break out of the loop
      }

      allData = allData.concat(data);
      offset += fetchLimit;
    }

    return NextResponse.json(allData, { status: 200 });
  } catch (error: any) {
    console.log('error is ', error);

    return NextResponse.json({ message: error.message }, { status: 500 });
  }
}
