import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

// Define a POST handler for refunding Data
export async function POST(request: Request) {
  // const cookieStore = await cookies();
  const supabase = createSupabaseServer();
  // const cookie: any = await cookieStore.get('user_data');
  const body = await request.json();
  const userId = body?.user_id;

  const { data, error } = await supabase.from(tableNames.todo).insert({
    due_date: body?.date || null,
    task: body?.task,
    user_id: userId,
    client_id: body?.client_id,
  });

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}

// Define a DELETE handler for refunding Data
export async function DELETE(request: Request) {
  const supabase = createSupabaseServer();
  const body = await request.json();
  const { data, error } = await supabase
    .from(tableNames.todo)
    .delete()
    .eq('id', body);

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}

// Define a PATCH handler for refunding Data
export async function PATCH(request: Request) {
  const supabase = createSupabaseServer();
  const body = await request.json();

  const { data, error } = await supabase
    .from(tableNames.todo)
    .update(body)
    .eq('id', body?.id);

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}

export async function GET(request: Request) {
  // const cookieStore = await cookies();
  const supabase = createSupabaseServer();
  // const cookie: any = await cookieStore.get('user_data');
  // const userId = JSON.parse(cookie.value)?.id;
  const { searchParams } = new URL(request.url);
  const sort = searchParams.get('sort') || '';
  const id = searchParams.get('id') || '';
  const userId = searchParams.get('user_id') || '';

  const { data, error } = await supabase
    .from(tableNames.todo)
    .select('*, clients(*)')
    .eq('user_id', id || userId)
    .order('created_at', { ascending: sort === 'desc' ? false : true });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data);
}
