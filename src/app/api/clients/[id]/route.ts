'use server';
import { NextResponse } from 'next/server';
// import supabase from '@/lib/supabase/client';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';

// Define a GET handler for fetching the client by ID
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const supabase = createSupabaseServer();
  const { id } = params;

  const { data, error } = await supabase
    .from(tableNames.clients)
    .select(
      ` *,
      transactions(*),
      invoices(*, 
        slp:slp_id(*), 
        slp_notes:slp_notes_invoice_id_fkey(*),
        clients(display_name),
        invoice_items:invoice_items_invoice_id_fkey(*)
      ),
      bookings (*, slp:slp_id(*)),
      client_emails(*),
      packages(*, invoices(*), clients(*)),
      slp_notes:slp_notes_client_id_fkey(*),
      active_slp(*),
      refunds(*),
      tags(*),
      organization:organization_id(*),
      organization_id,
      form_answers:form_answers_client_id_fkey(*)  // Add this line
    `
    )
    .eq('id', id)
    .order('id', { foreignTable: 'invoices' })
    .order('appointment', { foreignTable: 'bookings', ascending: false });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  if (data.length === 0) {
    return NextResponse.json({ error: 'No client found' }, { status: 500 });
  }

  return NextResponse.json(data?.[0]);
}
// Define a PATCH handler for fetching the client by ID
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  const supabase = createSupabaseServer();

  const { id } = params;
  const { payload } = await request.json();
  console.log('payload', payload);
  if (!id || !payload) {
    return NextResponse.json(
      { message: 'ID and data are required' },
      { status: 400 }
    );
  }
  const { data: updatedClient, error } = await supabase
    .from(tableNames.clients)
    .update(payload)
    .eq('id', id)
    .select();
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  return NextResponse.json(updatedClient?.[0]);
}
