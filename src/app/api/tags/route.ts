import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const supabase = createSupabaseServer();
  const { searchParams } = new URL(request.url);
  const category = searchParams.get('category') || '';
  const user_id = searchParams.get('user_id') || '';

  const { data: tags, error } = await supabase
    .from(tableNames.tags)
    .select('*, client_id(id, first_name, last_name)')
    .eq('user_id', user_id)
    .eq('category', category);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  const transformData = tags?.reduce((acc, item) => {
    const tag_name = item.tag_name;
    if (!acc[tag_name]) {
      acc[tag_name] = {
        name: tag_name,
        data: [],
      };
    }

    acc[tag_name].data.push(item);

    return acc;
  }, {});

  return NextResponse.json(Object.values(transformData) || {});
}

export async function POST(request: Request) {
  const supabase = createSupabaseServer();

  const body = await request.json();
  // const payload = body.map((item: any) => ({ ...item, user_id: userId }));

  const { data, error } = await supabase.from(tableNames.tags).insert(body);

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}

export async function PATCH(request: Request) {
  try {
    const supabase = createSupabaseServer();
    const body = await request.json();

    if (!Array.isArray(body) || body.length === 0) {
      const { data, error } = await supabase
        .from(tableNames.tags)
        .update(body)
        .eq('id', body.id)
        .select('*')
        .single();
      if (error) {
        return NextResponse.json({ message: error.message }, { status: 500 });
      }

      return NextResponse.json({ data }, { status: 200 });
    }

    const updates = body.filter((item) => item.id);

    const promises = updates.map(async (update) => {
      const { data, error } = await supabase
        .from(tableNames.tags)
        .update(update)
        .eq('id', update.id)
        .select('*')
        .single();

      if (error) {
        throw new Error(`Failed to update tag ${update.id}: ${error.message}`);
      }
      return data;
    });

    const results = await Promise.all(promises);

    const updatedData = results.filter(Boolean);

    const newRecords = body
      .filter((item) => !item.id)
      .map((item) => ({ ...item, user_id: updatedData?.[0]?.user_id }));

    await supabase.from(tableNames.tags).insert(newRecords);

    return NextResponse.json({ data: updatedData }, { status: 200 });
  } catch (error) {
    console.error('Error updating tags:', error);
    return NextResponse.json(
      {
        message:
          error instanceof Error ? error.message : 'Failed to update tags',
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request) {
  const supabase = createSupabaseServer();

  const body = await request.json();

  if (!body?.id) {
    return NextResponse.json({ status: 200 });
  }

  const { data, error } = await supabase
    .from(tableNames.tags)
    .delete()
    .eq('id', body?.id);

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }

  return NextResponse.json({ data }, { status: 200 });
}
