import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: Request,
  { params }: { params: { email: string; org_id: string } }
) {
  const { email, org_id } = params;
  const { data, error } = await supabaseAdmin
    .from(tableNames.client_emails)
    .select(
      `*, client:clients!client_emails_client_id_fkey(id, first_name, last_name, phone)`
    )
    .eq('email', email)
    .eq('organization_id', Number(org_id))
    .maybeSingle();

  console.log('data is ', data);
  console.log('error is ', error);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json(data);
}
