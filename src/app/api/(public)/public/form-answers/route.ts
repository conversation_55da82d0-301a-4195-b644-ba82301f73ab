import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

const supabaseAdmin = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  const body = await request.json();
  const { data, error } = await supabaseAdmin
    .from(tableNames.form_answers)
    .insert(body)
    .select();
  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }
  return NextResponse.json(data, { status: 200 });
}

export async function PUT(request: NextRequest) {
  const { id, answer_details, ...body } = await request.json();
  try {
    // First, get the existing record to merge answer_details
    const { data: existingRecord, error: fetchError } = await supabaseAdmin
      .from(tableNames.form_answers)
      .select('answer_details')
      .eq('id', id)
      .single();
    if (fetchError) {
      return NextResponse.json(
        { message: fetchError.message },
        { status: 500 }
      );
    }
    // Merge existing answer_details with new ones
    let mergedAnswerDetails: any[] = [];

    if (
      existingRecord?.answer_details &&
      Array.isArray(existingRecord.answer_details)
    ) {
      mergedAnswerDetails = [...existingRecord.answer_details];
    }
    // Add new answer_details (if provided)
    if (answer_details && Array.isArray(answer_details)) {
      // Remove any existing answers for the same question IDs to avoid duplicates
      const newQuestionIds = answer_details.map((answer: any) => answer.id);
      mergedAnswerDetails = mergedAnswerDetails.filter(
        (existingAnswer: any) => !newQuestionIds.includes(existingAnswer.id)
      );

      // Add the new answers
      mergedAnswerDetails.push(...answer_details);
    }
    // Update the record with merged answer_details
    const updateData = {
      ...body,
      answer_details: mergedAnswerDetails,
    };
    const { data, error } = await supabaseAdmin
      .from(tableNames.form_answers)
      .update(updateData)
      .eq('id', id)
      .select();
    if (error) {
      return NextResponse.json({ message: error.message }, { status: 500 });
    }
    return NextResponse.json(data, { status: 200 });
  } catch (error) {
    console.error('Error updating form answers:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
