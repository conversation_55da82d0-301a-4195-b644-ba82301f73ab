import { getInvoiceById, updateInvoiceById } from '@/app/service/invoice';
import {
  deleteInvoiceItems,
  getInvoiceItemById,
  updateInvoiceItemById,
} from '@/app/service/invoice_items';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';
import {
  getTaxValueForAnItem,
  handleAllTaxPlusPrice,
  handleInvoiceItems,
} from '../util';
import { updatePurchaseById } from '@/app/service/purchase';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseServer();

    // 1. Fetch the invoice and related data (excluding taxes)
    const { data: invoice, error: queryError } = await supabase
      .from(tableNames.invoices)
      .select(
        `
        *,
        client:clients (
          id,
          display_name,
          initial_email,
          phone,
          country,
          state,
          city
        ),
        transactions!transactions_invoice_id_fkey(*),
        invoice_items (
          *,
          package_offering (
            *
          ),
          services (
            *
          )
        ),
        services_purchases!services_purchases_invoice_id_fkey(*,service:services!service_id(*), invoice_items(*))
        `
      )
      .eq('id', Number(id))
      .single();

    if (queryError) {
      console.error('Supabase query error:', queryError);
      return NextResponse.json(
        {
          error: 'Failed to fetch invoice',
          code: queryError.code,
        },
        { status: 500 }
      );
    }

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // 2. Collect all tax_ids across all invoice_items
    const allTaxIds = Array.from(
      new Set(
        invoice.invoice_items
          .flatMap((item: any) => item.tax_ids || [])
          .filter(Boolean)
      )
    );

    // 3. Fetch taxes separately
    const { data: taxes, error: taxError } = await supabase
      .from('taxes')
      .select('*')
      .in('id', allTaxIds);

    if (taxError) {
      console.error('Failed to fetch taxes:', taxError);
      return NextResponse.json(
        { error: 'Failed to fetch taxes', code: taxError.code },
        { status: 500 }
      );
    }

    // 4. Map tax_ids to full tax objects and attach to each invoice_item
    const taxMap = new Map<number, any>();
    for (const tax of taxes || []) {
      taxMap.set(tax.id, tax);
    }

    invoice.invoice_items = invoice.invoice_items.map((item: any) => ({
      ...item,
      taxes: (item.tax_ids || [])
        .map((taxId: number) => taxMap.get(taxId))
        .filter(Boolean),
    }));

    return NextResponse.json({ data: invoice });
  } catch (error: any) {
    console.error('Unexpected error in GET /api/invoices/[id]:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && {
          details: error.message,
        }),
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createSupabaseServer();
    const { id } = params;
    const body = await request.json();
    const {
      tax_value,
      discount,
      newItems,
      updatedItems,
      deletedItems,
      due_date,
      invoice_date,
      invoice_number,
      memo,
    } = body;

    // console.log('body is ', body);
    // console.log('id is ', id);

    if (!id) {
      return NextResponse.json(
        { error: 'Invoice ID is required' },
        { status: 400 }
      );
    }

    // 1 Verify if invoice exist
    const invoiceData = await getInvoiceById(id, supabase);
    if (!invoiceData) throw new Error(`Invoice  not found: ${id}`);
    let total = 0;

    // 2. Handle deleted items (soft‐delete or hard delete)
    if (Array.isArray(deletedItems) && deletedItems.length) {
      for (const item of deletedItems) {
        const invoice = await getInvoiceItemById(item?.id, supabase);
        for (const purchase of invoice.services_purchases) {
          await updatePurchaseById(
            purchase.id,
            { status: 'DELETED' },
            supabase
          );
        }
        await deleteInvoiceItems({ id: item?.id }, supabase);
      }
    }

    // 3. Handle updated items
    if (Array.isArray(updatedItems) && updatedItems.length) {
      const allUpdatedPrice = await handleAllTaxPlusPrice(
        updatedItems,
        supabase
      );
      total += allUpdatedPrice;
      console.log('allUpdatedPrice is ', allUpdatedPrice);
      // return;

      for (const item of updatedItems) {
        const {
          id,
          package_id,
          service_id,
          price,
          quantity,
          product_name,
          description,
        } = item;
        const tax_value = await getTaxValueForAnItem(item, supabase);
        // apply any recalculation for price/tax here if needed,
        // or you could assume the client already did that.
        await updateInvoiceItemById(
          id,
          {
            price,
            quantity,
            product_name,
            description,
            updated_at: new Date(),
            tax_value,
            tax_ids: item?.taxes?.map((item: any) => Number(item?.id)),
            ...(service_id && { service_id }),
            ...(package_id && { package_id }),
          },
          supabase
        );
      }
    }

    // 4. Handle new items
    if (Array.isArray(newItems) && newItems.length) {
      for (const item of newItems) {
        // reuse your create‐item logic
        console.log('trying to add is ', item);
        const createdPrice = await handleInvoiceItems(id, [item], supabase);
        console.log('createdPrice is ', createdPrice);

        total += createdPrice;
      }
    }
    console.log({
      total_price: total,
      tax_value,
      invoice_date,
      due_date,
      discount,
    });

    // 5. Update invoice total_price
    const updatedInvoice = await updateInvoiceById(
      id,
      {
        total_price: total,
        tax_value,
        invoice_date: new Date(invoice_date),
        invoice_date_raw: invoice_date,
        due_date: new Date(due_date),
        discount,
        invoice_number,
        memo,
      },
      supabase
    );

    return NextResponse.json({
      success: true,
      data: updatedInvoice,
    });
  } catch (error: any) {
    console.error('Unexpected error in PUT /api/invoices/[id]:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        ...(process.env.NODE_ENV === 'development' && {
          details: error.message,
        }),
      },
      { status: 500 }
    );
  }
}
