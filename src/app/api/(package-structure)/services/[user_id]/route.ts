import { tableNames } from '@/constants/table_names';
import { NextResponse } from 'next/server';
// import { createClient } from '@supabase/supabase-js';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getNumberParam } from '@/utils/format-object';

// const getSupabaseClient = (request: Request) => {
//   const authHeader = request.headers.get('Authorization') || '';
//   const token = authHeader.replace(/^Bearer\s+/, '');
//   const supabase = createClient(
//     process.env.NEXT_PUBLIC_SUPABASE_URL!,
//     process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
//     {
//       global: { headers: { Authorization: `Bearer ${token}` } },
//     }
//   );
//   return supabase;
// };
export async function GET(
  request: Request,
  { params }: { params: { user_id: string } }
) {
  const supabase = createSupabaseServer();
  const { searchParams } = new URL(request.url);
  const size = getNumberParam(searchParams, 'size', 50);
  const currentPage = getNumberParam(searchParams, 'currentPage', 1);
  const statusParam = searchParams.get('status');

  // Parse status filter - default to 'ACTIVE' if not provided
  const statusFilter = statusParam
    ? statusParam.split(',').map((s) => s.trim())
    : ['ACTIVE'];

  const start = (currentPage - 1) * size;
  const end = start + size - 1;
  const { user_id: organization_id } = params;

  if (!organization_id) {
    return NextResponse.json({ error: 'User id is required' }, { status: 500 });
  }

  const { data: services, error: servicesError } = await supabase.rpc(
    'get_services_with_taxes',
    {
      org_id: Number(organization_id),
      start: start,
      end_index: end,
    }
  );

  // Filter services by status using the IN filter
  const filteredServices =
    services?.filter((service: any) => statusFilter.includes(service.status)) ||
    [];

  if (servicesError) {
    console.error('Error fetching services:', servicesError);
  }

  const totalQuery = supabase
    .from(tableNames.services)
    .select('id', { count: 'exact', head: true })
    .eq('organization_id', Number(organization_id))
    .in('status', statusFilter);

  const { count, error: countError } = await totalQuery;

  if (servicesError || countError) {
    return NextResponse.json(
      { error: servicesError?.message || countError?.message },
      { status: 500 }
    );
  }

  return NextResponse.json(
    { services: filteredServices, total_count: count },
    { status: 200 }
  );
}
