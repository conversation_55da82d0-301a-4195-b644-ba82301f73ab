'use server';
import { NextRequest, NextResponse } from 'next/server';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';

// Define a GET handler for fetching the client by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  const supabase = createSupabaseServer();

  const { data, error } = await supabase
    .from(tableNames.bookings)
    .select(
      `*, 
      clients(*,  packages(*), invoices(*)), 
      slp_notes(*,invoice:invoices!slp_notes_invoice_id_fkey(*, slp_data:users(*),services(*), transactions:transactions_invoice_id_fkey(*), invoice_items(*, package_offering (
            *
          ),
          services (
            *
          )))),
      services_purchases(*, invoice_items(*, invoice_id(*), services(*)))
      
      `
      // package_used:redeemed_sessions(*),
    )
    .eq('id', id);
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  const booking = { ...data[0] };
  const servicePurchases = booking?.services_purchases
    ?.map((item: any) => {
      if (item?.booking_id === booking?.id) {
        return {
          ...item,
          invoice_items: {
            ...item?.invoice_items,
            quantity: 1,
          },
        };
      }
    })
    .filter(Boolean);
  // const invoice = {
  //   ...booking?.slp_notes?.invoice,
  // invoice_item_data: [...booking.slp_notes.invoice.invoice_items],
  // invoice_items: undefined,
  // };

  // 1. Fetch the invoice and related data (excluding taxes)
  const invoice = booking?.slp_notes?.invoice || ({} as any);
  // 2. Collect all tax_ids across all invoice_items
  const allTaxIds = Array.from(
    new Set(
      (invoice?.invoice_items || [])
        .flatMap((item: any) => item.tax_ids || [])
        .filter(Boolean)
    )
  );

  // 3. Fetch taxes separately
  const { data: taxes, error: taxError } = await supabase
    .from('taxes')
    .select('*')
    .in('id', allTaxIds);

  if (taxError) {
    console.error('Failed to fetch taxes:', taxError);
    return NextResponse.json(
      { error: 'Failed to fetch taxes', code: taxError.code },
      { status: 500 }
    );
  }

  // 4. Map tax_ids to full tax objects and attach to each invoice_item
  const taxMap = new Map<number, any>();
  for (const tax of taxes || []) {
    taxMap.set(tax.id, tax);
  }

  invoice.invoice_items = invoice?.invoice_items?.map((item: any) => ({
    ...item,
    taxes: (item?.tax_ids || [])
      .map((taxId: number) => taxMap.get(taxId))
      .filter(Boolean),
  }));

  return NextResponse.json([
    {
      ...booking,
      slp_notes: {
        ...booking?.slp_notes,
        invoice,
      },
      services_purchases: servicePurchases,
    },
  ]);
}

// Define a PATCH handler for fetching the client by ID
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const { id } = params;
  const supabase = await createSupabaseServer();

  const { payload } = await request.json();
  if (!id || !payload) {
    return NextResponse.json(
      { message: 'ID and data are required' },
      { status: 400 }
    );
  }
  console.log('payload is ', payload);

  const { data: updatedClient, error } = await supabase
    .from(tableNames.bookings)
    .update(payload)
    .eq('id', Number(id))
    .select();
  console.log('error', error);

  console.log('error is', error);
  if (error) {
    console.log('error', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  return NextResponse.json(updatedClient?.[0]);
}
