import { NextResponse } from 'next/server';
// import supabase from '@/lib/supabase/client';
import { tableNames } from '@/constants/table_names';
import { createSupabaseServer } from '@/lib/supabase/server';
import moment from 'moment/moment';
import 'moment-timezone';
import 'moment/locale/en-ca';

export async function GET(request: Request) {
  console.log('ln 11 i am here');

  try {
    const supabase = createSupabaseServer();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id') || '';
    const organization_id = searchParams.get('organization_id') || '';

    // Current month calculations
    const currentMonthStart = moment.tz().startOf('month').utc().toISOString();
    const currentMonthEnd = moment.tz().endOf('month').utc().toISOString();

    // Last month calculations
    const lastMonthStart = moment
      .tz()
      .subtract(1, 'month')
      .startOf('month')
      .utc()
      .toISOString();
    const lastMonthEnd = moment
      .tz()
      .subtract(1, 'month')
      .endOf('month')
      .utc()
      .toISOString();

    // Query for current month data
    const { data: currentMonthData, count: currentMonthCount } = await supabase
      .from(tableNames.clients)
      .select('*', { count: 'exact' })
      .eq('active_slp', id)
      .eq('organization_id', organization_id)
      .gte('lead_created', currentMonthStart)
      .lte('lead_created', currentMonthEnd);

    // Query for last month data
    const { data: lastMonthData, count: lastMonthCount } = await supabase
      .from(tableNames.clients)
      .select('*', { count: 'exact' })
      .eq('active_slp', id)
      .eq('organization_id', organization_id)
      .gte('lead_created', lastMonthStart)
      .lte('lead_created', lastMonthEnd);

    return NextResponse.json(
      {
        data: {
          currentMonth: {
            data: currentMonthData,
            count: currentMonthCount,
          },
          lastMonth: {
            data: lastMonthData,
            count: lastMonthCount,
          },
        },
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.log(error);
    return NextResponse.json(
      { success: false, message: error.message || 'Something went wrong' },
      { status: 500 }
    );
  }
}
