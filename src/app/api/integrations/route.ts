import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { type CookieOptions, createServerClient } from '@supabase/ssr';
import { env } from '@/constants/env';
import { tableNames } from '@/constants/table_names';
import { createClient } from '@supabase/supabase-js';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getSlugFromName } from '@/utils/event';
import moment from 'moment-timezone';

const adminSupabase = createClient(
  env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const FIXED_DATE = '2025-03-19';
const START_TIME = '09:00';
const END_TIME = '17:00';

function getStartTimeUTC(timezone: string): string {
  return moment
    .tz(`${FIXED_DATE} ${START_TIME}`, 'YYYY-MM-DD HH:mm', timezone)
    .toISOString();
}

function getEndTimeUTC(timezone: string): string {
  return moment
    .tz(`${FIXED_DATE} ${END_TIME}`, 'YYYY-MM-DD HH:mm', timezone)
    .toISOString();
}
const createDefaultEvent = async (user: any) => {
  const { data: Service } = await adminSupabase
    .from(tableNames.services)
    .select('*')
    .eq('name', 'Initial Consultation')
    .single();
  const title = 'Initial Consultation';
  const event = {
    title,
    description:
      'A 60-minute session to assess client needs and establish goals.',
    duration: 60,
    slug: getSlugFromName(title),
    organization_name: user?.organization?.name,
    organization_id: user?.organization_id,
    is_active: true,
    is_deleted: false,
    form_config: [
      {
        id: 1,
        qt: 'First name',
        icon: 'TfiText',
        type: 'Textbox',
        required: true,
        isDefault: true,
      },
      {
        id: 2,
        qt: 'Last name',
        icon: 'TfiText',
        type: 'Textbox',
        required: true,
        isDefault: true,
      },
      {
        id: 3,
        qt: 'Email',
        icon: 'TfiText',
        type: 'Textbox',
        required: true,
        isDefault: true,
      },
    ],
    creator_id: user?.id,
    creator_slug: user?.event_slug,
    service_id: Service?.id || null,
  };
  const { data: CreateEventdata, error: CreateEventError } = await adminSupabase
    .from(tableNames.events)
    .insert(event)
    .select('*')
    .single();
  if (CreateEventError) throw CreateEventError;

  console.log('start time is ', getStartTimeUTC(user.timezone));
  console.log('end time is ', getEndTimeUTC(user.timezone));

  const event_id = CreateEventdata?.id;
  const availabilities = [
    {
      day: 'sunday',
      available: false,
      startTime: getStartTimeUTC(user.timezone),
      endTime: getEndTimeUTC(user.timezone),
    },
    {
      day: 'monday',
      available: true,
      startTime: getStartTimeUTC(user.timezone),
      endTime: getEndTimeUTC(user.timezone),
    },
    {
      day: 'tuesday',
      available: true,
      startTime: getStartTimeUTC(user.timezone),
      endTime: getEndTimeUTC(user.timezone),
    },
    {
      day: 'wednesday',
      available: true,
      startTime: getStartTimeUTC(user.timezone),
      endTime: getEndTimeUTC(user.timezone),
    },
    {
      day: 'thursday',
      available: true,
      startTime: getStartTimeUTC(user.timezone),
      endTime: getEndTimeUTC(user.timezone),
    },
    {
      day: 'friday',
      available: true,
      startTime: getStartTimeUTC(user.timezone),
      endTime: getEndTimeUTC(user.timezone),
    },
    {
      day: 'saturday',
      available: false,
      startTime: getStartTimeUTC(user.timezone),
      endTime: getEndTimeUTC(user.timezone),
    },
  ];

  const payload = availabilities?.map((item: any) => ({
    event_id,
    start_time: item.startTime,
    end_time: item.endTime,
    available: item.available,
    day: item.day,
  }));

  const { error } = await adminSupabase
    .from(tableNames.availabilities)
    .insert(payload)
    .select();
  if (error) throw error;
};

export async function GET(request: Request) {
  const cookieStore = await cookies();
  const cookie: any = await cookieStore.get('user_data');
  const userEmail = JSON.parse(cookie.value)?.email;
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const service = searchParams.get('service');

  // return;
  // const nextParam = searchParams.get('next');

  // let next = '/';
  // if (nextParam && nextParam !== 'null') {
  //   try {
  //     next = atob(nextParam);
  //   } catch (error) {
  //     next = '/';
  //   }
  // }

  if (code) {
    const supabase = createServerClient(
      env.SUPABASE_URL!,
      env.SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            cookieStore.set({ name, value, ...options });
          },
          remove(name: string, options: CookieOptions) {
            cookieStore.delete({ name, ...options });
          },
        },
      }
    );

    const { error, data } = await supabase.auth.exchangeCodeForSession(code);
    if (error) {
      console.log('ln 54 is an error for exchangeCodeForSession', error);
      return NextResponse.redirect(`${origin}/auth/auth-code-error`);
    }

    if (userEmail !== data?.user?.email) {
      console.log('wrong user account selected', error);
      return NextResponse.redirect(
        `${origin}/auth/auth-code-error?error=wrong_user`
      );
    }

    const { data: fullUser } = await adminSupabase.rpc('get_user_by_email', {
      user_email: data.user?.email,
    });

    await supabase
      .from(tableNames.users)
      .update({
        google_refresh_token: data?.session?.provider_refresh_token,
        google_access_token: data?.session?.provider_token,
        ...(service === 'calendar'
          ? { is_google_calendar_connected: true }
          : service === 'gmail'
            ? { is_gmail_connected: true }
            : {}),
      })
      .eq('email', data.user?.email)
      .select('*')
      .single();

    const expirationDate = new Date(
      (data.session?.expires_at as number) * 1000
    );
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);

    cookieStore.set({
      name: 'user_data',
      value: JSON.stringify(fullUser),
      expires: expirationDate,
    });

    if (service === 'calendar') {
      try {
        await createDefaultEvent(fullUser);
      } catch (error) {
        return NextResponse.redirect(
          `${origin}/${'profile?tab=account-settings&error=failed_event_creation'}`
        );
      }
    }
    if (!error) {
      return NextResponse.redirect(
        `${origin}/${'profile?tab=account-settings'}`
      );
    }
  }

  return NextResponse.redirect(`${origin}/auth/auth-code-error`);
}

export async function PATCH(request: Request) {
  const cookieStore = await cookies();
  const supabase = createSupabaseServer();
  const cookie: any = await cookieStore.get('user_data');
  const userId = JSON.parse(cookie.value)?.id;
  const body = await request.json();

  const { data, error } = await supabase
    .from(tableNames.users)
    .update(body)
    .eq('id', userId)
    .select('*')
    .single();

  if (error) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }
  await adminSupabase.rpc('get_user_by_email', {
    user_email: data?.email,
  });

  return NextResponse.json({ data }, { status: 200 });
}
