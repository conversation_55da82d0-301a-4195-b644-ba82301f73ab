'use client';
import soapLogo from '@/assets/soapLogo.png';
import CustomDatePicker from '@/components/elements/date-picker/date-picker';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { ProgressBar, ProgressRoot } from '@/components/ui/progress';
import { Radio, RadioGroup } from '@/components/ui/radio';
import { Image } from '@chakra-ui/react';

import useMultiPageFormHook from '@/hooks/forms/useSubmitNewFormHook';
import {
  Box,
  Flex,
  HStack,
  Spinner,
  Stack,
  Text,
  Textarea,
} from '@chakra-ui/react';

const MultiPageForm = ({ formDetails }: { formDetails: any }) => {
  const {
    formik,
    isLoading,
    currentPage,
    pages,
    currentPageQuestions,
    isFirstPage,
    isLastPage,
    // isPageSubmitted,
    goToPage,
    // goToNextPage,
    goToPreviousPage,
  } = useMultiPageFormHook({ formDetails });

  console.log('formDetails');

  const progressPercentage = ((currentPage - 1) / (pages.length - 1)) * 100;
  const isSinglePage = pages.length === 1;

  return (
    <Box width={'100%'} p={4} h={'100%'}>
      {/* Progress Bar - Only show for multi-page forms */}
      {!isSinglePage && (
        <Box mb={6}>
          <Flex justify="space-between" align="center" mb={2}>
            <Text fontSize="sm" color="gray.600">
              Page {currentPage} of {pages.length}
            </Text>
            <Text fontSize="sm" color="gray.600">
              {Math.round(progressPercentage)}% Complete
            </Text>
          </Flex>

          <ProgressRoot
            shape="rounded"
            value={progressPercentage || 0}
            colorPalette="orange"
            size="lg"
          >
            <ProgressBar rounded=".9rem" />
          </ProgressRoot>
        </Box>
      )}

      {/* Page Navigation - Only show for multi-page forms */}
      {!isSinglePage && (
        <Box mb={6}>
          <HStack spaceY={2}>
            {pages.map((pageNum: any) => (
              <Button
                key={pageNum}
                size="sm"
                variant={currentPage === pageNum ? 'solid' : 'outline'}
                colorScheme={currentPage === pageNum ? 'blue' : 'gray'}
                onClick={() => goToPage(pageNum)}
              >
                {pageNum}
              </Button>
            ))}
          </HStack>
        </Box>
      )}

      {/* Form Title - Show for single page forms */}
      <Box mb={6} textAlign="center">
        <Text fontSize="2xl" fontWeight="bold" color="gray.700">
          {formDetails.title}
        </Text>
        {formDetails.description && (
          <Text fontSize="md" color="gray.600" mt={2}>
            {formDetails.description}
          </Text>
        )}
      </Box>

      {/* Form Content */}
      <form
        onSubmit={formik.handleSubmit}
        style={{
          width: '90%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '0 auto',
        }}
      >
        {/* Current Page Questions */}
        {currentPageQuestions.map((question: any) => {
          const fieldName = `question_${question.id}`;
          const isTouched = formik.touched[fieldName];
          const errorMessage =
            typeof formik.errors[fieldName] === 'string'
              ? formik.errors[fieldName]
              : '';

          // Textbox or Number Input
          if (question.type === 'Textbox' || question.type === 'Number') {
            return (
              <Box
                key={question.id}
                width="100%"
                mb={8}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Flex align="center" mb={3}>
                  <Text fontSize="md" fontWeight="500" color="gray.800">
                    {question.qt}
                  </Text>
                  {question.required && (
                    <Text as="span" color="red.500" ml={1}>
                      *
                    </Text>
                  )}
                </Flex>

                <StringInput
                  inputProps={{
                    name: fieldName,
                    type: question.type === 'Textbox' ? 'text' : 'number',
                    value: formik.values[fieldName] || '',
                    onChange: formik.handleChange,
                    onBlur: formik.handleBlur,
                    placeholder: question.placeholder || '',
                  }}
                  fieldProps={{
                    invalid: isTouched && !!errorMessage,
                    errorText: isTouched && errorMessage ? errorMessage : '',
                    required: question.required,
                  }}
                />
              </Box>
            );
          }

          // TextArea Input
          else if (question.type === 'TextArea') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Flex align="center" mb={3}>
                  <Text fontSize="md" fontWeight="500" color="gray.800">
                    {question.qt}
                  </Text>
                  {question.required && (
                    <Text as="span" color="red.500" ml={1}>
                      *
                    </Text>
                  )}
                </Flex>

                <Textarea
                  name={fieldName}
                  value={formik.values[fieldName] || ''}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder={question.placeholder || ''}
                  minH="120px"
                  resize="vertical"
                />
                {isTouched && errorMessage && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errorMessage}
                  </Text>
                )}
              </Box>
            );
          }

          // Single Choice (Radio Buttons)
          else if (question.type === 'Single choice') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Text fontWeight="500" mb={2}>
                  {question.qt}
                  {question.required && (
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  )}
                </Text>

                <RadioGroup
                  cursor={'pointer'}
                  name={fieldName}
                  onValueChange={(val) =>
                    formik.setFieldValue(fieldName, val.value)
                  }
                  value={formik.values[fieldName] || ''}
                >
                  <Stack direction="column" gap={2}>
                    {question.options.map((option: string, index: number) => (
                      <Radio key={index} value={option}>
                        <Text fontSize="12px" cursor={'pointer'}>
                          {option}
                        </Text>
                      </Radio>
                    ))}
                  </Stack>
                </RadioGroup>
                {isTouched && errorMessage && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errorMessage}
                  </Text>
                )}
              </Box>
            );
          }

          // Multiple Choice (Checkboxes)
          else if (question.type === 'Multiple choice') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                textTransform={'capitalize'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Text fontWeight="500" mb={2}>
                  {question.qt}
                  {question.required && (
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  )}
                </Text>

                <Stack direction="column" gap={2}>
                  {question.options.map((option: string, index: number) => (
                    <HStack
                      key={index}
                      align="center"
                      cursor="pointer"
                      onClick={() => {
                        let newValues = formik.values[fieldName] || [];
                        const isChecked =
                          formik.values[fieldName]?.includes(option) || false;
                        newValues = !isChecked
                          ? [...newValues, option]
                          : newValues.filter((val: string) => val !== option);
                        formik.setFieldValue(fieldName, newValues);
                      }}
                    >
                      <input
                        type="checkbox"
                        name={fieldName}
                        value={option}
                        checked={
                          formik.values[fieldName]?.includes(option) || false
                        }
                        onChange={(e) => {
                          let newValues = formik.values[fieldName] || [];
                          newValues = e.target.checked
                            ? [...newValues, option]
                            : newValues.filter((val: string) => val !== option);
                          formik.setFieldValue(fieldName, newValues);
                        }}
                        style={{
                          accentColor: 'black',
                          width: '20px',
                          height: '20px',
                          cursor: 'pointer',
                          pointerEvents: 'none', // Prevents double-firing
                        }}
                      />
                      <Text fontSize="12px" fontWeight={'450'}>
                        {option}
                      </Text>
                    </HStack>
                  ))}
                </Stack>
                {isTouched && errorMessage && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errorMessage}
                  </Text>
                )}
              </Box>
            );
          }

          // Date Picker
          else if (question.type === 'Date') {
            return (
              <Box
                mb={9}
                key={question.id}
                width={'100%'}
                p={4}
                borderColor="gray.100"
                borderRadius="md"
                bg="white"
                boxShadow="sm"
              >
                {question.title && (
                  <Text fontSize="lg" fontWeight="600" color="gray.700" mb={2}>
                    {question.title}
                  </Text>
                )}

                {question.description && (
                  <Text
                    fontSize="sm"
                    color="gray.500"
                    mb={3}
                    lineHeight="short"
                  >
                    {question.description}
                  </Text>
                )}

                <Text fontWeight="500" mb={2}>
                  {question.qt}
                  {question.required && (
                    <Text as="span" color="red.500">
                      *
                    </Text>
                  )}
                </Text>

                <CustomDatePicker
                  onChange={(e: any) => formik.setFieldValue(fieldName, e)}
                  clearDefault={true}
                />
                {isTouched && errorMessage && (
                  <Text color="red.500" fontSize="sm" mt={1}>
                    {errorMessage}
                  </Text>
                )}
              </Box>
            );
          }

          return null;
        })}

        {/* Navigation Buttons */}
        <Flex
          justify={isSinglePage ? 'center' : 'space-between'}
          width="100%"
          mt={6}
          mb={4}
        >
          {!isSinglePage && (
            <Button
              variant="outline"
              onClick={goToPreviousPage}
              disabled={isFirstPage}
              width="120px"
            >
              Previous
            </Button>
          )}

          <Button
            type="submit"
            colorScheme="blue"
            disabled={isLoading}
            width={isSinglePage ? '200px' : '120px'}
          >
            {isLoading ? (
              <Spinner size="sm" />
            ) : isSinglePage ? (
              'Submit Form'
            ) : isLastPage ? (
              'Submit Form'
            ) : (
              'Submit & Next'
            )}
          </Button>
        </Flex>

        {/* Page Info */}
        <Text fontSize="sm" color="gray.500" textAlign="center">
          {isSinglePage
            ? 'Please fill out all required fields before submitting.'
            : isLastPage
              ? 'This is the final page. Click "Submit Form" to complete.'
              : 'Each page saves automatically. You can navigate between pages after submission.'}
        </Text>
      </form>

      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        width={'100%'}
        mt={6}
      >
        <Text fontSize={'12px'}>Powered by</Text>
        <Image src={soapLogo.src} alt="logo" w={'35'} h={'10'} ml={2} />
      </Box>
    </Box>
  );
};

export default MultiPageForm;
