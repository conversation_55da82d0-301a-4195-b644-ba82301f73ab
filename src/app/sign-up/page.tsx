import React from 'react';
import Signup from './Signup';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getUserByEmail } from '../service/user';
import { Metadata } from 'next';
import { getOrganizationName } from '@/utils/server-cookie-helper';

export async function generateMetadata(): Promise<Metadata> {
  const organization_name = getOrganizationName();
  const fullTitle = `Soap - ${organization_name} - SLPs`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default async function page() {
  const { auth } = createSupabaseServer();
  const user = await auth.getUser();
  const userFromServer = await getUserByEmail(
    user?.data?.user?.email as string
  );
  return <Signup userFromServer={userFromServer} />;
}
