'use client';
import { Stack, Flex, Icon, Box, Text } from '@chakra-ui/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FaAngleRight } from 'react-icons/fa6';

export default function DesktopSubNav({
  label,
  href,
  subLabel,
  handleClick,
}: any) {
  const pathName = usePathname();
  const isActive = pathName === href;

  return (
    <Box
      className="group"
      display={'block'}
      py={2}
      rounded={'md'}
      bg={isActive ? 'pink.50' : ''}
      _hover={{ bg: 'pink.50' }}
      w={'100%'}
      onClick={handleClick}
    >
      <Link href={href} prefetch={true}>
        <Stack direction={'row'} align={'center'}>
          <Box w={'80%'}>
            <Flex align="center" gap={3} pl={'3'}>
              <Text
                transition={'all .3s ease'}
                _groupHover={subLabel ? { color: '#e97a5b' } : undefined}
                fontWeight={400}
                color={isActive ? '#e97a5b' : ''}
                whiteSpace="nowrap"
              >
                {label}
              </Text>
              {subLabel && (
                <Text pl={'3'} pt={'5'} fontSize={'sm'} truncate>
                  {subLabel}
                </Text>
              )}
            </Flex>
          </Box>
          <Flex
            transition={'all .3s ease'}
            transform={'translateX(-10px)'}
            _groupHover={{ opacity: '100%', transform: 'translateX(-5px)' }}
            justify={'flex-end'}
            opacity={isActive ? '100%' : '0'}
            align={'center'}
            flex={1}
          >
            {/* <Icon color={'#e97a5b'} w={3} h={3}>
              <FaAngleRight />
            </Icon> */}
            <Icon as={FaAngleRight} color={'#e97a5b'} w={3} h={3} />
          </Flex>
        </Stack>
      </Link>
    </Box>
  );
}
