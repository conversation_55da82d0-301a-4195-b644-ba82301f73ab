import { CustomModal } from '@/components/elements/modal/custom-modal';
import { generateAndDownloadPDF } from '@/components/elements/pdf/Generate-PDF';
import SearchContact from '@/components/elements/search/SearchContact';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import {
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuSeparator,
  MenuTrigger,
} from '@/components/ui/menu';
import { toaster } from '@/components/ui/toaster';
import { useEditInvoiceHook } from '@/hooks/billing/invoice/useEditInvoiceHook';
import {
  Box,
  chakra,
  Flex,
  Grid,
  GridItem,
  Heading,
  HStack,
  Separator,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import 'moment-timezone';
import 'moment/locale/en-ca';
import moment from 'moment/moment';
import Link from 'next/link';
import { BsThreeDotsVertical, BsX } from 'react-icons/bs';
import { CreateAndSendPDF } from './CreateAndSendEmail';
import DeleteInvoice from './DeleteInvoice';
import LinkTransactions from './LinkTransactions';

export default function EditInvoice({
  invoice,
  color,
}: {
  invoice: any;
  color?: any;
}) {
  const { open, onClose, onOpen } = useDisclosure();
  const CSEmailDisclosure = useDisclosure();
  const deleteDisclosure = useDisclosure();

  const handleDownload = async () => {
    const { transactions, total_price } = invoice ?? {};

    const totalPaid = transactions?.reduce(
      (prev: number, currentTransaction: any) => {
        return prev + Number(currentTransaction?.amount || 0);
      },
      0
    );
    const amountDue = Number(total_price) - Number(totalPaid);
    try {
      await generateAndDownloadPDF({
        name: String(invoice?.clients?.display_name || invoice?.name),
        receiptNumber: String(invoice?.invoice_number),
        date: String(moment(invoice?.invoice_date).format('YYYY-MM-DD')),
        activity: invoice?.services ? String(invoice?.services?.name) : '',
        quantity: Number(invoice?.qty),
        rate: Number(invoice?.total_price),
        balance: 0,
        memo: String(invoice?.memo || ''),
        referral: invoice?.referral,
        amountDue: amountDue,
        dueDate: invoice?.due_date,
        email: invoice?.email,
        transactions,
      });
      toaster.create({
        type: 'success',
        description: `PDF succesfully downloaded`,
      });
    } catch (error) {
      toaster.create({
        type: 'error',
        description: `Failed to download PDF: ${error}`,
      });
    }
  };

  return (
    <div data-no-row-click="true">
      <MenuRoot data-no-row-click="true" positioning={{ placement: 'bottom' }}>
        <MenuTrigger cursor={'pointer'}>
          <BsThreeDotsVertical />
        </MenuTrigger>
        <MenuContent cursor={'pointer'}>
          {invoice?.organization_id == 1 ? (
            <MenuItem value="edit" onClick={onOpen} color={color}>
              Edit
            </MenuItem>
          ) : (
            <Link href={`/invoices/${invoice?.id}`} passHref>
              <MenuItem
                data-no-row-click="true"
                as="a"
                value="view"
                color={color}
                cursor={'pointer'}
              >
                View
              </MenuItem>
            </Link>
          )}

          {invoice?.organization_id !== 1 && (
            <>
              {/* <MenuSeparator /> */}
              <Link
                style={{ display: 'none' }}
                href={`/invoices/edit/${invoice?.id}`}
                passHref
              >
                <MenuItem as="a" value="edit" color={color} cursor={'pointer'}>
                  Edit
                </MenuItem>
              </Link>
            </>
          )}

          {invoice?.organization_id == 1 ? (
            <>
              <MenuSeparator />
              <MenuItem value="generatePDF" onClick={handleDownload}>
                Generate PDF
              </MenuItem>
              <MenuSeparator />
              <MenuItem value="Send Email" onClick={CSEmailDisclosure.onOpen}>
                Create and Send Email
              </MenuItem>
              <MenuSeparator />
              <MenuItem value="link-transaction">
                <div onClick={(e) => e.stopPropagation()}>
                  <LinkTransactions invoice={invoice} />
                </div>
              </MenuItem>
              <MenuSeparator />
              <MenuItem
                onClick={deleteDisclosure.onOpen}
                value="delete"
                color={'red'}
                data-no-row-click="true"
              >
                Delete
              </MenuItem>
            </>
          ) : // this is for new organization flow
          // <Link href={`/link-invoice/${invoice?.id}`}>
          //   Send Email
          // </Link>

          null}
        </MenuContent>
      </MenuRoot>

      {open && <EditModal isOpen={open} onClose={onClose} invoice={invoice} />}
      {CSEmailDisclosure.open && (
        <CreateAndSendPDF
          isOpen={CSEmailDisclosure.open}
          onCloseModal={CSEmailDisclosure.onClose}
          invoice={invoice}
          isInvoiceLoading={false}
        />
      )}
      {deleteDisclosure.open && (
        <DeleteInvoice
          open={deleteDisclosure.open}
          onClose={deleteDisclosure.onClose}
          row={invoice}
        />
      )}
    </div>
  );
}

function EditModal({ invoice, onClose, isOpen }: any) {
  const {
    handleFormSubmit,
    values,
    handleChange,
    handleBlur,
    setFieldValue,
    touched,
    statusOptions,
    sessionTypeOptions,
    errors,
    slpOptions,
    updateLoading,
    sessionDuration,
    searchResult,
    setSearchResult,
    selectExistingUser,
    linked,
    setLinked,
    slpInfo,
    user,
  } = useEditInvoiceHook({ invoice: invoice as any, onClose });

  return (
    <CustomModal w={'30rem'} onOpenChange={onClose} open={isOpen}>
      <Box>
        <Text textAlign={'center'} fontWeight={'semibold'} mb={'2rem'}>
          Edit Invoice {invoice.invoice_number}
        </Text>
        <chakra.form onSubmit={handleFormSubmit}>
          <Stack alignItems={'center'}>
            <Box width={'100%'}>
              <label className="font-medium text-gray-900">Lookup Client</label>
              <SearchContact
                // value={
                //   user ? `${user?.first_name} ${user?.last_name}` : invoice.name
                // }
                setSearchResult={(e: any) => {
                  setSearchResult(e);
                }}
                selectExistingUser={selectExistingUser}
                searchResult={searchResult}
              />
            </Box>

            <StringInput
              inputProps={{
                name: 'Name',
                value: user
                  ? `${user?.first_name} ${user?.last_name}`
                  : invoice.name,
                readOnly: true,
              }}
              fieldProps={{
                label: 'Client Name',
              }}
            />
            <StringInput
              inputProps={{
                name: 'product',
                onChange: handleChange,
                value: values.product as any,
                onBlur: handleBlur,
              }}
              fieldProps={{
                label: 'Product Description',
              }}
            />

            <StringInput
              inputProps={{
                name: 'invoice_date',
                onChange: handleChange,
                value: values.invoice_date?.split('T')[0],
                onBlur: handleBlur,
                type: 'date',
              }}
              fieldProps={{
                label: 'Invoice Date',
              }}
            />

            <Grid templateColumns="repeat(2, 1fr)" gap={6} width="100%">
              <GridItem>
                <StringInput
                  fieldProps={{
                    label: 'Qty',
                    invalid: touched.qty && !!errors.qty,
                    required: true,
                    errorText: errors?.qty as any,
                  }}
                  inputProps={{
                    name: 'qty',
                    onChange: handleChange,
                    value: values.qty as any,
                    onBlur: handleBlur,
                  }}
                  // error={!!errors.qty}
                  // errorMessage={errors?.qty}
                  // touched={touched.qty}
                />
              </GridItem>
              <GridItem>
                <Box w={'100%'}>
                  <CustomSelect
                    placeholder="Session Type"
                    options={sessionTypeOptions || []}
                    onChange={(val) => {
                      setFieldValue('session_type', val.value);
                    }}
                    label="Session Type"
                    defaultValue={
                      sessionTypeOptions &&
                      sessionTypeOptions?.find(
                        (item: any) => item?.value === values.session_type
                      )
                    }
                  />
                </Box>
              </GridItem>
            </Grid>

            <Grid templateColumns="repeat(2, 1fr)" gap={6} width="100%">
              <GridItem>
                <CustomSelect
                  placeholder="Total Minutes"
                  onChange={(val) => setFieldValue('total_hours', val?.value)}
                  options={sessionDuration}
                  required={true}
                  defaultValue={sessionDuration?.find(
                    (item: any) => Number(item.value) === values?.total_hours
                  )}
                  label="Total Minutes"
                />
              </GridItem>
              <GridItem>
                <StringInput
                  inputProps={{
                    type: 'number',
                    name: 'total_price',
                    onChange: handleChange,
                    value: values.total_price as any,
                    onBlur: handleBlur,
                  }}
                  fieldProps={{
                    label: 'Total Price',
                    invalid: touched.total_price && !!errors.total_price,
                    required: true,
                    errorText: errors?.total_price as any,
                  }}
                />
              </GridItem>
            </Grid>

            <StringInput
              inputProps={{
                name: 'memo',
                onChange: handleChange,
                value: values.memo as any,
                onBlur: handleBlur,
              }}
              fieldProps={{
                label: 'Memo',
              }}
            />
            <StringInput
              inputProps={{
                name: 'invoice_number',
                onChange: handleChange,
                value: values.invoice_number as any,
                onBlur: handleBlur,
              }}
              fieldProps={{
                label: 'Invoice Number',
              }}
            />

            <Box w={'100%'}>
              <CustomSelect
                placeholder="Slp"
                options={slpOptions || []}
                onChange={(val) => {
                  setFieldValue('slp_id', val.value);
                }}
                label="SLP"
                defaultValue={
                  slpOptions &&
                  slpOptions?.find((item: any) => item?.value === values.slp_id)
                }
              />
            </Box>

            <Box w={'100%'}>
              <CustomSelect
                placeholder="Unchanged"
                options={statusOptions}
                onChange={(val) => {
                  setFieldValue('status', val.value);
                }}
                label="Status"
                defaultValue={statusOptions.find(
                  (item: any) =>
                    item.value.toLowerCase() === values?.status.toLowerCase()
                )}
              />
            </Box>

            <CustomTextArea
              inputProps={{
                name: 'change_reason',
                onChange: handleChange,
                value: values.change_reason as any,
                onBlur: handleBlur,
              }}
              fieldProps={{
                label: 'Change Reason',
                invalid: touched.change_reason && !!errors.change_reason,
                errorText: errors?.change_reason as any,
              }}
              // errorMessage={errors?.change_reason}
              // touched={touched.change_reason}
            />
          </Stack>
          <Separator mt={'1rem'} />

          {invoice.slp_notes?.length > 0 && linked ? (
            <Box mt={'.5rem'} w={'100%'}>
              <HStack justifyContent={'space-between'}>
                <Heading fontSize={'22px'}>SLP Note Info</Heading>
                <Box onClick={() => setLinked(!linked)}>
                  <BsX size={24} color="red" />
                </Box>
              </HStack>
              <HStack
                justifyContent={'space-between'}
                alignItems={'flex-start'}
              >
                <Stack width={'100%'} gap={2}>
                  <HStack mt={'.4rem'}>
                    <Text fontWeight={600}>Client Name:</Text>
                    <Text>{`${invoice?.name}`}</Text>
                  </HStack>
                  <HStack alignItems={'flex-start'}>
                    <Text fontWeight={600}>Product:</Text>
                    <Text> {invoice?.product}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight={600}>Memo:</Text>
                    <Text> {invoice?.slp_notes?.[0]?.invoice_memo}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight={600}>SLP:</Text>
                    <Text> {slpInfo?.email}</Text>
                  </HStack>
                  <HStack>
                    <Text fontWeight={600}>Session Date:</Text>
                    <Text>
                      {' '}
                      {moment(invoice?.invoice_date)
                        .utc()
                        .format('MMMM D, YYYY')}
                    </Text>
                  </HStack>
                </Stack>
              </HStack>
            </Box>
          ) : (
            <Box w={'100%'}>
              <Text color={'red'} fontWeight={500}>
                No SLP notes found
              </Text>
            </Box>
          )}
          <Flex
            my={'2rem'}
            alignItems={'center'}
            justifyContent={'space-between'}
          >
            <Button onClick={onClose} bg={'gray'} _hover={{ bg: 'gray' }}>
              Cancel
            </Button>
            <Button bg={'primary.500'} loading={updateLoading} type="submit">
              Update
            </Button>
          </Flex>
        </chakra.form>
      </Box>
    </CustomModal>
  );
}
