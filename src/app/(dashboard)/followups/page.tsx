import React from 'react';

import dynamic from 'next/dynamic';
import { Metadata } from 'next';
import { Box } from '@chakra-ui/react';
import { getOrganizationName } from '@/utils/server-cookie-helper';

const ViewAllFollowUps = dynamic(
  () => import('./view-all-followups/view-all'),
  {
    ssr: false,
  }
);

export async function generateMetadata(): Promise<Metadata> {
  const organization_name = getOrganizationName();
  const fullTitle = `Soap - ${organization_name} - SLPs`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}

export default function page() {
  return (
    <Box>
      <ViewAllFollowUps />
    </Box>
  );
}
