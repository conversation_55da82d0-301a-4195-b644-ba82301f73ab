import React from 'react';
import AllUsers from './all-users/AllUsers';
import { Metadata } from 'next';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default function page() {
  return (
    <div style={{ paddingTop: '30px', paddingBottom: '30px' }}>
      <AllUsers />
    </div>
  );
}
