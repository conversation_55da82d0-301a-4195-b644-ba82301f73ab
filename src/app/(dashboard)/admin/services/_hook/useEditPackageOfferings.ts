import { useGetAllTaxesQuery } from '@/api/newsf/queries';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { PackageStatus, ProcessStatus } from '@/utils/enums';
import { useDisclosure } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import * as Yup from 'yup';

export const useEditPackageOfferings = (data: any, userFromServer: any) => {
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(false);
  const id = data?.client_id as string;

  const { data: ServicesData } = useGetServicesQuery(
    userFromServer?.organization_id,
    {
      enabled: !!userFromServer?.organization_id,
    }
  );

  const [refundDate, setRefundDate] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const searchParams = useSearchParams();
  const orgIdFromUrl = searchParams.get('organization_id');
  const userIdFromUrl = searchParams.get('user_id');

  const {
    open: isDeleteOpen,
    onClose: onDeleteClose,
    onOpen: onDeleteOpen,
  } = useDisclosure();
  const {
    open: openEdit,
    onClose: onEditClose,
    onOpen: onEditOpen,
  } = useDisclosure();

  const formatDate = (dateStr: any) => {
    // Handle ISO string format (e.g., "2024-11-13T23:00:00Z")
    if (dateStr.includes('T')) {
      return dateStr.split('T')[0];
    }

    // Handle "YYYY-MM-DD HH:mm:ss" format
    if (dateStr.includes(' ')) {
      return dateStr.split(' ')[0];
    }

    // Return the original string if it doesn't match the expected formats
    return dateStr;
  };
  const servicesOption = useMemo(
    () =>
      ServicesData?.services?.map((item: any) => ({
        label: item?.name,
        value: item,
      })) || [],
    [ServicesData]
  );
  const { data: TaxData } = useGetAllTaxesQuery(
    {
      user_id: userIdFromUrl || userFromServer.id || '',
      org_id: orgIdFromUrl || userFromServer?.organization?.id,
    },
    {
      enabled: !!userIdFromUrl || !!userFromServer.organization_id,
      generateItemId: true,
    }
  );

  interface ServiceItem {
    id?: any;
    name: string;
    price?: number;
    quantity: number;
    edited?: boolean;
    deleted?: boolean;
    description?: string;
    status?: ProcessStatus;
    package_item_id?: any;
    organization_id?: number;
  }
  const initialValues = {
    tax_id: data?.tax_id,
    amount: data?.amount,
    discount: data?.discount,
    price: data?.price || null,
    description: data?.description,
    package_name: data?.package_name,
    status: data?.status || PackageStatus.ACTIVE,
    expiry_date: data?.expiry_date || null,
    organization_id: data?.organization_id,
    name: data?.name || '',
    services: data?.services as ServiceItem[],
    can_expire: data?.can_expire ? true : false,
    expiry_duration: data?.expiry_duration || null,
    expiry_period: data?.expiry_period || null,
  };

  const statusOptions = [
    {
      label: PackageStatus.ACTIVE,
      value: PackageStatus.ACTIVE,
    },
    {
      label: PackageStatus.DELETED,
      value: PackageStatus.DELETED,
    },
  ];

  const validationSchema = Yup.object({
    price: Yup.number()
      .typeError('Price must be a number')
      .required('Price is required'),
    services: Yup.array()
      .required('Service is required')
      .min(1, 'At least one service is required'),
    name: Yup.string().trim(),
    description: Yup.string().trim(),
  });

  const {
    values,
    handleSubmit,
    errors,
    touched,
    handleChange,
    setFieldValue,
    handleBlur,
    setErrors,
  } = useFormik({
    initialValues: initialValues,
    enableReinitialize: true,
    validationSchema, // Attach the validation schema
    onSubmit: async (values) => {
      try {
        if (!values?.services?.length) {
          setErrors({
            services: 'At least one service is required',
          });
          throw new Error('Please Select at least one Service!');
        }

        console.log('values', values);
        const insert = {
          status: values.status,
          description: values.description,
          tax_id: values.tax_id,
          price: Number(values.price),
          amount: Number(values.amount),
          discount: Number(values?.amount) - Number(values?.price),
          name: values.name,
          can_expire: values?.can_expire,
          expiry_duration: values?.expiry_duration,
          expiry_period: values?.expiry_period,
        };

        console.log('insert', insert);

        setLoading(true);

        if (data) {
          const { error } = await supabase
            .from(tableNames.package_offering)
            .update(insert)
            .eq('id', data?.id as string);
          if (error) throw error;

          const deleteServices = values?.services?.filter((service) => {
            return (
              [ProcessStatus.OLD].includes(service.status!) && service?.deleted
            );
          });

          const updatedServices = values?.services?.filter((service) => {
            return (
              [ProcessStatus.OLD].includes(service.status!) && service?.edited
            );
          });

          const newServices = values.services?.filter((service) => {
            return [ProcessStatus.NEW].includes(service.status!);
          });

          const newServicePromise = newServices?.map((service) =>
            supabase?.from(tableNames?.package_items).insert({
              quantity: service?.quantity,
              service_id: service?.id,
              package_offering_id: data?.id,
            })
          );
          const updatedServicePromise = updatedServices?.map((service) =>
            supabase
              ?.from(tableNames?.package_items)
              .update({
                quantity: service?.quantity,
              })
              .eq('id', service?.package_item_id)
          );

          const deletedServicePromise = deleteServices?.map((service) =>
            supabase
              ?.from(tableNames?.package_items)
              .delete()
              .eq('id', service?.package_item_id)
          );

          await Promise.all(newServicePromise);
          await Promise.all(updatedServicePromise);
          await Promise.all(deletedServicePromise);

          await queryClient.invalidateQueries({
            queryKey: [
              queryKey.packages.getPackageOfferings,
              userFromServer.id,
              userFromServer.organization_id,
            ],
            exact: false,
          });

          onEditClose();
          toaster.create({
            description: 'Package Updated Successfully ',
            type: 'success',
          });
        }
      } catch (error) {
        setLoading(false);
        toaster.create({
          description: 'Something went wrong.',
          type: 'error',
        });
        console.error(error);
      } finally {
        setLoading(false);
      }
    },
  });

  const submitEdit = (e: any) => {
    console.log('submiting', errors);
    e?.preventDefault();
    handleSubmit();
  };
  const handleDeletePackage = async () => {
    try {
      setDeleteLoading(true);
      if (data?.id) {
        const { error } = await supabase
          .from(tableNames.packages)
          .update({ status: 'DELETED' })
          .eq('id', data?.id as string);
        if (error) throw error;
        await queryClient.invalidateQueries({
          queryKey: [queryKey.client.getById, id],
        });
        toaster.create({
          type: 'success',
          description: 'Package Deleted Successfully',
        });
      } else {
        throw new Error('Invalid Data');
      }
    } catch (error: any) {
      toaster.create({
        type: 'error',
        description: error?.message,
      });
    } finally {
      setDeleteLoading(false);
      onDeleteClose();
    }
  };

  const onServiceClick = (service: ServiceItem) => {
    // find duplicate service
    const tempServices = values.services;
    const targetIndex = tempServices?.findIndex(
      (item) => item?.id === service?.id
    );
    if (targetIndex > -1) {
      const targetService = tempServices[targetIndex];

      console.log('targetService', targetService);
      tempServices?.splice(targetIndex, 1, {
        ...targetService,
        quantity: targetService?.quantity + 1,
        edited: true,
        deleted: targetService?.deleted ? false : targetService?.deleted,
      });
    } else {
      tempServices.push({ ...service, quantity: 1, status: ProcessStatus.NEW });
    }
    setFieldValue('services', [...tempServices]);
    computeTotalPrice(tempServices);
  };

  const computeTotalPrice = (services: ServiceItem[]) => {
    const multiples = services.map((service: ServiceItem) => {
      const result = service.price! * service.quantity;
      return result;
    });
    const sum = multiples.reduce((previousValue, currentValue) => {
      const sum = previousValue + currentValue;
      return sum;
    }, 0);
    setFieldValue('price', sum);
    setFieldValue('amount', sum);

    // discount
    // const currentAmount = Number(values.amount) || 0;
    // if (!values.amount || currentAmount === sum) {
    //   setFieldValue('amount', sum);
    //   setFieldValue('discount', '');
    // } else {
    //   const discount = sum - currentAmount;
    //   setFieldValue('discount', discount > 0 ? discount : '');
    // }
  };

  const handleRemoveService = (serviceId: string) => {
    const services = values.services.map((service) => {
      if (service.id !== serviceId) {
        return service;
      }
      return {
        ...service,
        quantity: 0,
        deleted: true,
      };
    });
    setFieldValue('services', services);
    computeTotalPrice(services);
  };

  const handleQuantityChange = (serviceId: string, newQuantity: number) => {
    console.log('handleQuantityChange', serviceId, newQuantity);
    const services = values.services.map((service) =>
      service.id === serviceId
        ? { ...service, quantity: newQuantity, edited: true }
        : service
    );
    setFieldValue('services', services);
    computeTotalPrice(services);
  };
  const handleExpiry = () => {
    if (values?.can_expire) {
      return setFieldValue('can_expire', false);
    }
    setFieldValue('expiry_duration', 1);
    setFieldValue('expiry_period', 'Year(s)');
    return setFieldValue('can_expire', true);
  };
  useEffect(() => {
    if (values.amount < values.price) {
      const discount = values.price - values.amount;
      setFieldValue('discount', discount);
    } else {
      setFieldValue('discount', 0);
    }
  }, [setFieldValue, values.amount, values.price]);

  return {
    loading,
    setFieldValue,
    values,
    errors,
    handleExpiry,
    touched,
    formatDate,
    handleSubmit,
    handleBlur,
    submitEdit,
    servicesOption,
    handleChange,
    statusOptions,
    setRefundDate,
    refundDate,
    handleDeletePackage,
    deleteLoading,
    onDeleteClose,
    onDeleteOpen,
    isDeleteOpen,
    handleQuantityChange,
    handleRemoveService,
    onServiceClick,
    onEditClose,
    onEditOpen,
    openEdit,
    TaxData,
  };
};
