import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

export const useServices = (slp: any, status: string[] = ['ACTIVE']) => {
  const router = useRouter();
  const validTabs = useMemo(() => ['services', 'packages'], []);
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(() => {
    const tabFromUrl = searchParams.get('tab') || 'services';
    return validTabs.includes(tabFromUrl) ? tabFromUrl : 'services';
  });

  const { data: ServicesData, isLoading: ServicesLoading } =
    useGetServicesQuery(slp?.organization_id, {
      enabled: !!slp?.organization_id,
      status: status.join(','),
    });

  const handleValueChange = (details: { value: string }) => {
    setActiveTab(details.value);
  };

  useEffect(() => {
    // Check if organization_id exists in the URL
    const organizationId = searchParams.get('organization_id');
    if (organizationId) {
      // console.log('Organization ID exists in the URL:', organizationId);
      return; // Skip the effect if organization_id exists
    }

    if (!validTabs.includes(activeTab)) {
      setActiveTab('services');
    } else {
      const newUrl = `/admin/services`;
      router.replace(newUrl, { scroll: false });
    }
  }, [activeTab, router, validTabs, searchParams]);

  return { ServicesData, ServicesLoading, handleValueChange, activeTab };
};

export type TUserServiceHook = ReturnType<typeof useServices>;
