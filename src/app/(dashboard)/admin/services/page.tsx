import { getUserByEmail } from '@/app/service/user';
import { createSupabaseServer } from '@/lib/supabase/server';
import { Metadata } from 'next';
import AllServices from './all-services/AllServices';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

const page = async () => {
  const { auth } = createSupabaseServer();
  const user = await auth.getUser();
  const userFromServer = await getUserByEmail(
    user?.data?.user?.email as string
  );
  return (
    <div>
      {/* <CreateProduct /> */}
      <AllServices slp={userFromServer} />
    </div>
  );
};

export default page;
