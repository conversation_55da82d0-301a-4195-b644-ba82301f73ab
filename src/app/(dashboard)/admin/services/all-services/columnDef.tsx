import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import Status from '@/components/elements/status/Status';
import { Box } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import Actions from './Actions';
// import EditBookingModal from './EditBookingModal';

const columnHelper = createColumnHelper<any>();

export const createColumnDef = () => [
  columnHelper.accessor('name', {
    cell: (info) => (
      <Box w={'10rem'} whiteSpace={'break-spaces'}>
        {info.getValue()}
      </Box>
    ),
    header: 'Name',
    id: 'name',
  }),
  columnHelper.accessor('description', {
    cell: (info) => (
      <Box w={'25rem'} whiteSpace="break-spaces">
        {info.getValue()}
      </Box>
    ),
    header: 'Description',
    id: 'description',
  }),
  columnHelper.accessor('price', {
    cell: (info) => <Box>{formatMoney(info.getValue())}</Box>,
    header: 'Price',
    id: 'price',
  }),
  columnHelper.accessor('duration_minutes', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Minutes',
    id: 'minutes',
  }),

  columnHelper.accessor('status', {
    cell: (info) => (
      <Box>
        <Status name={info.getValue() as string} />
      </Box>
    ),
    header: 'Status',
    id: 'status',
  }),

  columnHelper.display({
    id: 'edit-service',
    cell: (props) => (
      <Box>
        <Actions data={props.row.original} />
      </Box>
    ),
    header: 'Action',
  }),
];
