'use client';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTable from '@/components/table/CustomTable';
import { IGetServicesFilterState } from '@/store/filters/services';
import {
  Box,
  Center,
  Flex,
  Heading,
  Tabs,
  Text,
  VStack,
} from '@chakra-ui/react';
import { getCoreRowModel, getSortedRowModel } from '@tanstack/react-table';
import { useRecoilState } from 'recoil';
import Packages from '../packages/Packages';
import AddNewServiceModal from './AddNewServiceModal';
import { createColumnDef } from './columnDef';
// import PackageTransaction from '../package-transactions/PackageTransaction';
import { useServices } from '../_hook/useServices';
import EmptyState from '@/components/elements/EmptyState';
import { Switch } from '@/components/ui/switch';
import { useMemo, useState } from 'react';

export default function AllServices({ slp }: { slp: any }) {
  const [filter, setFilter] = useRecoilState(IGetServicesFilterState);
  const [showArchived, setShowArchived] = useState(false);
  const serviceStatus = useMemo(() => {
    if (showArchived) {
      return ['ACTIVE', 'INACTIVE']; // Show both ACTIVE and INACTIVE
    }
    return ['ACTIVE'];
  }, [showArchived]);
  const { ServicesData, ServicesLoading, handleValueChange, activeTab } =
    useServices(slp, serviceStatus);

  console.log('ServicesData', ServicesData);

  return (
    <div>
      <Tabs.Root
        border={'none'}
        defaultValue={'services'}
        lazyMount
        size={{ base: 'sm', md: 'md' }}
        value={activeTab}
        onValueChange={handleValueChange}
      >
        <Box
          style={{ position: 'sticky', zIndex: 1, top: '80px' }}
          bg={'white'}
          borderBottom={{ lg: '1px solid' }}
          borderColor={{ lg: 'gray.50' }}
        >
          <Tabs.List
            display={'flex'}
            border={'none'}
            alignItems={'center'}
            gap={'6'}
            overflowY={'hidden'}
          >
            <Tabs.Trigger
              value={'services'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Services
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'packages'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Packages
            </Tabs.Trigger>
          </Tabs.List>
        </Box>

        <Box
          pt={'3'}
          paddingRight={'2'}
          paddingLeft={'2'}
          flex={1}
          // h={{
          //   base: 'calc(100% - 7.5rem)', // Adjusted for base view
          //   md: 'calc(100% - 52.051rem)', // Adjusted for md view
          // }}
          pb={'20'}
        >
          <Tabs.Content value={'services'}>
            <Box display={'flex'} flexDirection={'column'} alignItems={'start'}>
              <Flex
                justifyContent={'space-between'}
                width={'100%'}
                alignItems={'start'}
                gap={'3'}
                flexDirection={{ base: 'column', md: 'row' }}
              >
                <VStack alignItems={'start'} gap={'0.5'}>
                  <Heading
                    fontSize={{ base: '1.3rem', md: '2rem' }}
                    fontWeight={'semibold'}
                  >
                    Services
                  </Heading>
                  <Text
                    fontSize={{ base: 'sm', md: 'md' }}
                    color={'gray.300'}
                    fontWeight={'500'}
                    mb={'3'}
                  >
                    Create and edit services. The descriptions will be reflected
                    in the client invoices.
                  </Text>
                  <Switch
                    checked={showArchived}
                    onChange={() => setShowArchived(!showArchived)}
                    colorPalette={'orange'}
                  >
                    {'Show Archived'}
                  </Switch>
                </VStack>
                <Box>
                  <AddNewServiceModal
                    userFromServer={slp}
                    variant={2}
                    buttonName="New Service"
                  />
                </Box>
              </Flex>

              <Box minH={'20rem'} mt={'5'} width={'100%'}>
                {ServicesLoading ? (
                  <Center h={'20rem'}>
                    <AnimateLoader />
                  </Center>
                ) : ServicesData?.services?.length > 0 ? (
                  <CustomTable
                    columnDef={createColumnDef()}
                    data={ServicesData?.services || []}
                    filter={{
                      tableName: 'Services',
                    }}
                    tableOptions={{
                      pageCount: 1,
                      manualPagination: true,
                      getCoreRowModel: getCoreRowModel(),
                      getSortedRowModel: getSortedRowModel(),
                      enableMultiSort: true,
                      enableRowSelection: true,
                    }}
                    total={ServicesData?.total_count}
                    pagination={{
                      row: Number(filter?.size),
                      page: Number(filter?.currentPage),
                    }}
                    setPagination={{
                      onPageChange: (e) =>
                        setFilter({ ...filter, currentPage: e }),
                      onRowChange: (e) => setFilter({ ...filter, size: e }),
                    }}
                  />
                ) : (
                  <EmptyState text="No Services" />
                )}
              </Box>
            </Box>
          </Tabs.Content>

          <Tabs.Content value={'packages'}>
            <Packages
              userFromServer={slp}
              variant={2}
              buttonName="New Package"
            />
          </Tabs.Content>
          {/* <Tabs.Content value={'transactions'}>
            <PackageTransaction />
          </Tabs.Content> */}
        </Box>
      </Tabs.Root>
    </div>
  );
}
