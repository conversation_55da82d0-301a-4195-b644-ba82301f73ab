import {
  Box,
  MenuContent,
  Menu<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MenuSeparator,
  // MenuSeparator,
  MenuTrigger,
  useDisclosure,
} from '@chakra-ui/react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import EditServiceModal from './EditServiceModal';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import { useUpdateService } from '@/api/services/update-service';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';
import { useQueryClient } from '@tanstack/react-query';
import { FiArchive, FiEdit } from 'react-icons/fi';
// import GeneratePaymentLink from './GeneratePaymentLink';

const Actions = ({ data }: { data: any }) => {
  const queryClient = useQueryClient();
  const { open, onClose, onOpen } = useDisclosure();
  const {
    open: isArchiveOpen,
    onClose: onArchiveClose,
    onOpen: onArchiveOpen,
  } = useDisclosure();
  const { mutateAsync: UpdateServiceApi, isLoading: UpdateServiceLoading } =
    useUpdateService();

  const isInactive = data?.status === 'INACTIVE';

  const handleArchiveToggle = async () => {
    try {
      const newStatus = isInactive ? 'ACTIVE' : 'INACTIVE';
      await UpdateServiceApi({ id: data?.id, status: newStatus });
      await queryClient.invalidateQueries({
        queryKey: [queryKey.services.getAll],
      });

      toaster.create({
        description: `Service ${isInactive ? 'unarchived' : 'archived'} successfully.`,
        type: 'success',
      });

      onArchiveClose();
    } catch (error: any) {
      toaster.create({
        description: error?.message || 'Something went wrong.',
        type: 'error',
      });
      console.error(error);
    }
  };

  return (
    <Box position={'relative'}>
      <MenuRoot>
        <MenuTrigger cursor={'pointer'}>
          <BsThreeDotsVertical />
        </MenuTrigger>
        <MenuContent cursor={'pointer'} position={'absolute'} right={'2px'}>
          <MenuItem onClick={onOpen} value="edit">
            <FiEdit />
            Edit
          </MenuItem>
          <MenuSeparator />
          <MenuItem
            value="archive"
            onClick={onArchiveOpen}
            color={isInactive ? 'green' : 'red'}
          >
            <FiArchive />
            {isInactive ? 'Unarchive' : 'Archive'}
          </MenuItem>
        </MenuContent>
      </MenuRoot>

      {open && <EditServiceModal isOpen={open} onClose={onClose} data={data} />}

      <ConsentDialog
        handleSubmit={handleArchiveToggle}
        open={isArchiveOpen}
        onOpenChange={onArchiveClose}
        heading={isInactive ? 'Confirm Unarchive?' : 'Confirm Archive?'}
        note={`This will ${isInactive ? 'unarchive' : 'archive'} this service.`}
        isLoading={UpdateServiceLoading}
      />
    </Box>
  );
};

export default Actions;
