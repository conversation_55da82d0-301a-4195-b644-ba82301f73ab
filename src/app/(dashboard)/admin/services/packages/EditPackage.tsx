import { CustomModal } from '@/components/elements/modal/custom-modal';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Box, Flex, HStack, Stack, Text } from '@chakra-ui/react';
import { BsTrash } from 'react-icons/bs';
import { FiDollarSign, FiMinus, FiPlus } from 'react-icons/fi';
import { useEditPackageOfferings } from '../_hook/useEditPackageOfferings';
import { periodOptions } from './AddPackage';

export default function EditPackage({
  data,
  userFromServer,
  isOpen,
  onClose,
}: {
  data?: any;
  userFromServer: any;
  isOpen: boolean;
  onClose: () => void;
}) {
  const {
    loading,
    // setFieldValue,
    values,
    errors,
    touched,
    submitEdit,
    handleChange,
    // statusOptions,
    onServiceClick,
    onEditClose,
    // onEditOpen,
    // openEdit,
    handleRemoveService,
    handleQuantityChange,
    handleExpiry,
    setFieldValue,
    servicesOption,
    TaxData,
  } = useEditPackageOfferings(data, userFromServer);

  console.log('EditPackage values', values);

  const taxOptions = TaxData?.data?.map((tax: any) => {
    return {
      ...tax,
      value: tax?.id,
      label: `${tax?.name}(${tax?.value}%) ${tax.description ? ` - ${tax.description}` : ''}`,
    };
  });

  return (
    <>
      <CustomModal
        w={{ base: '90%', md: '40%' }}
        open={isOpen}
        onOpenChange={() => {
          if (loading) {
            return;
          }
          onClose();
        }}
      >
        <Box my={'1rem'}>
          <Text fontSize={'1.2rem'} textAlign={'center'} fontWeight={'bold'}>
            Edit Package Offering
          </Text>
        </Box>

        <form onSubmit={submitEdit}>
          <Stack gap={'1rem'} pt={'1rem'}>
            <Box>
              <Box pb={'.5rem'}>
                <StringInput
                  fieldProps={{
                    invalid: touched.name && !!errors.name,
                    label: 'Package Name',
                    required: true,
                    errorText: errors?.name,
                  }}
                  inputProps={{
                    name: 'name',
                    value: values.name,
                    onChange: handleChange,
                  }}
                />
              </Box>
              <Box pb={'.5rem'}>
                <StringInput
                  fieldProps={{
                    invalid: touched.description && !!errors.description,
                    label: 'Description',
                    required: true,
                    errorText: errors?.description,
                  }}
                  inputProps={{
                    name: 'description',
                    value: values.description,
                    onChange: handleChange,
                  }}
                />
              </Box>

              <CustomSelect
                placeholder="Add Service(s)"
                onChange={(val) => onServiceClick(val?.value)}
                options={servicesOption}
                required={values.services.length === 0}
                label="Service(s)"
                selectedOption={null}
                isClearable={true}
              />

              {values?.services?.length > 0 && (
                <Box mt={4}>
                  <Text mb={2}>Selected Services:</Text>
                  <Stack gap={3}>
                    {values.services.map((service: any) => {
                      if (!service.deleted) {
                        return (
                          <Box
                            key={service.id}
                            p={3}
                            borderWidth="1px"
                            borderRadius="md"
                            position="relative"
                          >
                            <Flex
                              justifyContent="space-between"
                              alignItems="center"
                            >
                              <Box w={'25rem'}>
                                <Box>
                                  <Text fontWeight="medium">
                                    {service?.name}
                                  </Text>
                                  <Text fontWeight="medium">
                                    {service?.description}
                                  </Text>
                                </Box>
                              </Box>
                              <Box>
                                <Text>${service?.price}</Text>
                              </Box>

                              <Box>
                                <HStack gap={4}>
                                  <HStack gap={2}>
                                    <FiMinus
                                      cursor={'pointer'}
                                      onClick={() =>
                                        handleQuantityChange(
                                          service.id!,
                                          Math.max(service?.quantity - 1, 1)
                                        )
                                      }
                                    />
                                    <Text minW="2rem" textAlign="center">
                                      {service?.quantity}
                                    </Text>

                                    <FiPlus
                                      cursor={'pointer'}
                                      onClick={() =>
                                        handleQuantityChange(
                                          service.id!,
                                          service.quantity + 1
                                        )
                                      }
                                    />
                                  </HStack>

                                  <BsTrash
                                    color={'red'}
                                    cursor={'pointer'}
                                    onClick={() =>
                                      handleRemoveService(service.id!)
                                    }
                                  />
                                </HStack>
                              </Box>
                            </Flex>
                          </Box>
                        );
                      }
                    })}
                  </Stack>
                </Box>
              )}
            </Box>

            <Box
              display={'grid'}
              alignItems={'center'}
              gridTemplateColumns={'1fr'}
              gap={'.5rem'}
              w={'full'}
            >
              <StringInput
                fieldProps={{
                  invalid: touched.price && !!errors.price,
                  label: (
                    <>
                      Package Price{' '}
                      {values?.amount ? (
                        <>
                          (
                          <Text
                            display={'flex'}
                            alignItems={'center'}
                            color={'gray.200'}
                            // textDecorationLine={'line-through'}
                          >
                            ${values?.amount}
                          </Text>
                          )
                        </>
                      ) : null}
                    </>
                  ),
                  errorText: errors?.price,
                  required: true,
                }}
                inputProps={{
                  name: 'price',
                  value: values.price,
                  onChange: handleChange,
                }}
                inputGroup={{
                  startElement: <FiDollarSign />,
                }}
              />
            </Box>

            <CustomSelect
              placeholder="Tax"
              onChange={(val) => setFieldValue('tax_id', val?.value)}
              options={taxOptions}
              label="Select Tax"
              selectedOption={taxOptions?.find(
                (tax: any) => tax?.value == values?.tax_id
              )}
            />
            <Switch
              checked={values?.can_expire}
              onChange={() => {
                handleExpiry();
              }}
              colorPalette={'orange'}
            >
              Add Expiry
            </Switch>

            {values?.can_expire ? (
              <Flex gap={'1rem'}>
                <Box flexGrow={1}>
                  <StringInput
                    inputProps={{
                      type: 'number',
                      name: 'expiry_duration',
                      value: values.expiry_duration || undefined,
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'Duration',
                    }}
                  />
                </Box>
                <Box flexGrow={1}>
                  <CustomSelect
                    selectedOption={periodOptions.find(
                      (option) => values?.expiry_period == option.value
                    )}
                    placeholder="Select Period"
                    onChange={(option) =>
                      setFieldValue('expiry_period', option.value)
                    }
                    options={periodOptions}
                    label="Period"
                  />
                </Box>
              </Flex>
            ) : null}
            <Flex
              my={'1.8rem'}
              alignItems={'center'}
              justifyContent={'space-between'}
            >
              <Button
                onClick={onEditClose}
                variant={'outline'}
                minH={'3rem'}
                minW={'15rem'}
              >
                Cancel{' '}
              </Button>
              <Button
                loading={loading}
                minH={'3rem'}
                minW={'15rem'}
                bg={'primary.500'}
                type="submit"
              >
                Save
              </Button>
            </Flex>
          </Stack>
        </form>
      </CustomModal>
    </>
  );
}
