import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
// import moment from 'moment';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { IPackageList } from '@/shared/interface/packages-list';
import PackageActions from './PackageActions';

const columnHelper = createColumnHelper<any>();

// if expiry date is = selected date
// const checkIsExpired = () => {
//   return false;
// if (!row?.can_expire) {
// }
// const expiry_date = new Date(row?.expiry_date).getTime();
// const now = new Date().getTime();
// const isExpired = expiry_date <= now;
// console.log('isExpired', isExpired);
// return isExpired;
// };

export const columnDef = (section: any, userFromServer: any) => {
  return [
    columnHelper.display({
      cell: (info) => {
        return <Box>{info.row.original?.name}</Box>;
      },
      header: 'Package Name',
      id: 'package_name' + section,
    }),
    columnHelper.accessor('description', {
      cell: (info) => {
        return (
          <Box w={'18rem'} whiteSpace="normal" wordBreak="break-word">
            {info.getValue()}
          </Box>
        );
      },

      header: 'Description',
      id: 'description',
    }),
    // columnHelper.accessor('package_items', {
    //   cell: (info) => {
    //     const product = !info.row.original?.products?.length
    //       ? info.getValue()
    //       : listProducts(info.row.original?.products);
    //
    //     return (
    //       <Box
    //
    //         w={'auto'}
    //         whiteSpace="normal"
    //         wordBreak="break-word"
    //       >
    //         {section === 'raw'
    //           ? product
    //           : // ? info.getValue()
    //             changeProvinceShortForm(info.getValue())}
    //       </Box>
    //     );
    //   },
    //   header: 'Products',
    //   id: 'product',
    // }),
    columnHelper.display({
      id: 'product-details',
      cell: (props) => {
        const services = props?.row?.original?.package_item;

        return (
          <Stack>
            {services?.map((item: any) => {
              return (
                <Flex
                  alignItems={'center'}
                  gap={'.5rem'}
                  key={item?.service?.id}
                >
                  <Text> {item?.service?.name}</Text>
                  <Text fontWeight={600}> {item?.quantity}</Text>
                </Flex>
              );
            })}
          </Stack>
        );
      },
      header: 'Services',
    }),

    // columnHelper.accessor('price', {
    //   cell: (info) => {
    //
    //     return (
    //       <Box >
    //         ${info.row.original?.price}
    //       </Box>
    //     );
    //   },
    //   header: 'Base Price',
    //   id: 'price',
    // }),
    columnHelper.accessor('price', {
      cell: (info) => {
        return <Box>{formatMoney(info.getValue())}</Box>;
      },
      header: 'Amount',
      id: 'amount',
    }),
    columnHelper.display({
      cell: (info) => {
        return (
          <Box>
            {info?.row.original?.tax ? (
              <>
                {info?.row.original?.tax?.name} (
                {info?.row.original?.tax?.value}%)
              </>
            ) : null}
          </Box>
        );
      },
      header: 'Tax',
      id: 'tax',
    }),
    // columnHelper.accessor('discount', {
    //   cell: (info) => {
    //
    //     return (
    //       <Box color={isExpired ? 'red' : 'green'}>
    //         ${info.getValue()?.toFixed(2)}
    //       </Box>
    //     );
    //   },
    //   header: 'Discount',
    //   id: 'discount',
    // }),
    columnHelper.display({
      cell: (info) => {
        const row = info.row.original;

        return (
          <Box minW={'5rem'}>
            {row.can_expire ? (
              <>
                {row?.expiry_duration} {row?.expiry_period}
              </>
            ) : (
              <Text>No Expiry</Text>
            )}
          </Box>
        );
      },
      header: 'Expiry Date',
      id: 'expiry date',
    }),
    columnHelper.display({
      cell: (props) => (
        <PackageActions
          userFromServer={userFromServer}
          data={props.row.original as IPackageList}
        />
      ),
      header: 'Action',
      id: 'action',
    }),
  ].filter(Boolean);
};
