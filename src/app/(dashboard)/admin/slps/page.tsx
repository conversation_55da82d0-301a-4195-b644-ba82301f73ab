import React from 'react';
import AllSlps from './all-slps/AllSlps';
import { Metadata } from 'next';
import { Box } from '@chakra-ui/react';
import { getOrganizationName } from '@/utils/server-cookie-helper';

export async function generateMetadata(): Promise<Metadata> {
  const organization_name = getOrganizationName();
  const fullTitle = `Soap - ${organization_name} - SLPs`;
  const description = `Soap Note platform.`;
  return {
    title: fullTitle,
    description,
  };
}

export default function page() {
  return (
    <Box>
      <AllSlps />
    </Box>
  );
}
