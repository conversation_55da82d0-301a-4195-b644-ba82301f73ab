import {
  <PERSON>u<PERSON>ontent,
  <PERSON>u<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON>ara<PERSON>,
  MenuTrigger,
} from '@/components/ui/menu';
import { Switch } from '@/components/ui/switch';
import {
  Box,
  Button,
  Flex,
  IconButton,
  Input,
  Stack,
  Text,
  Textarea,
} from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { FiPlus, FiTrash2 } from 'react-icons/fi';
import { MdAdd, MdRemove } from 'react-icons/md';

interface RadioComProps {
  // setAllSelectedQuestions: (questions: any) => void;
  generateUniqueId: () => string;
  onClose: () => void;
  q?: any;
  addQuestionToCurrentPage: (questionData: any) => void;
  currentPage?: number;
}

const RadioCom = ({
  // setAllSelectedQuestions,
  onClose,
  addQuestionToCurrentPage,
  generateUniqueId,
  currentPage,
  q,
}: RadioComProps) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [question, setQuestion] = useState('');
  const [isRequired, setIsRequired] = useState(false);
  const [options, setOptions] = useState<string[]>(
    q?.options
      ? Array.isArray(q.options)
        ? q.options
        : q.options.split(',')
      : ['']
  );
  const [isEdit, setIsEdit] = useState(false);
  const [showError, setShowError] = useState({
    question: false,
    options: false,
  });
  const [showTitle, setShowTitle] = useState(false);
  const [showDescription, setShowDescription] = useState(false);
  const questionRef = useRef<HTMLInputElement>(null);

  const handleAddOrEditQuestion = () => {
    const hasValidOptions = options.some((opt) => opt.trim() !== '');
    const newErrorState = {
      question: question.trim().length === 0,
      options: !hasValidOptions,
    };

    setShowError(newErrorState);

    if (newErrorState.question || newErrorState.options) {
      if (newErrorState.question && questionRef.current) {
        questionRef.current.focus();
      }
      return;
    }

    const radioQuestion = {
      id: isEdit ? q.id : generateUniqueId(),
      qt: question,
      required: isRequired,
      heading: q?.heading,
      page: currentPage,
      options: options.filter((opt) => opt.trim() !== ''),
      type: q?.type || 'Single choice',
      icon:
        q?.type === 'Single choice'
          ? 'IoMdRadioButtonOn'
          : 'IoIosCheckboxOutline',
      title: showTitle ? title : '',
      description: showDescription ? description : '',
    };

    // setAllSelectedQuestions((prevQuestions: any) =>
    //   isEdit
    //     ? prevQuestions.map((item: any) =>
    //         item.id === q.id ? radioQuestion : item
    //       )
    //     : [...prevQuestions, radioQuestion]
    // );
    addQuestionToCurrentPage(radioQuestion);

    onClose();
  };

  const handleAddOption = () => {
    setOptions([...options, '']);
    setShowError((prev) => ({ ...prev, options: false }));
  };

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
    setShowError((prev) => ({ ...prev, options: false }));
  };

  const handleRemoveOption = (index: number) => {
    if (options.length > 1) {
      setOptions(options.filter((_, i) => i !== index));
    }
  };

  const handleAddTitle = () => {
    setShowTitle(true);
  };

  const handleRemoveTitle = () => {
    setShowTitle(false);
    setTitle('');
  };

  const handleAddDescription = () => {
    setShowDescription(true);
  };

  const handleRemoveDescription = () => {
    setShowDescription(false);
    setDescription('');
  };

  useEffect(() => {
    if (q?.qt) {
      setQuestion(q.qt || '');
      setTitle(q.title || '');
      setDescription(q.description || '');
      setIsRequired(q.required === 'true' || q.required === true || false);
      setOptions(
        Array.isArray(q.options) ? q.options : q.options?.split(',') || ['']
      );
      setIsEdit(true);
      if (q.title) setShowTitle(true);
      if (q.description) setShowDescription(true);
    }
  }, [q]);

  return (
    <Box display="flex" width="100%" flexDirection="column" gap="1.5rem">
      <Flex alignItems="center" justifyContent="space-between" width="100%">
        <Text fontSize="1rem" fontWeight={500}>
          {isEdit
            ? `Edit ${q?.type || 'Single choice'}`
            : `Add ${q?.type || 'Single choice'}`}
        </Text>
        <Flex alignItems="center" gap="0.5rem">
          <MenuRoot
            positioning={{
              placement: 'bottom-end',
              strategy: 'fixed',
            }}
          >
            <MenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                p="0.5rem"
                minW="auto"
                h="auto"
                _hover={{ bg: 'gray.100' }}
                cursor="pointer"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <BsThreeDotsVertical />
              </Button>
            </MenuTrigger>
            <MenuContent
              cursor={'pointer'}
              width={'10rem'}
              style={{
                zIndex: 9999,
                position: 'fixed',
              }}
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              {!showTitle ? (
                <MenuItem
                  width={'100'}
                  value="add-title"
                  fontSize={'10px'}
                  cursor={'pointer'}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddTitle();
                  }}
                >
                  <MdAdd style={{ marginRight: '0.5rem' }} />
                  Add Title
                </MenuItem>
              ) : (
                <MenuItem
                  value="remove-title"
                  width={'100'}
                  fontSize={'10px'}
                  cursor={'pointer'}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveTitle();
                  }}
                >
                  <MdRemove style={{ marginRight: '0.5rem' }} />
                  Remove Title
                </MenuItem>
              )}
              <MenuSeparator />
              {!showDescription ? (
                <MenuItem
                  width={'100'}
                  fontSize={'10px'}
                  value="add-description"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddDescription();
                  }}
                >
                  <MdAdd style={{ marginRight: '0.5rem' }} />
                  Add Description
                </MenuItem>
              ) : (
                <MenuItem
                  value="remove-description"
                  fontSize={'10px'}
                  cursor={'pointer'}
                  width={'100'}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveDescription();
                  }}
                >
                  <MdRemove style={{ marginRight: '0.5rem' }} />
                  Remove Description
                </MenuItem>
              )}
            </MenuContent>
          </MenuRoot>

          <Button
            onClick={handleAddOrEditQuestion}
            bg="primary.500"
            color="white"
            _hover={{ bg: 'primary.600' }}
          >
            {isEdit ? 'Save' : 'Add'}
          </Button>
        </Flex>
      </Flex>

      <Stack gap="1.5rem">
        {/* Question Input */}
        <Box>
          <Text mb={1} fontSize="sm" color="gray.600">
            Question <span style={{ color: 'red' }}>*</span>
          </Text>
          <Input
            ref={questionRef}
            value={question}
            onChange={(e) => {
              setQuestion(e.target.value);
              setShowError((prev) => ({ ...prev, question: false }));
            }}
            placeholder="What do you want to ask?"
            variant="flushed"
            borderBottom="1px solid"
            borderColor="gray.300"
            _focus={{ borderColor: 'primary.500', boxShadow: 'none' }}
            height="40px"
            px={0}
          />
          {showError.question && (
            <Text color="red.500" fontSize="sm" mt={1}>
              Please enter a question
            </Text>
          )}
        </Box>

        {/* Required Toggle */}
        <Flex alignItems="center" justifyContent="space-between" width="100%">
          <Text>Required</Text>
          <Switch
            checked={isRequired}
            onChange={() => setIsRequired(!isRequired)}
            colorScheme="primary"
          />
        </Flex>

        {/* Title Section - Conditionally Rendered */}
        {showTitle && (
          <Box>
            <Flex alignItems="center" justifyContent="space-between" mb={1}>
              <Text fontSize="sm" color="gray.600">
                Title (optional)
              </Text>
              <Button
                variant="ghost"
                size="sm"
                p="0.25rem"
                minW="auto"
                h="auto"
                _hover={{ bg: 'gray.100' }}
                cursor="pointer"
                onClick={handleRemoveTitle}
              >
                <MdRemove color="gray.500" />
              </Button>
            </Flex>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter title"
              border="1px solid"
              borderColor="gray.200"
              _focus={{ borderColor: 'primary.500' }}
              height="40px"
            />
          </Box>
        )}

        {/* Description Section - Conditionally Rendered */}
        {showDescription && (
          <Box>
            <Flex alignItems="center" justifyContent="space-between" mb={1}>
              <Text fontSize="sm" color="gray.600">
                Description (optional)
              </Text>
              <Button
                variant="ghost"
                size="sm"
                p="0.25rem"
                minW="auto"
                h="auto"
                _hover={{ bg: 'gray.100' }}
                cursor="pointer"
                onClick={handleRemoveDescription}
              >
                <MdRemove color="gray.500" />
              </Button>
            </Flex>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter description"
              border="1px solid"
              borderColor="gray.200"
              _focus={{ borderColor: 'primary.500' }}
              minH="80px"
            />
          </Box>
        )}

        {/* Options */}
        <Stack spaceY={2}>
          <Text fontSize="sm" fontWeight="medium">
            Options{' '}
            {showError.options && options.every((opt) => opt.trim() === '') && (
              <Text as="span" color="red.500">
                (At least one option required)
              </Text>
            )}
          </Text>

          {options.map((option, index) => (
            <Flex key={index} alignItems="center" gap={2}>
              <Input
                placeholder="Start typing here..."
                value={option}
                onChange={(e) => handleOptionChange(index, e.target.value)}
                border="1px solid"
                borderColor="gray.200"
                _focus={{ borderColor: 'primary.500' }}
                height="40px"
              />
              {options.length > 1 && (
                <IconButton
                  aria-label="Remove option"
                  onClick={() => handleRemoveOption(index)}
                  size="sm"
                  variant="ghost"
                >
                  <FiTrash2 size={16} />
                </IconButton>
              )}
            </Flex>
          ))}

          <Button onClick={handleAddOption} size="sm" variant="outline">
            <FiPlus style={{ marginRight: '5px' }} /> Add a choice
          </Button>
        </Stack>
      </Stack>
    </Box>
  );
};

export default RadioCom;
