import {
  <PERSON>u<PERSON>ontent,
  <PERSON>uI<PERSON>,
  <PERSON>uRoot,
  MenuSeparator,
  MenuTrigger,
} from '@/components/ui/menu';
import { Switch } from '@/components/ui/switch';
import {
  Box,
  Button,
  Flex,
  Input,
  Stack,
  Text,
  Textarea,
} from '@chakra-ui/react';
import { useEffect, useRef, useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { MdAdd, MdRemove } from 'react-icons/md';

interface NumberComProps {
  // setAllSelectedQuestions: (questions: any) => void;
  generateUniqueId: () => string;
  onClose: () => void;
  q?: any;
  addQuestionToCurrentPage: (questionData: any) => void;
  currentPage?: number;
}

const NumberCom = ({
  // setAllSelectedQuestions,
  onClose,
  generateUniqueId,
  addQuestionToCurrentPage,
  currentPage,
  q,
}: NumberComProps) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [question, setQuestion] = useState('');
  const [isRequired, setIsRequired] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [showError, setShowError] = useState(false);
  const [showTitle, setShowTitle] = useState(false);
  const [showDescription, setShowDescription] = useState(false);
  const questionInputRef = useRef<HTMLInputElement>(null);

  const isValid = question.trim().length > 0;

  const handleAddOrEditNumberQuestion = () => {
    if (!isValid) {
      setShowError(true);
      questionInputRef.current?.focus();
      return;
    }

    const questionData = {
      id: isEdit ? q.id : generateUniqueId(),
      qt: question,
      required: isRequired,
      heading: q?.heading,
      type: 'Number',
      page: currentPage,
      icon: 'GoHash',
      title: showTitle ? title : '',
      description: showDescription ? description : '',
      placeholder: q?.placeholder || '',
    };

    // setAllSelectedQuestions((prevQuestions: any) =>
    //   isEdit
    //     ? prevQuestions.map((item: any) =>
    //         item.id === q.id ? questionData : item
    //       )
    //     : [...prevQuestions, questionData]
    // );

    addQuestionToCurrentPage(questionData);

    onClose();
  };

  const handleAddTitle = () => {
    setShowTitle(true);
  };

  const handleRemoveTitle = () => {
    setShowTitle(false);
    setTitle('');
  };

  const handleAddDescription = () => {
    setShowDescription(true);
  };

  const handleRemoveDescription = () => {
    setShowDescription(false);
    setDescription('');
  };

  useEffect(() => {
    if (q?.qt) {
      setQuestion(q.qt || '');
      setTitle(q.title || '');
      setDescription(q.description || '');
      setIsRequired(q.required || false);
      setIsEdit(true);
      if (q.title) setShowTitle(true);
      if (q.description) setShowDescription(true);
    }
  }, [q]);

  return (
    <Box display="flex" width="100%" flexDirection="column" gap="1.5rem">
      <Flex alignItems="center" justifyContent="space-between" width="100%">
        <Text fontSize="1rem" fontWeight={500}>
          {isEdit ? 'Edit Number Question' : 'Add Number Question'}
        </Text>
        <Flex alignItems="center" gap="0.5rem">
          <MenuRoot
            positioning={{
              placement: 'bottom-end',
              strategy: 'fixed',
            }}
          >
            <MenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                p="0.5rem"
                minW="auto"
                h="auto"
                _hover={{ bg: 'gray.100' }}
                cursor="pointer"
                onClick={(e) => {
                  e.stopPropagation();
                }}
              >
                <BsThreeDotsVertical />
              </Button>
            </MenuTrigger>
            <MenuContent
              cursor={'pointer'}
              width={'10rem'}
              style={{
                zIndex: 9999,
                position: 'fixed',
              }}
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              {!showTitle ? (
                <MenuItem
                  width={'100'}
                  value="add-title"
                  fontSize={'10px'}
                  cursor={'pointer'}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddTitle();
                  }}
                >
                  <MdAdd style={{ marginRight: '0.5rem' }} />
                  Add Title
                </MenuItem>
              ) : (
                <MenuItem
                  value="remove-title"
                  width={'100'}
                  fontSize={'10px'}
                  cursor={'pointer'}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveTitle();
                  }}
                >
                  <MdRemove style={{ marginRight: '0.5rem' }} />
                  Remove Title
                </MenuItem>
              )}
              <MenuSeparator />
              {!showDescription ? (
                <MenuItem
                  width={'100'}
                  fontSize={'10px'}
                  value="add-description"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddDescription();
                  }}
                >
                  <MdAdd style={{ marginRight: '0.5rem' }} />
                  Add Description
                </MenuItem>
              ) : (
                <MenuItem
                  value="remove-description"
                  fontSize={'10px'}
                  cursor={'pointer'}
                  width={'100'}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveDescription();
                  }}
                >
                  <MdRemove style={{ marginRight: '0.5rem' }} />
                  Remove Description
                </MenuItem>
              )}
            </MenuContent>
          </MenuRoot>

          <Button
            onClick={handleAddOrEditNumberQuestion}
            bg="primary.500"
            color="white"
            _hover={{ bg: 'primary.600' }}
          >
            {isEdit ? 'Save' : 'Add'}
          </Button>
        </Flex>
      </Flex>

      <Stack gap="1.5rem">
        {/* Question Input */}
        <Box>
          <Text mb={1} fontSize="sm" color="gray.600">
            Question <span style={{ color: 'red' }}>*</span>
          </Text>
          <Input
            ref={questionInputRef}
            value={question}
            onChange={(e) => {
              setQuestion(e.target.value);
              setShowError(false);
            }}
            placeholder="What do you want to ask?"
            variant="flushed"
            borderBottom="1px solid"
            borderColor="gray.300"
            _focus={{ borderColor: 'primary.500', boxShadow: 'none' }}
            height="40px"
            px={0}
          />
          {showError && (
            <Text color="red.500" fontSize="sm" mt={1}>
              Please enter a question
            </Text>
          )}
        </Box>

        {/* Required Toggle */}
        <Flex alignItems="center" justifyContent="space-between" width="100%">
          <Text>Required</Text>
          <Switch
            checked={isRequired}
            onChange={() => setIsRequired(!isRequired)}
            colorScheme="primary"
          />
        </Flex>

        {/* Title Section - Conditionally Rendered */}
        {showTitle && (
          <Box>
            <Flex alignItems="center" justifyContent="space-between" mb={1}>
              <Text fontSize="sm" color="gray.600">
                Title (optional)
              </Text>
              <Button
                variant="ghost"
                size="sm"
                p="0.25rem"
                minW="auto"
                h="auto"
                _hover={{ bg: 'gray.100' }}
                cursor="pointer"
                onClick={handleRemoveTitle}
              >
                <MdRemove color="gray.500" />
              </Button>
            </Flex>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter title"
              border="1px solid"
              borderColor="gray.200"
              _focus={{ borderColor: 'primary.500' }}
              height="40px"
            />
          </Box>
        )}

        {/* Description Section - Conditionally Rendered */}
        {showDescription && (
          <Box>
            <Flex alignItems="center" justifyContent="space-between" mb={1}>
              <Text fontSize="sm" color="gray.600">
                Description (optional)
              </Text>
              <Button
                variant="ghost"
                size="sm"
                p="0.25rem"
                minW="auto"
                h="auto"
                _hover={{ bg: 'gray.100' }}
                cursor="pointer"
                onClick={handleRemoveDescription}
              >
                <MdRemove color="gray.500" />
              </Button>
            </Flex>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter description"
              border="1px solid"
              borderColor="gray.200"
              _focus={{ borderColor: 'primary.500' }}
              minH="80px"
            />
          </Box>
        )}
      </Stack>
    </Box>
  );
};

export default NumberCom;
