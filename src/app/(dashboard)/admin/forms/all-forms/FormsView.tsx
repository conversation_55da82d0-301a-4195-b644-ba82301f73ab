'use client';
import { useFormHook } from '@/hooks/forms/useFormHook';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import CreateNewForm from '../create/CreateNewForm';
import DisplayAnswers from '../display-answers/[id]/DisplayAnswers';
import EditForm from '../edit/[id]/EditForm';
import ViewAnswers from '../view-answers/[id]/ViewAnswers';
import FormsDashboard from './FormsDashboard';

const FormsView = ({ slp }: { slp: any }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  // Get current params directly from URL on each render
  const currentView = searchParams.get('view') || 'dashboard';
  const currentId = searchParams.get('id');
  const organization_id = searchParams.get('organization_id');
  // console.log('organization_id---454433', organization_id);

  // Active view state that will determine what's rendered
  const [activeView, setActiveView] = useState(currentView);
  const [activeId, setActiveId] = useState(
    currentId ? Number(currentId) : null
  );

  // Sync state with URL changes
  useEffect(() => {
    setActiveView(currentView);
    setActiveId(currentId ? Number(currentId) : null);
  }, [currentView, currentId]);

  // console.log('activeView', activeView);

  const getFormsViewState = (newView: string, newId?: number) => {
    // console.log('newView', newView);

    // Update the active view immediately for instant UI change
    setActiveView(newView);
    setActiveId(newId || null);

    // Update URL in the background
    const params = new URLSearchParams();
    params.set('view', newView);
    if (newId) params.set('id', newId.toString());

    // console.log('organization_id', organization_id);

    // Add organization_id if it exists
    if (organization_id) {
      params.set('organization_id', organization_id);
      params.set('tab', 'forms');
    }
    // console.log('params', params);
    // Use push in the background - no need to wait for this
    setTimeout(() => {
      router.push(`?${params.toString()}`, { scroll: true });
    }, 0);
  };

  const formHook = useFormHook({ slp });

  // Render based on active view state, not directly from URL params
  switch (activeView) {
    case 'dashboard':
      return (
        <FormsDashboard {...formHook} getFormsViewState={getFormsViewState} />
      );
    case 'edit':
      return <EditForm id={activeId} getFormsViewState={getFormsViewState} />;
    case 'view-answers':
      return (
        <ViewAnswers id={activeId} getFormsViewState={getFormsViewState} />
      );
    case 'create':
      return <CreateNewForm slp={slp} getFormsViewState={getFormsViewState} />;
    case 'submitted-answers':
      return (
        <DisplayAnswers id={activeId} getFormsViewState={getFormsViewState} />
      );
    default:
      return (
        <div>
          <h1>Forms View</h1>
          <p>Invalid view state</p>
        </div>
      );
  }
};

export default FormsView;
