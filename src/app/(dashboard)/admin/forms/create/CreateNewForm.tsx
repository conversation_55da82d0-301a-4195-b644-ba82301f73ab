'use client';
import CustomTextArea from '@/components/Input/CustomTextArea';
import StringInput from '@/components/Input/StringInput';
import { env } from '@/constants/env';
import { useFormHook } from '@/hooks/forms/useFormHook';
import { getSlugFromName, getSlugFromOrgName } from '@/utils/event';
import {
  Box,
  Button,
  Flex,
  Heading,
  HStack,
  Input,
  Separator,
  Spinner,
  Stack,
  Text,
} from '@chakra-ui/react';
import NextLink from 'next/link';
import { CiTextAlignRight } from 'react-icons/ci';
import { LiaTimesSolid } from 'react-icons/lia';
import Questions from './Questions';

const CreateNewForm = ({
  slp,
  getFormsViewState,
}: {
  slp?: any;
  getFormsViewState?: any;
}) => {
  const formHook = useFormHook({ slp });

  const {
    loading,
    values,
    errors,
    touched,
    organizationName,
    handleChange,
    handleSubmitNewForm,
    allSelectedQuestions,
    setFieldValue,
  } = formHook;

  // console.log('val', values);

  const isAnyFieldEmpty =
    Object.values(values).some((value) => value === '') ||
    allSelectedQuestions?.length === 0;

  // console.log('isAnyFieldEmpty', isAnyFieldEmpty);
  console.log('values', values);
  // console.log('allSelectedQuestions', allSelectedQuestions);

  return (
    <Box
      display={'flex'}
      flexDirection={'column'}
      width={{ base: '95%', lg: '80%' }}
      mx={'auto'}
    >
      {/* header */}
      <Box
        display={'flex'}
        alignItems={'center'}
        justifyContent={'space-between'}
        width={'100%'}
      >
        <Flex
          alignItems={'center'}
          gap={'10px'}
          onClick={() => getFormsViewState('dashboard')}
          // onClick={() => getFormsViewState('create')}
        >
          <Box
            borderRadius={'10px'}
            p={'10px'}
            cursor={'pointer'}
            _hover={{ bg: '#d96847', color: '#fff' }}
            bg={'#F2F2F2'}
          >
            <LiaTimesSolid />
          </Box>

          <Stack>
            <Heading fontWeight={400} fontSize={{ base: '1rem', lg: '1.5rem' }}>
              New Form Template
            </Heading>
            <Text
              display={'flex'}
              gap={'5px'}
              alignItems={'center'}
              fontSize={{ base: '0.7rem', lg: '1rem' }}
            >
              <CiTextAlignRight /> Form Template
            </Text>
          </Stack>
        </Flex>

        <Flex gap={'1rem'} alignItems={'center'}>
          <NextLink
            href={
              isAnyFieldEmpty
                ? '#'
                : `${env.FRONTEND_URL}/form/${getSlugFromOrgName(organizationName)}/${values.slug}`
            }
            passHref
            legacyBehavior
          >
            <Button
              as="a"
              disabled={isAnyFieldEmpty}
              bg="transparent"
              width="8rem"
              color="#000"
              variant="outline"
              opacity={isAnyFieldEmpty ? 0.6 : 1}
              cursor={isAnyFieldEmpty ? 'not-allowed' : 'pointer'}
              pointerEvents={isAnyFieldEmpty ? 'none' : 'auto'}
              _hover={
                isAnyFieldEmpty
                  ? undefined
                  : {
                      /* hover styles */
                    }
              }
            >
              Preview
            </Button>
          </NextLink>

          <Button
            onClick={handleSubmitNewForm}
            bg={'primary.500'}
            width={'8rem'}
          >
            {loading ? <Spinner size={'sm'} /> : 'Save Changes'}
          </Button>
        </Flex>
      </Box>
      {/* header ends */}

      {/* details forms */}

      <Box
        display={'flex'}
        width={'100'}
        justifyContent={'space-between'}
        alignItems={'start'}
        mt={'3rem'}
      >
        <Text width={{ base: '15%', lg: '10%' }}>Details</Text>
        <Box width={{ base: '55%', lg: '40%' }}>
          <Stack my={'2rem'} gap={'1.5rem'} width={'100%'}>
            <HStack>
              <StringInput
                inputProps={{
                  name: 'title',
                  type: 'text',
                  placeholder: 'Enter a title',

                  onChange: (e) => {
                    handleChange(e);
                    setFieldValue('slug', getSlugFromName(e.target.value));
                  },
                }}
                fieldProps={{
                  label: 'Title',
                  invalid: touched.title && !!errors.title,
                  errorText: errors.title,
                  required: true,
                }}
              />
            </HStack>
            {/* <HStack>
              <StringInput
                inputProps={{
                  name: 'header',
                  type: 'text',
                  placeholder: 'Enter a header message',

                  onChange: handleChange,
                }}
                fieldProps={{
                  label: 'Header message',
                  invalid: touched.header && !!errors.header,
                  errorText: errors.header,
                  required: true,
                }}
              />
            </HStack> */}

            <HStack>
              <Flex direction="column" w="full">
                <Text
                  whiteSpace={{ base: 'normal', lg: 'nowrap' }}
                  overflow="hidden"
                  textOverflow="ellipsis"
                  fontWeight={'500'}
                  mb={1}
                >
                  Form URL
                  <Text as="sup" fontSize={'15px'} color="red.500">
                    *
                  </Text>
                </Text>

                <Box position="relative">
                  <Input
                    name="slug"
                    type="text"
                    pl={`${(env.FRONTEND_URL?.length || 0) * 7 + 2}px`} // Padding to make space for prefix
                    value={values.slug} // Only store the user-entered part
                    onChange={(e: any) => {
                      // Only update with the user's input
                      handleChange({
                        target: {
                          name: 'slug',
                          value: e.target.value,
                        },
                      });
                    }}
                    placeholder="enter-url-slug"
                    height="40px" // Set a fixed height
                    lineHeight="1.2" // Adjust line height for better vertical alignment
                  />
                  <Text
                    position="absolute"
                    left={4}
                    top="50%"
                    transform="translateY(-50%)"
                    color="gray.500"
                    pointerEvents="none"
                    fontSize="sm"
                    lineHeight="40px" // Match input height
                    height="40px" // Match input height
                  >
                    {env.FRONTEND_URL || 'https://example.com'}/
                  </Text>
                </Box>
                {errors.slug && (
                  <Text color="red.500" fontSize="11px" mt={1}>
                    {errors.slug}
                  </Text>
                )}
              </Flex>
            </HStack>
            <HStack>
              <CustomTextArea
                inputProps={{
                  name: 'description',
                  value: values.description,
                  onChange: handleChange,
                }}
                fieldProps={{
                  label: 'Form Description',
                  required: true,
                  invalid: touched.description && !!errors.description,
                  errorText: errors.description,

                  //    invalid: touched.description && !!errors.description,
                }}
              />
            </HStack>
          </Stack>
        </Box>
      </Box>
      {/* details form end */}
      <Separator my={'1rem'} />

      {/* questions flow */}
      <Box width={'100%'}>
        <Questions formHook={formHook} />
      </Box>
      {/* questions flow ends */}

      {/* preview modal */}
      {/* <CustomModal
        w={{ base: '15%', md: '20rem' }}
        open={openPreview}
        onOpenChange={onClosePreview}
      >
        <PreviewForm allSelectedQuestions={allSelectedQuestions} />
      </CustomModal> */}
      {/* preview modal end */}
    </Box>
  );
};

export default CreateNewForm;
