import NumberCom from '@/app/(dashboard)/admin/forms/_components/NumberCom';
import TextCom from '@/app/(dashboard)/admin/forms/_components/TextCom';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Badge, Box, Flex, Stack, Text } from '@chakra-ui/react';
import { QuestionTypes } from '../../../../../components/form/type';
import DateCom from '../_components/DateCom';
import RadioCom from '../_components/RadioCom';
import PageNavigation from '../all-forms/PageNavigation';
import DndProviderWrapper from './DndProviderWrapper';
import SelectedQuestions from './SelectedQuestions';

const Questions = ({ formHook, type }: { formHook?: any; type?: any }) => {
  const {
    open,
    onClose,
    generateUniqueId,
    handleQuestionClick,
    selectedQuestionType,
    allSelectedQuestions,
    removeSelectedQuestion,
    setAllSelectedQuestions,
    // Multi-page functionality
    currentPage,
    totalPages,
    getCurrentPageQuestions,
    addPage,
    removePage,
    switchToPage,
    addQuestionToCurrentPage,
    getQuestionsForPage,
    moveQuestionToPage,
    getPageSummary,
    validatePages,
  } = formHook || {}; // Add fallback for formHook

  // Get questions for current page with fallback and type check
  const currentPageQuestions =
    getCurrentPageQuestions && typeof getCurrentPageQuestions === 'function'
      ? getCurrentPageQuestions()
      : (allSelectedQuestions || []).filter((q: any) => q.page === currentPage);

  const renderQuestionModalBody = (q: any) => {
    if (!q) return null;

    console.log('allSelected', allSelectedQuestions);
    switch (q.type.trim()) {
      case 'Textbox':
      case 'TextArea':
        return (
          <TextCom
            onClose={onClose}
            generateUniqueId={generateUniqueId}
            currentPage={currentPage}
            addQuestionToCurrentPage={addQuestionToCurrentPage}
            q={q}
          />
        );
      case 'Number':
        return (
          <NumberCom
            onClose={onClose}
            q={q}
            generateUniqueId={generateUniqueId}
            currentPage={currentPage}
            addQuestionToCurrentPage={addQuestionToCurrentPage}
          />
        );
      case 'Date':
        return (
          <DateCom
            onClose={onClose}
            q={q}
            currentPage={currentPage}
            addQuestionToCurrentPage={addQuestionToCurrentPage}
            generateUniqueId={generateUniqueId}
          />
        );
      case 'Single choice':
      case 'Multiple choice':
        return (
          <RadioCom
            onClose={onClose}
            q={q}
            currentPage={currentPage}
            addQuestionToCurrentPage={addQuestionToCurrentPage}
            generateUniqueId={generateUniqueId}
          />
        );
      default:
        return <Text>Select a question type.</Text>;
    }
  };

  // Validation check with fallback
  const pageValidation =
    validatePages && typeof validatePages === 'function'
      ? validatePages()
      : { isValid: true, message: '' };

  // Early return if formHook is not provided
  if (!formHook) {
    return <Box>Form hook not provided</Box>;
  }

  return (
    <DndProviderWrapper>
      <Box
        display={'flex'}
        width={'100%'}
        flexDirection={'column'}
        justifyContent={'start'}
        alignItems={'start'}
        gap={'1rem'}
      >
        {/* Page Navigation */}
        {PageNavigation && addPage && removePage && switchToPage && (
          <PageNavigation
            currentPage={currentPage || 1}
            totalPages={totalPages || 1}
            onAddPage={addPage}
            onRemovePage={removePage}
            onSwitchPage={switchToPage}
            getQuestionsForPage={getQuestionsForPage}
            getPageSummary={getPageSummary}
          />
        )}

        {/* Validation Alert */}
        {!pageValidation.isValid && <Box>{pageValidation.message}</Box>}

        {/* Current Page Header */}

        {type !== 'event' && (
          <Flex justify="space-between" align="center" width="100%">
            <Text fontSize="xl" fontWeight="600">
              Add Questions to Page {currentPage || 1}
            </Text>
            <Badge colorScheme="blue" fontSize="sm" px={3} py={1}>
              {currentPageQuestions.length} question
              {currentPageQuestions.length !== 1 ? 's' : ''}
            </Badge>
          </Flex>
        )}

        <Flex
          justifyContent={'space-between'}
          mt={'1rem'}
          alignItems={'start'}
          width={'100%'}
        >
          <Flex width={'100%'} gap={'1rem'}>
            <Stack w={'60%'}>
              {QuestionTypes?.map((question: any, index: any) => (
                <Box
                  onClick={() =>
                    handleQuestionClick &&
                    typeof handleQuestionClick === 'function'
                      ? handleQuestionClick(question)
                      : null
                  }
                  key={index}
                >
                  <Box
                    display={'flex'}
                    width={'100%'}
                    bg={'#F2F2F2'}
                    p={'1rem'}
                    gap={'10px'}
                    borderRadius={'5px'}
                    cursor={'pointer'}
                    alignItems={'center'}
                    transition="background-color 0.5s ease, transform 0.5s ease"
                    _hover={{ bg: 'primary.500', color: '#fff' }}
                  >
                    <Text>{question?.icon}</Text>
                    <Text>{question?.heading}</Text>
                  </Box>
                </Box>
              ))}
            </Stack>

            <Box w={'40%'} maxW={'25rem'} maxH={'50vh'} overflow={'auto'}>
              <SelectedQuestions
                handleQuestionClick={handleQuestionClick}
                removeSelectedQuestion={removeSelectedQuestion}
                allSelectedQuestions={currentPageQuestions}
                setAllSelectedQuestions={setAllSelectedQuestions}
                currentPage={currentPage}
                totalPages={totalPages}
                moveQuestionToPage={moveQuestionToPage}
              />
            </Box>
          </Flex>
        </Flex>

        {/* Page Summary */}

        {type !== 'event' && (
          <Box
            mt={4}
            p={4}
            bg="gray.50"
            borderRadius="md"
            border="1px solid"
            borderColor="gray.200"
            width="100%"
          >
            <Text fontSize="md" fontWeight="500" mb={2}>
              Form Overview
            </Text>
            <Stack direction="row" spaceY={4} wrap="wrap">
              {(getPageSummary && typeof getPageSummary === 'function'
                ? getPageSummary()
                : []
              )?.map((pageInfo: any) => (
                <Box
                  key={pageInfo.page}
                  p={2}
                  bg="white"
                  borderRadius="md"
                  border="1px solid"
                  borderColor="gray.200"
                  minW="120px"
                >
                  <Text fontSize="sm" fontWeight="500">
                    Page {pageInfo.page}
                  </Text>
                  <Text fontSize="xs" color="gray.600">
                    {pageInfo.questionCount} questions
                  </Text>
                  {pageInfo.hasRequired && (
                    <Badge colorScheme="red" fontSize="xs" mt={1}>
                      Required fields
                    </Badge>
                  )}
                </Box>
              ))}
            </Stack>
          </Box>
        )}

        {/* Modal for question configuration */}
        <CustomModal
          w={{ base: '30%', md: '9rem' }}
          open={open}
          onOpenChange={onClose}
        >
          <Box width={'100%'}>
            {renderQuestionModalBody(selectedQuestionType)}
          </Box>
        </CustomModal>
      </Box>
    </DndProviderWrapper>
  );
};

export default Questions;

// import NumberCom from '@/app/(dashboard)/admin/forms/_components/NumberCom';
// import TextCom from '@/app/(dashboard)/admin/forms/_components/TextCom';
// import { CustomModal } from '@/components/elements/modal/custom-modal';
// import { Badge, Box, Flex, Stack, Text } from '@chakra-ui/react';
// import { QuestionTypes } from '../../../../../components/form/type';
// import DateCom from '../_components/DateCom';
// import RadioCom from '../_components/RadioCom';
// import PageNavigation from '../all-forms/PageNavigation';
// import DndProviderWrapper from './DndProviderWrapper';
// import SelectedQuestions from './SelectedQuestions';

// const Questions = ({ formHook }: { formHook?: any }) => {
//   const {
//     open,
//     onClose,
//     generateUniqueId,
//     handleQuestionClick,
//     selectedQuestionType,
//     allSelectedQuestions,
//     removeSelectedQuestion,
//     setAllSelectedQuestions,
//     // Multi-page functionality
//     currentPage,
//     totalPages,
//     getCurrentPageQuestions,
//     addPage,
//     removePage,
//     switchToPage,
//     addQuestionToCurrentPage,
//     getQuestionsForPage,
//     moveQuestionToPage,
//     getPageSummary,
//     validatePages,
//   } = formHook || {};

//   // Get questions for current page
//   const currentPageQuestions = getCurrentPageQuestions
//     ? getCurrentPageQuestions()
//     : [];

//   // Updated function to handle adding questions to current page
//   // const handleAddQuestionToCurrentPage = (questionData: any) => {
//   //   const questionWithPage = {
//   //     ...questionData,
//   //     page: currentPage,
//   //   };

//   //   setAllSelectedQuestions((prev: any[]) => {
//   //     const isEdit = prev.some((q) => q.id === questionData.id);
//   //     if (isEdit) {
//   //       return prev.map((q) =>
//   //         q.id === questionData.id ? questionWithPage : q
//   //       );
//   //     } else {
//   //       return [...prev, questionWithPage];
//   //     }
//   //   });
//   // };

//   const renderQuestionModalBody = (q: any) => {
//     if (!q) return null;

//     console.log('allSelected', allSelectedQuestions);
//     switch (q.type.trim()) {
//       case 'Textbox':
//       case 'TextArea':
//         return (
//           <TextCom
//             onClose={onClose}
//             generateUniqueId={generateUniqueId}
//             currentPage={currentPage}
//             addQuestionToCurrentPage={addQuestionToCurrentPage}
//             q={q}
//             // setAllSelectedQuestions={handleAddQuestionToCurrentPage}
//           />
//         );
//       case 'Number':
//         return (
//           <NumberCom
//             onClose={onClose}
//             q={q}
//             generateUniqueId={generateUniqueId}
//             currentPage={currentPage}
//             addQuestionToCurrentPage={addQuestionToCurrentPage}
//             // setAllSelectedQuestions={handleAddQuestionToCurrentPage}
//           />
//         );
//       case 'Date':
//         return (
//           <DateCom
//             onClose={onClose}
//             q={q}
//             currentPage={currentPage}
//             addQuestionToCurrentPage={addQuestionToCurrentPage}
//             // setAllSelectedQuestions={handleAddQuestionToCurrentPage}
//             generateUniqueId={generateUniqueId}
//           />
//         );
//       case 'Single choice':
//       case 'Multiple choice':
//         return (
//           <RadioCom
//             onClose={onClose}
//             q={q}
//             currentPage={currentPage}
//             addQuestionToCurrentPage={addQuestionToCurrentPage}
//             // setAllSelectedQuestions={handleAddQuestionToCurrentPage}
//             generateUniqueId={generateUniqueId}
//           />
//         );
//       default:
//         return <Text>Select a question type.</Text>;
//     }
//   };

//   // Validation check
//   // const pageValidation = validatePages();

//   const pageValidation =
//     validatePages && typeof validatePages === 'function'
//       ? validatePages()
//       : { isValid: true, message: '' };

//   return (
//     <DndProviderWrapper>
//       <Box
//         display={'flex'}
//         width={'100%'}
//         flexDirection={'column'}
//         justifyContent={'start'}
//         alignItems={'start'}
//         gap={'1rem'}
//       >
//         {/* Page Navigation */}
//         <PageNavigation
//           currentPage={currentPage}
//           totalPages={totalPages}
//           onAddPage={addPage}
//           onRemovePage={removePage}
//           onSwitchPage={switchToPage}
//           getQuestionsForPage={getQuestionsForPage}
//           getPageSummary={getPageSummary}
//         />

//         {/* Validation Alert */}
//         {!pageValidation.isValid && <Box>{pageValidation.message}</Box>}

//         {/* Current Page Header */}
//         <Flex justify="space-between" align="center" width="100%">
//           <Text fontSize="xl" fontWeight="600">
//             Add Questions to Page {currentPage}
//           </Text>
//           <Badge colorScheme="blue" fontSize="sm" px={3} py={1}>
//             {currentPageQuestions.length} question
//             {currentPageQuestions.length !== 1 ? 's' : ''}
//           </Badge>
//         </Flex>

//         <Flex
//           justifyContent={'space-between'}
//           mt={'1rem'}
//           alignItems={'start'}
//           width={'100%'}
//         >
//           <Flex width={'100%'} gap={'1rem'}>
//             <Stack w={'60%'}>
//               {QuestionTypes?.map((question: any, index: any) => (
//                 <Box onClick={() => handleQuestionClick(question)} key={index}>
//                   <Box
//                     display={'flex'}
//                     width={'100%'}
//                     bg={'#F2F2F2'}
//                     p={'1rem'}
//                     gap={'10px'}
//                     borderRadius={'5px'}
//                     cursor={'pointer'}
//                     alignItems={'center'}
//                     transition="background-color 0.5s ease, transform 0.5s ease"
//                     _hover={{ bg: 'primary.500', color: '#fff' }}
//                   >
//                     <Text>{question?.icon}</Text>
//                     <Text>{question?.heading}</Text>
//                   </Box>
//                 </Box>
//               ))}
//             </Stack>

//             <Box w={'40%'} maxW={'25rem'} maxH={'50vh'} overflow={'auto'}>
//               <SelectedQuestions
//                 handleQuestionClick={handleQuestionClick}
//                 removeSelectedQuestion={removeSelectedQuestion}
//                 allSelectedQuestions={currentPageQuestions} // Only show current page questions
//                 setAllSelectedQuestions={setAllSelectedQuestions}
//                 currentPage={currentPage}
//                 totalPages={totalPages}
//                 moveQuestionToPage={moveQuestionToPage}
//               />
//             </Box>
//           </Flex>
//         </Flex>

//         {/* Page Summary */}
//         <Box
//           mt={4}
//           p={4}
//           bg="gray.50"
//           borderRadius="md"
//           border="1px solid"
//           borderColor="gray.200"
//           width="100%"
//         >
//           <Text fontSize="md" fontWeight="500" mb={2}>
//             Form Overview
//           </Text>
//           <Stack direction="row" spaceY={4} wrap="wrap">
//             {getPageSummary().map((pageInfo: any) => (
//               <Box
//                 key={pageInfo.page}
//                 p={2}
//                 bg="white"
//                 borderRadius="md"
//                 border="1px solid"
//                 borderColor="gray.200"
//                 minW="120px"
//               >
//                 <Text fontSize="sm" fontWeight="500">
//                   Page {pageInfo.page}
//                 </Text>
//                 <Text fontSize="xs" color="gray.600">
//                   {pageInfo.questionCount} questions
//                 </Text>
//                 {pageInfo.hasRequired && (
//                   <Badge colorScheme="red" fontSize="xs" mt={1}>
//                     Required fields
//                   </Badge>
//                 )}
//               </Box>
//             ))}
//           </Stack>
//         </Box>

//         {/* Modal for question configuration */}
//         <CustomModal
//           w={{ base: '30%', md: '9rem' }}
//           open={open}
//           onOpenChange={onClose}
//         >
//           <Box width={'100%'}>
//             {renderQuestionModalBody(selectedQuestionType)}
//           </Box>
//         </CustomModal>
//       </Box>
//     </DndProviderWrapper>
//   );
// };

// export default Questions;
