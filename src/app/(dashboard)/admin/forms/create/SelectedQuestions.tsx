import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>u<PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>u<PERSON><PERSON><PERSON><PERSON>,
  MenuTrigger,
  Text,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { CiEdit } from 'react-icons/ci';
import { MdOutlineDelete } from 'react-icons/md';
import { RxTextAlignJustify } from 'react-icons/rx';
import { TbPageBreak } from 'react-icons/tb';
import iconMap from '../_components/iconMap';

interface Question {
  id: number;
  qt: string;
  type: string;
  required: boolean;
  icon: string;
  isDefault?: boolean;
  page: number;
  [key: string]: any;
}

interface SelectedQuestionsProps {
  allSelectedQuestions: Question[];
  removeSelectedQuestion: (id: number) => void;
  handleQuestionClick: (question: Question) => void;
  setAllSelectedQuestions: (
    questions: Question[] | ((prev: Question[]) => Question[])
  ) => void;
  currentPage: number;
  totalPages: number;
  moveQuestionToPage?: (questionId: string, targetPage: number) => void;
}

const ItemTypes = {
  QUESTION: 'question',
};

const DraggableQuestion: React.FC<{
  q: Question;
  index: number;
  removeSelectedQuestion: (id: number) => void;
  handleQuestionClick: (question: Question) => void;
  moveQuestion: (dragIndex: number, hoverIndex: number) => void;
  currentPage: number;
  totalPages: number;
  moveQuestionToPage?: (questionId: string, targetPage: number) => void;
}> = ({
  q,
  index,
  removeSelectedQuestion,
  handleQuestionClick,
  moveQuestion,
  currentPage,
  totalPages,
  moveQuestionToPage,
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const isDefault = q.default === 'true';

  console.log('isDefault', isDefault, 'q', q);

  const ref = React.useRef<HTMLDivElement>(null);

  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.QUESTION,
    item: { index },

    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: ItemTypes.QUESTION,
    hover(item: { index: number }, monitor) {
      if (!ref.current) return;
      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();

      if (!clientOffset) return;

      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

      moveQuestion(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  drag(drop(ref));

  const handlePageMove = (newPage: number) => {
    if (moveQuestionToPage) {
      moveQuestionToPage(q.id.toString(), newPage);
    }
  };

  return isDefault ? (
    <Box
      ref={ref}
      position="relative"
      p={4}
      borderWidth="1px"
      mb={'10px'}
      borderRadius="md"
      boxShadow="md"
      bg="gray.100"
      w="100%"
      opacity={isDragging ? 0.5 : 1}
      cursor="not-allowed"
    >
      <Text fontWeight="bold" fontSize="lg">
        {iconMap[q.icon] || null}
      </Text>
      <Text color="gray.500">{q?.qt}</Text>
      <Text fontSize="sm" color="gray.400">
        {q?.required ? 'Required' : 'Optional'} - {q?.heading} (Default)
      </Text>
      <Text fontSize="xs" color="gray.500" mt={1}>
        Page {q.page || currentPage}
      </Text>
    </Box>
  ) : (
    <Box
      ref={ref}
      position="relative"
      p={4}
      borderWidth="1px"
      mb={'10px'}
      borderRadius="md"
      boxShadow="md"
      bg="white"
      w="100%"
      opacity={isDragging ? 0.5 : 1}
      onMouseEnter={() => setHoveredIndex(index)}
      onMouseLeave={() => setHoveredIndex(null)}
      _hover={{
        bg: 'primary.500',
        color: '#fff',
        cursor: isDragging ? 'grabbing' : 'move',
      }}
    >
      {hoveredIndex === index && (
        <Box position="absolute" top={2} right={2} display="flex" gap={2}>
          <Box
            onClick={(e) => {
              e.stopPropagation();
              handleQuestionClick(q);
            }}
            p={1}
            borderRadius="md"
            color={'#000'}
            bg="#f2f2f2"
            _hover={{ bg: '#e0e0e0' }}
            cursor="pointer"
          >
            <CiEdit size={16} />
          </Box>

          {totalPages > 1 && (
            <MenuRoot>
              <MenuTrigger>
                <Box
                  p={1}
                  borderRadius="md"
                  color={'#000'}
                  bg="#f2f2f2"
                  cursor="pointer"
                >
                  <TbPageBreak size={16} />
                </Box>
              </MenuTrigger>
              <MenuContent
                position="absolute"
                right={0}
                zIndex={1000}
                bg="white"
                boxShadow="md"
                width={'100%'}
                bgColor={'white'}
              >
                <Text fontSize="xs" px={3} py={1} color="gray.600">
                  Move to page:
                </Text>
                <MenuSeparator />
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (pageNum) => (
                    <MenuItem
                      value="edit"
                      key={pageNum}
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePageMove(pageNum);
                      }}
                      bg="transparent"
                      _hover={{ bg: 'gray.100', cursor: 'pointer' }}
                      _focus={{ bg: 'gray.100', cursor: 'pointer' }}
                    >
                      Page {pageNum}
                    </MenuItem>
                  )
                )}
              </MenuContent>
            </MenuRoot>
          )}

          <Box
            onClick={(e) => {
              e.stopPropagation();
              removeSelectedQuestion(q.id);
            }}
            p={1}
            borderRadius="md"
            color={'#000'}
            bg="#f2f2f2"
            _hover={{ bg: '#e0e0e0' }}
            cursor="pointer"
          >
            <MdOutlineDelete size={16} />
          </Box>
        </Box>
      )}

      <Text fontWeight="bold" fontSize="lg">
        {hoveredIndex === index ? (
          <RxTextAlignJustify size={20} />
        ) : (
          iconMap[q.icon] || null
        )}
      </Text>
      <Text>{q?.qt}</Text>
      <Flex justify="space-between" align="center" mt={2}>
        <Text
          fontSize="sm"
          color={hoveredIndex === index ? 'white' : 'gray.500'}
        >
          {q?.required ? 'Required' : 'Optional'} - {q?.heading}
        </Text>
        <Text
          fontSize="xs"
          color={hoveredIndex === index ? 'white' : 'blue.500'}
          fontWeight="500"
        >
          Page {q.page || currentPage}
        </Text>
      </Flex>
    </Box>
  );
};

const SelectedQuestions: React.FC<SelectedQuestionsProps> = ({
  allSelectedQuestions,
  removeSelectedQuestion,
  handleQuestionClick,
  setAllSelectedQuestions,
  currentPage,
  totalPages,
  moveQuestionToPage,
}) => {
  const moveQuestion = (dragIndex: number, hoverIndex: number) => {
    setAllSelectedQuestions((prevQuestions: Question[]) => {
      const newQuestions = [...prevQuestions];
      const draggedItem = newQuestions[dragIndex];

      // Remove the dragged item
      newQuestions.splice(dragIndex, 1);
      // Insert it at the hover position
      newQuestions.splice(hoverIndex, 0, draggedItem);

      return newQuestions;
    });
  };

  // Filter questions for the current page
  const currentPageQuestions = allSelectedQuestions
    .filter((q) => q.page === currentPage)
    .sort((a, b) => {
      // Sort by some order property if available, or by id as fallback
      if (a.order !== undefined && b.order !== undefined) {
        return a.order - b.order;
      }
      return a.id - b.id;
    });

  return (
    <Box width="100%">
      {currentPageQuestions.length === 0 ? (
        <Box
          p={4}
          borderWidth="1px"
          borderRadius="md"
          borderColor="gray.200"
          textAlign="center"
          bg="gray.50"
          mb={4}
        >
          <Text color="gray.500">No questions on this page yet</Text>
        </Box>
      ) : (
        currentPageQuestions.map((q, index) => (
          <DraggableQuestion
            key={q.id}
            q={q}
            index={index}
            removeSelectedQuestion={removeSelectedQuestion}
            handleQuestionClick={handleQuestionClick}
            moveQuestion={moveQuestion}
            currentPage={currentPage}
            totalPages={totalPages}
            moveQuestionToPage={moveQuestionToPage}
          />
        ))
      )}
    </Box>
  );
};

export default SelectedQuestions;
