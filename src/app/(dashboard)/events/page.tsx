import React from 'react';
import Events from './Events';
import { getAllEventsById } from '@/api/events';
import { createSupabaseServer } from '@/lib/supabase/server';
import { getUserByEmail } from '@/app/service/user';
import { Metadata } from 'next';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default async function page() {
  const { auth } = createSupabaseServer();
  const user = await auth.getUser();
  const userFromServer = await getUserByEmail(
    user?.data?.user?.email as string
  );
  const data = await getAllEventsById(userFromServer);

  return (
    <div style={{ paddingTop: '30px' }}>
      <Events events={data} userFromServer={userFromServer} />
    </div>
  );
}
