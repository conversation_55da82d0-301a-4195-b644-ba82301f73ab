'use client';

import Questions from '@/app/(dashboard)/admin/forms/create/Questions';
import AvailabilityForm from '@/app/(dashboard)/events/_components/AvailabilityForm';
import CustomTextArea from '@/components/Input/CustomTextArea';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { env } from '@/constants/env';
import { getSlugFromName } from '@/utils/event';
import { Box, Flex, Heading, Icon, Stack, Text } from '@chakra-ui/react';
import moment from 'moment-timezone';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { IoArrowBackOutline } from 'react-icons/io5';
import { useEditEvent } from '../../_hook/useEditEvents';
// import { Checkbox } from '@/components/ui/checkbox';
// import { useIntegrations } from '@/app/(dashboard)/integrations/useIntegrations';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import LinkServiceModal from '../../create/LinkServiceModal';

export default function EditEvents({
  eventDetails,
  userFromServer,
}: {
  eventDetails: any;
  userFromServer: any;
}) {
  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const router = useRouter();
  // const { handleStripeConnect } = useIntegrations(userFromServer);
  const editEventHook = useEditEvent(eventDetails, userFromServer);

  const {
    loading,
    handleChange,
    errors,
    touched,
    handleSubmit,
    setFieldValue,
    setErrors,
    values,
    selectedQuestionType,
    handleQuestionClick,
    removeSelectedQuestion,
    setAllSelectedQuestions,
    onClose,
    open,
    allSelectedQuestions,
    linkServiceDisclosure,
  } = editEventHook;

  console.log('values is ', values);
  console.log(' allSelectedQuestions', allSelectedQuestions);

  return (
    <div>
      <Flex alignItems={'center'} justify={'space-between'}>
        <Stack alignItems={'flex-start'}>
          <Flex
            cursor={'pointer'}
            onClick={() => router.back()}
            alignItems={'center'}
            gap={'.51rem'}
            my={'1rem'}
          >
            <Link href={'/events'}>
              <Icon boxSize={'1rem'}>
                <IoArrowBackOutline />
              </Icon>
            </Link>

            <Text>Go back</Text>
          </Flex>

          <Box>
            <Heading mb={'.5rem'} fontWeight={500} fontSize={'2rem'}>
              {eventDetails?.title}
            </Heading>
            <Text>Edit this event</Text>
          </Box>
        </Stack>

        {/* <Button>Save Changes</Button> */}
      </Flex>

      <Box
        maxW={'60%'}
        mx={'auto'}
        boxShadow={'sm'}
        rounded={'md'}
        minH={'10rem'}
        mt={'2rem'}
        py={'2rem'}
        mb={'2rem'}
      >
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
        >
          <Stack gap={'1rem'}>
            <Box px={'2rem'}>
              <StringInput
                fieldProps={{
                  invalid: touched.title && !!errors.title,
                  label: 'Event Title',
                  required: true,
                  // readOnly: true,
                }}
                inputProps={{
                  // readOnly: true,
                  name: 'title',
                  value: values.title,
                  onChange: (e) => {
                    handleChange(e);
                    setFieldValue('slug', getSlugFromName(e.target.value));
                  },
                }}
              />
            </Box>
            <Box px={'2rem'}>
              <StringInput
                fieldProps={{
                  label: `Event Url (${env.FRONTEND_URL}/book/${userFromServer.event_slug}/)`,
                  // readOnly: true,
                }}
                inputProps={{
                  name: 'slug',
                  value: values.slug,
                  onChange: handleChange,
                  // readOnly: true,
                }}
              />
            </Box>
            <Box px={'2rem'}>
              <CustomTextArea
                fieldProps={{
                  invalid: touched.description && !!errors.description,
                  label: 'Event Description',
                  required: true,
                }}
                inputProps={{
                  name: 'description',
                  value: values.description,
                  onChange: handleChange,
                }}
              />
            </Box>

            {/* ==================================== SERVICE FUNCTIONALITY ============================ */}
            {
              <Box px={'2rem'}>
                {values?.service ? (
                  <>
                    <Text fontWeight={500} mb={'.4rem'}>
                      Service Selected
                    </Text>
                    <Stack
                      onClick={linkServiceDisclosure.onOpen}
                      cursor={'pointer'}
                      _hover={{ bg: 'rgba(0,0,0,0.1)', border: 'none' }}
                      border={'1px solid black'}
                      rounded={'.3rem'}
                      p={'1rem'}
                    >
                      <Flex alignItems={'center'}>
                        <Text minW={'4rem'} fontWeight={500} opacity={'.7'}>
                          Name:
                        </Text>
                        <Text>{values?.service?.name}</Text>
                      </Flex>
                      <Flex alignItems={'center'}>
                        <Text minW={'4rem'} fontWeight={500} opacity={'.7'}>
                          Price:
                        </Text>
                        <Text>${values?.service?.price}</Text>
                      </Flex>
                    </Stack>
                  </>
                ) : (
                  <Text
                    onClick={linkServiceDisclosure.onOpen}
                    color={'primary.500'}
                    cursor={'pointer'}
                    fontWeight={500}
                  >
                    Link a service
                  </Text>
                )}
              </Box>
            }

            {/* {values?.product && (
              <Box px={'2rem'}>
                {userFromServer?.organization?.stripe_user_id ? (
                  <Checkbox
                    onChange={(e: any) => {
                      console.log('e is ', e.target);
                      setFieldValue('accept_payment', e.target.checked);
                    }}
                    checked={values?.accept_payment}
                  >
                    <Text fontSize={'1rem'}>Accept payments with Stripe</Text>
                  </Checkbox>
                ) : (
                  <Box
                    color={'primary.500'}
                    fontWeight={'semibold'}
                    onClick={handleStripeConnect}
                    _hover={{ bg: 'transparent' }}
                    cursor={'pointer'}
                  >
                    Connect Stripe To Accept Payment
                  </Box>
                )}
              </Box>
            )} */}
            {/* ==================================== PRODUCT FUNCTIONALITY ============================ */}
            <Box px={'2rem'}>
              <StringInput
                fieldProps={{
                  invalid: touched.duration && !!errors.duration,
                  label: 'Duration (minutes)',
                  required: true,
                }}
                inputProps={{
                  name: 'duration',
                  value: values.duration,
                  onChange: handleChange,
                  type: 'number',
                }}
              />
            </Box>

            <Box px={'2rem'}>
              <Text fontWeight={500} mb={'1rem'}>
                Availability
              </Text>
              <AvailabilityForm
                setErrors={setErrors}
                setFieldValue={setFieldValue}
                initialValues={values.availability.map((item: any) => ({
                  available: item.available,
                  day: item.day,
                  id: item.id,
                  endTime: moment
                    .utc(item.end_time)
                    .tz(userTimezone)
                    .format('HH:mm'),
                  startTime: moment
                    .utc(item.start_time)
                    .tz(userTimezone)
                    .format('HH:mm'),
                }))}
              />
            </Box>
            <Box px={'2rem'}>
              <Questions
                type="event"
                formHook={{
                  open,
                  onClose,
                  handleQuestionClick,
                  selectedQuestionType,
                  allSelectedQuestions,
                  removeSelectedQuestion,
                  setAllSelectedQuestions,
                  currentPage: 1,
                }}
              />
            </Box>

            <Box mt={'1rem'} px={'2rem'}>
              <Button
                loading={loading}
                type="submit"
                disabled={Object.keys(errors).length > 0}
              >
                Save Changes
              </Button>
            </Box>
          </Stack>
        </form>
      </Box>

      <CustomModal
        w={{ base: '90%', md: '40rem' }}
        open={linkServiceDisclosure.open}
        onOpenChange={linkServiceDisclosure.onClose}
      >
        <LinkServiceModal createEventHook={editEventHook} />
      </CustomModal>
    </div>
  );
}
