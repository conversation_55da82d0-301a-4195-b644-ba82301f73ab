import { createAvailabilityApi } from '@/api/availability';
import { createEventApi } from '@/api/events/create-event';
// import { useCreateProductMutation } from '@/api/products/create-products';
// import { useGetAllProductsQuery } from '@/api/products/get-all-products';
import { useCreateServiceMutation } from '@/api/services/create-service';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { toaster } from '@/components/ui/toaster';
import { eventSchema } from '@/form-handling/validations/events';
import useFetch from '@/hooks/utils/use-fetch';
import { getSlugFromName } from '@/utils/event';
import { useDisclosure } from '@chakra-ui/react';
import { useFormik } from 'formik';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { FormEvent, useState } from 'react';

export const useCreateEvent = ({ userFromServer }: any) => {
  const router = useRouter();
  const { onClose, onOpen, open } = useDisclosure();
  const linkServiceDisclosure = useDisclosure();
  const [isExisting, setIsExisting] = useState(true);

  const [allSelectedQuestions, setAllSelectedQuestions] = useState<any>([
    {
      id: 1,
      qt: 'Email',
      required: true,
      type: 'Textbox',
      icon: 'TfiText',
      default: 'true',
      heading: 'Short answer',
      page: 1,
    },
    {
      id: 2,
      qt: 'First name',
      required: true,
      type: 'Textbox',
      icon: 'TfiText',
      default: 'true',
      heading: 'Short answer',
      page: 1,
    },
    {
      id: 3,
      qt: 'Last name',
      required: true,
      type: 'Textbox',
      icon: 'TfiText',
      default: 'true',
      heading: 'Short answer',
      page: 1,
    },
  ]);

  const generateUniqueId = () => {
    return `q-${moment().valueOf()}-${Math.floor(Math.random() * 900) + 100}`;
    // Example: "q-1712345678901-427"
  };

  const [selectedQuestionType, setSelectedQuestionType] = useState<any>(null);
  const { data: AllServices } = useGetServicesQuery(
    userFromServer?.organization_id,
    {
      enabled: Boolean(userFromServer?.organization_id),
    }
  );
  const { loading: CreateEventLoading, fn: fnCreateEvent } =
    useFetch(createEventApi);
  const { loading: CreateAvailabilityLoading, fn: fnCreateAvailability } =
    useFetch(createAvailabilityApi);
  const { mutateAsync: CreateServiceApi, isLoading: CreateServiceLoading } =
    useCreateServiceMutation();

  const createEventFormik = useFormik<any>({
    validationSchema: eventSchema,
    initialValues: {
      duration: 30,
      existingService: false,
      service: null,
    },
    onSubmit: async (values) => {
      console.log('values in submit is ', { ...values });
      const service = values?.service;
      let service_id = null;
      // return;
      // Product is selected
      if (
        values?.accept_payment &&
        !userFromServer?.organization?.stripe_user_id
      ) {
        toaster.create({
          description: 'Please Connect Stripe',
          type: 'error',
        });
        return;
      }
      if (service) {
        // Existing product
        if (service?.id) {
          service_id = service.id;
        } else {
          const response = await CreateServiceApi({
            ...service,
            duration_minutes: values?.duration,
            organization_id: userFromServer?.organization_id,
          });
          console.log('response is ', response);
          service_id = response?.data?.id;
        }
      }
      // return;

      const CreateEventData = await fnCreateEvent({
        title: values.title,
        accept_payment: values.accept_payment,
        description: values.description,
        duration: Number(values.duration),
        slug: values.slug || getSlugFromName(values.title),
        organization_name: getSlugFromName(userFromServer.organization.name),
        organization_id: userFromServer.organization_id,
        form_config: allSelectedQuestions,
        creator_id: userFromServer.id,
        creator_slug: userFromServer?.event_slug,
        service_id,
      });
      // console.log('event is ', {
      //   title: values.title,
      //   description: values.description,
      //   duration: Number(values.duration),
      //   slug: values.slug || getSlugFromName(values.title),
      //   organization_name: getSlugFromName(userFromServer.organization.name),
      //   organization_id: userFromServer.organization_id,
      //   form_config: allSelectedQuestions,
      //   creator_id: userFromServer.id,
      //   creator_slug: userFromServer?.event_slug,
      // });
      // console.log('availabilities is ', {
      //   event_id: CreateEventdata?.id,
      //   availabilities: values.availabilities,
      // });

      await fnCreateAvailability({
        event_id: CreateEventData?.id,
        availabilities: values.availabilities,
      });
      router.push('/events');
      router.refresh();
    },
  });

  const handleCreateNewService = (e: FormEvent) => {
    e.preventDefault();
    const service = createEventFormik.values?.service;
    console.log('service is ', service);

    if (
      !service?.price ||
      !service?.name ||
      !createEventFormik?.values?.duration
    ) {
      toaster.create({
        description: 'Invalid inputs',
        type: 'error',
      });
      return;
    }
    linkServiceDisclosure.onClose();
  };
  const handleQuestionClick = (q: any) => {
    setSelectedQuestionType(q);
    onOpen();
  };
  const removeSelectedQuestion = (id: any) => {
    setAllSelectedQuestions((prev: any) =>
      prev.filter((q: any) => q.id !== id)
    );
  };
  // console.log(
  //   'allSelectedQuestions from create event is',
  //   allSelectedQuestions
  // );
  //   console.log('formik error is ', createEventFormik.errors);
  // console.log('values is ', createEventFormik.values);

  const addQuestionToCurrentPage = (questionData: any) => {
    console.log('questionData', questionData);
    const questionWithPage = {
      ...questionData,
      page: 1,
    };

    setAllSelectedQuestions((prev: any) => {
      const isEdit = prev.some((q: any) => q.id === questionData.id);
      if (isEdit) {
        return prev.map((q: any) =>
          q.id === questionData.id ? questionWithPage : q
        );
      } else {
        return [...prev, questionWithPage];
      }
    });
  };

  return {
    createEventFormik,
    CreateEventLoading,
    CreateAvailabilityLoading,
    setAllSelectedQuestions,
    handleQuestionClick,
    open,
    onClose,
    addQuestionToCurrentPage,
    generateUniqueId,
    selectedQuestionType,
    allSelectedQuestions,
    removeSelectedQuestion,
    linkServiceDisclosure,
    AllServices,
    handleCreateNewService,
    isExisting,
    setIsExisting,
    CreateServiceLoading,
  };
};

export type TCreateEventHook = ReturnType<typeof useCreateEvent>;
