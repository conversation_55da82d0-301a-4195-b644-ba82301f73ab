/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable unused-imports/no-unused-vars */
import { useCreateServiceMutation } from '@/api/services/create-service';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';
import { toaster } from '@/components/ui/toaster';
import { eventSchema } from '@/form-handling/validations/events';
import { useDisclosure } from '@chakra-ui/react';
import { useFormik } from 'formik';
import { useRouter } from 'next/navigation';
import { FormEvent, useEffect, useState } from 'react';

export const useEditEvent = (eventDetails: any, userFromServer: any) => {
  console.log('eventDetails', eventDetails);
  const { onClose, onOpen, open } = useDisclosure();
  const [loading, setLoading] = useState(false);
  const [isExisting, setIsExisting] = useState(true);

  const router = useRouter();
  const [allSelectedQuestions, setAllSelectedQuestions] = useState<any>(
    eventDetails?.form_config
  );

  console.log('allSelectedQuestions', allSelectedQuestions);

  const [selectedQuestionType, setSelectedQuestionType] = useState<any>(null);
  const handleQuestionClick = (q: any) => {
    setSelectedQuestionType(q);
    onOpen();
  };
  const { data: AllServices } = useGetServicesQuery(
    userFromServer?.organization_id,
    {
      enabled: Boolean(userFromServer?.organization_id),
    }
  );
  const { mutateAsync: CreateServiceApi, isLoading: CreateServiceLoading } =
    useCreateServiceMutation();
  const removeSelectedQuestion = (id: any) => {
    setAllSelectedQuestions((prev: any) =>
      prev.filter((q: any) => q.id !== id)
    );
  };
  const linkServiceDisclosure = useDisclosure();

  const {
    values,
    handleChange,
    errors,
    touched,
    handleSubmit,
    setFieldValue,
    setErrors,
  } = useFormik<any>({
    validationSchema: eventSchema,

    initialValues: {
      ...eventDetails,
    },
    onSubmit: async () => {
      //   console.log('values is ', { ...values });
      try {
        setLoading(true);
        let service_id = values?.service?.id || null;

        const {
          availabilities,
          availability,
          created_at,
          service,
          ...editedEvent
        } = values;
        const newService = service && !service?.id;

        console.log('new service  is ', newService);
        // return;

        const editedAvailability = availabilities.map((item: any) => ({
          available: item.available,
          id: item.id,
          day: item.day,
          start_time: item.startTime,
          end_time: item.endTime,
        }));

        if (newService) {
          console.log('trying to create new service ');
          // return;

          const response = await CreateServiceApi({
            ...service,
            duration_minutes: values?.duration,
            organization_id: userFromServer?.organization_id,
          });
          service_id = response?.data?.id;
        }
        // console.log('editedAvailability is', editedAvailability);
        console.log('payload is ', {
          ...editedEvent,
          service_id,
          updated_at: new Date(),
          form_config: allSelectedQuestions,
        });

        // return;

        const editEventRes = await fetch(`/api/events/${eventDetails.id}`, {
          method: 'PATCH',
          body: JSON.stringify({
            payload: {
              ...editedEvent,
              service_id: values?.service?.id || null,
              updated_at: new Date(),
              form_config: allSelectedQuestions,
            },
          }),
          cache: 'no-store',
        });
        if (!editEventRes.ok) {
          throw new Error(await editEventRes.json());
        }
        const editAvailabilityRes = await fetch(`/api/availability`, {
          method: 'PATCH',
          body: JSON.stringify({
            payload: editedAvailability,
          }),
          cache: 'no-store',
        });
        if (!editAvailabilityRes.ok) {
          throw new Error(await editAvailabilityRes.json());
        }
        router.push('/events');
        router.refresh();
      } catch (error: any) {
        toaster.create({
          type: 'error',
          description: error?.message,
        });
      } finally {
        setLoading(false);
      }
    },
  });

  const handleCreateNewService = (e: FormEvent) => {
    e.preventDefault();
    const service = values?.service;
    console.log('service is ', service);

    if (!service?.price || !service?.name || !values?.duration) {
      toaster.create({
        description: 'Invalid inputs',
        type: 'error',
      });
      return;
    }
    linkServiceDisclosure.onClose();
  };

  useEffect(() => {
    if (eventDetails?.service_id && AllServices?.services?.length > 0) {
      const selectedService = AllServices?.services?.find(
        (item: any) => item?.id === eventDetails?.service_id
      );

      setFieldValue('service', selectedService);
    }
  }, [eventDetails, AllServices, setFieldValue]);

  const addQuestionToCurrentPage = (questionData: any) => {
    console.log('questionData', questionData);
    const questionWithPage = {
      ...questionData,
      page: 1,
    };

    setAllSelectedQuestions((prev: any) => {
      const isEdit = prev.some((q: any) => q.id === questionData.id);
      if (isEdit) {
        return prev.map((q: any) =>
          q.id === questionData.id ? questionWithPage : q
        );
      } else {
        return [...prev, questionWithPage];
      }
    });
  };
  return {
    loading,
    handleChange,
    errors,
    touched,
    handleSubmit,
    addQuestionToCurrentPage,
    setFieldValue,
    setErrors,
    values,
    selectedQuestionType,
    handleQuestionClick,
    removeSelectedQuestion,
    onOpen,
    onClose,
    allSelectedQuestions,
    setAllSelectedQuestions,
    open,
    linkServiceDisclosure,
    handleCreateNewService,
    setIsExisting,
    isExisting,
    AllServices,
    createEventFormik: {
      values,
      handleChange,
      errors,
      touched,
      handleSubmit,
      setFieldValue,
      setErrors,
      CreateServiceLoading,
    },
  };
};

export type TEditEventHook = ReturnType<typeof useEditEvent>;
