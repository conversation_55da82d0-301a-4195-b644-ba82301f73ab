'use client';
import AvailabilityForm from '@/app/(dashboard)/events/_components/AvailabilityForm';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import CustomTextArea from '@/components/Input/CustomTextArea';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { env } from '@/constants/env';
import { getSlugFromName } from '@/utils/event';
import { Box, Flex, Heading, Icon, Stack, Text } from '@chakra-ui/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { IoArrowBackOutline } from 'react-icons/io5';
import Questions from '../../admin/forms/create/Questions';
import { useIntegrations } from '../../integrations/useIntegrations';
import { useCreateEvent } from '../_hook/useCreateEvent';
import LinkServiceModal from './LinkServiceModal';

export default function CreateEvent({ userFromServer }: any) {
  // const [availability, setAvailability] = useState<any>();
  const router = useRouter();
  const createEventHook = useCreateEvent({ userFromServer });
  const { handleStripeConnect } = useIntegrations(userFromServer);

  const {
    createEventFormik,
    CreateEventLoading,
    CreateAvailabilityLoading,
    open,
    onClose,
    handleQuestionClick,
    selectedQuestionType,
    allSelectedQuestions,
    removeSelectedQuestion,
    addQuestionToCurrentPage,
    generateUniqueId,
    setAllSelectedQuestions,
    linkServiceDisclosure,
    CreateServiceLoading,
  } = createEventHook;
  const {
    handleSubmit,
    touched,
    handleChange,
    values,
    setFieldValue,
    errors,
    setErrors,
  } = createEventFormik;
  console.log('errors is ', errors);
  console.log('values is ', values);

  return (
    <div>
      <Flex alignItems={'center'} justify={'space-between'}>
        <Stack alignItems={'flex-start'} w={'100%'}>
          <Flex
            cursor={'pointer'}
            onClick={() => router.back()}
            alignItems={'center'}
            gap={'.51rem'}
            my={'1rem'}
          >
            <Link href={'/events'}>
              <Icon boxSize={'1rem'}>
                <IoArrowBackOutline />
              </Icon>
            </Link>

            <Text>Go back</Text>
          </Flex>

          <Box>
            <Heading mb={'.5rem'} fontWeight={500} fontSize={'2rem'}>
              New Event
            </Heading>
            <Text>Create a new event</Text>
          </Box>
        </Stack>
      </Flex>
      <Box
        maxW={'60%'}
        mx={'auto'}
        boxShadow={'sm'}
        rounded={'md'}
        minH={'10rem'}
        mt={'2rem'}
        py={'2rem'}
        mb={'2rem'}
      >
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
        >
          <Stack gap={'1rem'}>
            <Box px={'2rem'}>
              <StringInput
                fieldProps={{
                  invalid: touched.title && !!errors.title,
                  label: 'Event Title',
                  required: true,
                }}
                inputProps={{
                  name: 'title',
                  value: values.title,
                  onChange: (e) => {
                    handleChange(e);
                    setFieldValue('slug', getSlugFromName(e.target.value));
                  },
                }}
              />
            </Box>
            <Box px={'2rem'}>
              <CustomTextArea
                fieldProps={{
                  invalid: touched.description && !!errors.description,
                  label: 'Event Description',
                  required: true,
                }}
                inputProps={{
                  name: 'description',
                  value: values.description,
                  onChange: handleChange,
                }}
              />
            </Box>
            <Box px={'2rem'}>
              <StringInput
                fieldProps={{
                  label: `Event Url (${env.FRONTEND_URL}/book/${userFromServer.event_slug}/)`,
                }}
                inputProps={{
                  name: 'slug',
                  value: values.slug,
                  onChange: handleChange,
                }}
              />
            </Box>

            {/* <Box px={'2rem'}>
              <CustomSelect
                placeholder="Select"
                onChange={(val) =>
                  setFieldValue('isProductAvailable', val?.value)
                }
                options={isProductAvailableOptions}
                required={true}
                label="Select Product"
              />
            </Box> */}
            <Box px={'2rem'}>
              {values?.service ? (
                <>
                  <Text fontWeight={500} mb={'.4rem'}>
                    Service Selected
                  </Text>
                  <Stack
                    onClick={linkServiceDisclosure.onOpen}
                    cursor={'pointer'}
                    _hover={{ bg: 'rgba(0,0,0,0.1)', border: 'none' }}
                    border={'1px solid black'}
                    rounded={'.3rem'}
                    p={'1rem'}
                  >
                    <Flex alignItems={'center'}>
                      <Text minW={'4rem'} fontWeight={500} opacity={'.7'}>
                        Name:
                      </Text>
                      <Text>{values?.service?.name}</Text>
                    </Flex>
                    <Flex alignItems={'center'}>
                      <Text minW={'4rem'} fontWeight={500} opacity={'.7'}>
                        Price:
                      </Text>
                      <Text>${values?.service?.price}</Text>
                    </Flex>
                  </Stack>
                </>
              ) : (
                <Text
                  onClick={linkServiceDisclosure.onOpen}
                  color={'primary.500'}
                  cursor={'pointer'}
                  fontWeight={500}
                >
                  Link a service
                </Text>
              )}
            </Box>

            {values?.service && (
              <Box px={'2rem'}>
                {userFromServer?.organization?.stripe_user_id ? (
                  <Checkbox
                    onChange={(e: any) => {
                      console.log('e is ', e.target);
                      setFieldValue('accept_payment', e.target.checked);
                    }}
                    checked={values?.accept_payment}
                  >
                    <Text fontSize={'1rem'}>Accept payments with Stripe</Text>
                  </Checkbox>
                ) : (
                  <Box
                    color={'primary.500'}
                    fontWeight={'semibold'}
                    onClick={handleStripeConnect}
                    _hover={{ bg: 'transparent' }}
                    cursor={'pointer'}
                  >
                    Connect Stripe To Accept Payment
                  </Box>
                )}
              </Box>
            )}
            <Box px={'2rem'}>
              <StringInput
                fieldProps={{
                  invalid: touched.duration && !!errors.duration,
                  label: 'Duration (minutes)',
                  required: true,
                  disabled: values.service,
                }}
                inputProps={{
                  name: 'duration',
                  value: values.duration,
                  onChange: handleChange,
                  type: 'number',
                }}
              />
            </Box>

            <Box px={'2rem'}>
              <Text fontWeight={500} mb={'1rem'}>
                Availability
              </Text>
              <AvailabilityForm
                setErrors={setErrors}
                setFieldValue={setFieldValue}
              />
            </Box>
            <Box px={'2rem'}>
              <Questions
                type="event"
                formHook={{
                  open,
                  onClose,
                  handleQuestionClick,
                  addQuestionToCurrentPage,
                  selectedQuestionType,
                  allSelectedQuestions,
                  removeSelectedQuestion,
                  generateUniqueId,
                  setAllSelectedQuestions,
                  currentPage: 1,
                }}
              />
            </Box>
            <Box mt={'1rem'} px={'2rem'}>
              <Button
                loading={
                  CreateEventLoading ||
                  CreateAvailabilityLoading ||
                  CreateServiceLoading
                }
                type="submit"
                disabled={Object.keys(errors).length > 0}
              >
                Save Changes
              </Button>
            </Box>
          </Stack>
        </form>
      </Box>
      <CustomModal
        w={{ base: '90%', md: '40rem' }}
        open={linkServiceDisclosure.open}
        onOpenChange={linkServiceDisclosure.onClose}
      >
        <LinkServiceModal createEventHook={createEventHook} />
      </CustomModal>
    </div>
  );
}
