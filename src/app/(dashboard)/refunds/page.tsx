import React from 'react';
import dynamic from 'next/dynamic';
import { Metadata } from 'next';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

const ViewAllRefunds = dynamic(() => import('./view-all'), {
  ssr: false,
});

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default function page() {
  return (
    <div>
      <ViewAllRefunds />
    </div>
  );
}
