/* eslint-disable unused-imports/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import NumericInput from '@/components/Input/NumericInput';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';

import {
  transactionMethodOptions,
  transactionTypeOptions,
} from '@/data/options';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import { TTransactionHook } from './_hook/useTransactions';
import SearchContact from '@/components/elements/search/SearchContact';
import { Checkbox } from '@/components/ui/checkbox';

export default function AddTransaction({
  transactionHook,
}: {
  transactionHook: TTransactionHook;
}) {
  const {
    createTransactionFormik,
    searchResult,
    setSearchResult,
    handleSelectClient,
    createTransaction,
    CreateTransactionLoading,
    packageOfferings,
    purchasePackageLoading,
    createDisclosure,
  } = transactionHook;
  const { values, errors, setFieldValue, handleBlur, handleChange } =
    createTransactionFormik;
  return (
    <div>
      <Text
        fontWeight={500}
        fontSize={'1.25rem'}
        textAlign={'center'}
        mb={'1rem'}
      >
        Add Transaction
      </Text>
      <form onSubmit={createTransaction}>
        <Stack gap={'1rem'}>
          <Box>
            <label className="font-medium text-gray-900">Lookup Client</label>
            <SearchContact
              setSearchResult={(e: any) => {
                setSearchResult(e);
              }}
              searchResult={searchResult}
              selectExistingUser={(item) => handleSelectClient(item)}
            />
            <Box mt={'1rem'}>
              {values.display_name && values.client_id && (
                <StringInput
                  inputProps={{
                    name: 'display_name',
                    value: values.display_name || '',
                    onChange: handleChange,
                  }}
                  fieldProps={{ label: 'Display name', disabled: true }}
                />
              )}
            </Box>
          </Box>
          <Checkbox
            display={'none'}
            onCheckedChange={(e) => {
              setFieldValue('purchase_package', e.checked);
            }}
            checked={values?.purchase_package}
            // my={'.8rem'}
          >
            <Text fontSize={'1rem'}>Purchase a Package</Text>
          </Checkbox>
          {values?.purchase_package ? (
            <CustomSelect
              placeholder="Packages"
              required={true}
              onChange={(val) => {
                setFieldValue('package_offering', val);
                console.log('val', val?.price);
                setFieldValue('amount', val?.price);
              }}
              options={packageOfferings}
              label="Packages"
              // defaultValue={statusOptions?.find(
              //   (item) => item.value?.toLocaleLowerCase() === 'active'
              // )}
            />
          ) : null}
          {values?.purchase_package && values?.package_offering && (
            <Box>
              <Stack gap={3}>
                <Box
                  fontWeight={'semibold'}
                  display={'flex'}
                  justifyContent={'space-between'}
                >
                  <Text>{values?.package_offering?.name}</Text>
                  <Text>${values?.package_offering?.price}</Text>
                </Box>
                {values.package_offering?.package_item?.map(
                  (packageItem: any, packageItemKey: any) => (
                    <Box
                      key={packageItemKey}
                      p={3}
                      borderWidth="1px"
                      borderRadius="md"
                      position="relative"
                    >
                      <Flex justifyContent="space-between" alignItems="center">
                        <Box w={'25rem'}>
                          <Box>
                            <Text fontWeight="medium">
                              {packageItem?.service?.name}
                            </Text>
                            <Text fontWeight="medium">
                              {packageItem?.service?.description}
                            </Text>
                          </Box>
                        </Box>
                        <Box>
                          <Text>{packageItem?.quantity}</Text>
                        </Box>
                      </Flex>
                    </Box>
                  )
                )}
              </Stack>
            </Box>
          )}

          {!values?.purchase_package ? (
            <>
              {' '}
              <StringInput
                inputProps={{
                  name: 'transaction_date',
                  type: 'date',
                  placeholder: 'Select Date',
                  onBlur: handleBlur,
                  value: values.transaction_date,
                  onChange: handleChange,
                }}
                fieldProps={{
                  label: 'Date',
                  required: true,
                }}
              />
              <NumericInput
                inputProps={{
                  name: 'Amount',
                  placeholder: 'Add Amount',
                }}
                onValueChange={(item: any) => {
                  console.log('item is ', item);
                  setFieldValue('amount', item.floatValue);
                }}
                fieldProps={{
                  label: 'Amount',
                  //   invalid: touched.title && !!errors.title,
                  //   errorText: errors.title,
                  required: true,
                }}
              />{' '}
            </>
          ) : null}
          <CustomSelect
            placeholder="Transaction Method"
            required={true}
            onChange={(val) => setFieldValue('payment_method', val?.value)}
            options={transactionMethodOptions}
            label="Transaction Method"
            // defaultValue={statusOptions?.find(
            //   (item) => item.value?.toLocaleLowerCase() === 'active'
            // )}
          />
          <CustomSelect
            placeholder="Transaction Type"
            // required={true}
            onChange={(val) => setFieldValue('transaction_type', val?.value)}
            options={transactionTypeOptions}
            label="Transaction Type"
            // defaultValue={statusOptions?.find(
            //   (item) => item.value?.toLocaleLowerCase() === 'active'
            // )}
          />
          {/* <CustomSelect
            placeholder="Status"
            required={true}
            onChange={(val) => `setFieldValue('status', val?.value)`}
            options={transactionStatusOptions}
            label="Status"
            // defaultValue={statusOptions?.find(
            //   (item) => item.value?.toLocaleLowerCase() === 'active'
            // )}
          /> */}
          {/* <StringInput
            inputProps={{
              name: 'invoiceNumber',
              placeholder: 'Invoice Number',

              onChange: (e) => {
                // handleChange(e);
                // setFieldValue('slug', getSlugFromName(e.target.value));
              },
            }}
            fieldProps={{
              label: 'Invoice Number',
              //   invalid: touched.title && !!errors.title,
              //   errorText: errors.title,
              // required: true,
            }}
          /> */}
          <CustomTextArea
            inputProps={{
              name: 'note',
              placeholder: 'Note',

              onChange: (e) => {
                setFieldValue('note', e.target.value);
                // handleChange(e);
                // setFieldValue('slug', getSlugFromName(e.target.value));
              },
            }}
            fieldProps={{
              label: 'Note',
              //   invalid: touched.title && !!errors.title,
              //   errorText: errors.title,
            }}
          />
          <Flex
            my={'1.8rem'}
            alignItems={'center'}
            justifyContent={'space-between'}
          >
            <Button
              onClick={createDisclosure?.onClose}
              variant={'outline'}
              minH={'3rem'}
              minW={{ base: '7rem', md: '15rem' }}
            >
              Cancel{' '}
            </Button>
            <Button
              loading={CreateTransactionLoading || purchasePackageLoading}
              minH={'3rem'}
              minW={{ base: '7rem', md: '15rem' }}
              type="submit"
              bg={'primary.500'}
            >
              Save
            </Button>
          </Flex>
          {/* <Button
            loading={CreateTransactionLoading || purchasePackageLoading}
            w={'100%'}
            type="submit"
          >
            Submit
          </Button> */}
        </Stack>
      </form>
    </div>
  );
}
