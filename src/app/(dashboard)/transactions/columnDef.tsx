import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { Box, Text } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import moment from 'moment-timezone';

import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import EditTransactions from './EditTransactions';

const ClientNameCell = ({ row }: { row: any }) => {
  const searchParams = useSearchParams();
  const organizationId = searchParams.get('organization_id');
  const client = row.original?.invoice?.client || row.original?.client;
  const clientId = row.original?.invoice?.client_id || row.original?.client_id;

  if (!client || !clientId) {
    return null;
  }

  const href = organizationId
    ? `/contacts/${clientId}?organization_id=${organizationId}`
    : `/contacts/${clientId}`;

  return (
    <Box
      color={'rgb(79 70 229)'}
      fontWeight={500}
      onClick={(e) => e.stopPropagation()}
    >
      <Link href={href}>
        {`${client.first_name || ''} ${client.last_name || ''}`}{' '}
      </Link>
    </Box>
  );
};

const columnHelper = createColumnHelper<any>();

export const createColumnDef = () => [
  columnHelper.display({
    cell: (props) => <ClientNameCell row={props.row} />,
    header: 'Name',
    id: 'name',
  }),
  columnHelper.accessor('amount', {
    cell: (info) => <Box>{formatMoney(info.getValue())}</Box>,
    header: 'Amount',
    id: 'amount',
  }),
  columnHelper.accessor('transaction_type', {
    cell: (info) => (
      <Box maxW="25rem" whiteSpace="normal" wordBreak="break-word">
        {info.getValue()}
      </Box>
    ),
    header: 'Type',
    id: 'transaction-type',
  }),
  columnHelper.accessor('payment_method', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Payment Method',
    id: 'payment-method',
  }),
  columnHelper.accessor('transaction_date', {
    cell: (info) => (
      <Box>
        {moment(info.getValue().split('T')[0]).format('MMM D, YYYY')}

        {/* {moment(info.getValue()).format('MMM D, YYYY')} */}
        {/* {moment.tz(info.getValue(), 'America/New_York').format('MMM D, YYYY')} */}
      </Box>
    ),
    header: 'Date',
    id: 'transaction_date',
  }),
  // columnHelper.display({
  //   id: 'is_package',
  //   cell: (props) => (
  //     <Box>{props.row.original?.is_package ? 'TRUE' : 'FALSE'}</Box>
  //   ),
  //   header: 'Package',
  // }),
  // columnHelper.accessor('status', {
  //   cell: (info) => (
  //     <Box>
  //       <Status
  //         name={info.getValue() as string}
  //         style={{
  //           backgroundColor:
  //             info.getValue()?.toLowerCase() === 'active'
  //               ? '#10B981'
  //               : undefined,
  //           color:
  //             info.getValue()?.toLowerCase() === 'active' ? 'white' : undefined,
  //         }}
  //       />
  //     </Box>
  //   ),
  //   header: 'Status',
  //   id: 'status',
  // }),
  columnHelper.accessor('invoice_id', {
    cell: (info) => (
      <Box>{info.getValue() ? <Text>Linked</Text> : <Text>Unlinked</Text>}</Box>
    ),
    header: 'Status',
    id: 'status',
  }),
  columnHelper.display({
    id: 'edit-transactions',
    cell: (props) => {
      return (
        <Box>
          <EditTransactions row={props.row.original} />
        </Box>
      );
    },
    header: 'Action',
  }),
];
