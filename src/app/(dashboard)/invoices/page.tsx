import React from 'react';
import Invoices from './Invoices';
import { Metadata } from 'next';
import { Box } from '@chakra-ui/react';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default function page() {
  return (
    <Box>
      <Invoices />
    </Box>
  );
}
