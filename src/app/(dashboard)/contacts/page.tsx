import dynamic from 'next/dynamic';
import { Metadata } from 'next';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

const AllContacts = dynamic(() => import('./AllContacts'), {
  ssr: false,
});

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default function page() {
  return (
    <div>
      <AllContacts />
    </div>
  );
}
