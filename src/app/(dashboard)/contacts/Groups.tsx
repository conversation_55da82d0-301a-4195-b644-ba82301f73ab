import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { useGroups } from '@/hooks/clients/useGroups';
import {
  Avatar,
  Box,
  Card,
  Flex,
  For,
  Heading,
  SimpleGrid,
  Text,
} from '@chakra-ui/react';
import React from 'react';
import { CreateGroup } from './CreateGroup';
import { AvatarGroup } from '@/components/ui/avatar';
import { ViewGroup } from './ViewGroup';

export function Groups() {
  const hook = useGroups();
  const { addDisclosure, data, viewDisclosure, setViewData } = hook;

  return (
    <Box pb={'20'}>
      <Flex
        alignItems={'flex-end'}
        justifyContent={'space-between'}
        flexWrap={'wrap'}
        gap={'4'}
      >
        <Box>
          <Heading fontSize={'2rem'}>Groups</Heading>
          <Text
            fontSize={'0.8rem'}
            mt={'1'}
            fontWeight={'medium'}
            color={'gray.500'}
          >
            All groups
          </Text>
        </Box>

        <Box>
          <Button
            minW={'10rem'}
            bg={'primary.500'}
            onClick={addDisclosure?.onOpen}
          >
            Create Group
          </Button>
        </Box>
      </Flex>
      <Box mt={'2rem'}>
        {/* <StringInput
          inputProps={{
            placeholder: 'Enter name',
            onChange: getClientHook.handleInputChange,
            value: getClientHook.search,
          }}
          fieldProps={{ label: 'Search for group' }}
        /> */}
      </Box>
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} gap={'4'} mt={'1rem'}>
        <For each={Array.isArray(data) ? data : []}>
          {(item: any) => (
            <Card.Root
              size="sm"
              key={item?.name}
              _hover={{ transform: 'scale(1.01)' }}
              cursor={'pointer'}
              onClick={() => {
                setViewData(item);
                viewDisclosure.onOpen();
              }}
            >
              <Card.Header>
                <Heading size="md" textTransform={'capitalize'}>
                  {item?.name}
                </Heading>
              </Card.Header>
              <Card.Body color="fg.muted">
                <AvatarGroup gap="0" spaceX="-3" size="lg">
                  {item?.data?.slice(0, 5)?.map((client: any) => (
                    <Avatar.Root key={client?.id}>
                      <Avatar.Fallback>
                        {`${client?.client_id?.first_name?.substring(0, 1)}${client?.client_id?.last_name?.substring(0, 1)}`}
                      </Avatar.Fallback>
                      <Avatar.Image src="" />
                    </Avatar.Root>
                  ))}
                </AvatarGroup>
                Total Client: {item?.data?.length}
              </Card.Body>
            </Card.Root>
          )}
        </For>
      </SimpleGrid>

      <CustomModal
        open={addDisclosure?.open}
        onOpenChange={addDisclosure?.onClose}
        headertext={'Create a new group'}
      >
        <CreateGroup hook={hook} />
      </CustomModal>
      <CustomModal
        open={viewDisclosure?.open}
        onOpenChange={viewDisclosure?.onClose}
        // headertext={`Group: ${viewData?.name}`}
      >
        <ViewGroup hook={hook} />
      </CustomModal>
    </Box>
  );
}
