import { useGetAllClientsQuery } from '@/api/clients/get-all-clients';
import { useGetAllMatchedClientsQuery } from '@/api/clients/get-all-matched-clients';
import { fetchInvoicesWithLimit } from '@/api/invoices';
import { useGetTagsQuery } from '@/api/tags/get-tags';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import { provinceOptions } from '@/data/options/consultations';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { useContactStages } from '@/hooks/clients/useGetContactsStages';
import { useCopy } from '@/hooks/ui/copy';
import supabase from '@/lib/supabase/client';
import { AllClientsFilterState } from '@/store/filters/clients';
import { getPrimaryEmail } from '@/utils/helper';
import { changeProvinceShortForm } from '@/utils/province-helper';
import { useDisclosure } from '@chakra-ui/react';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { useRecoilState, useResetRecoilState } from 'recoil';
// import { useGetContactsStages } from '@/hooks/clients/useGetContactsStages';
interface FilterOption {
  value: string | number;
  label: string;
}

interface FilterData {
  id: string;
  name: string;
  options: FilterOption[] | undefined;
  selected: (string | number)[];
}
export const useGetClientsHook = () => {
  const { contactStagesOptions, isLoading: isStagesLoading } =
    useContactStages(false);

  const [filter, setFilter] = useRecoilState(AllClientsFilterState);
  const resetFilter = useResetRecoilState(AllClientsFilterState);
  const [importFile, setImportFile] = useState<any>(null);
  const importFileRef = useRef<any>(null);
  const importDisclosure = useDisclosure();
  const { UserFromQuery } = useSupabaseSession();

  console.log('filter---4', filter);

  // console.log('contactStages', UserLoading, contactStages);
  const matchedPayload = {
    provinceFilter: filter.provinceFilter,
    stageFilter: filter.stageFilter,
    leadFilter: filter.leadFilter,
    slpFilter: filter.slpFilter,
    goal: filter.goal,
    group: filter.group,
    search: filter.search,
    active_clients: filter.active_clients,
  };
  const { data: allMatchedClients, isLoading: isMatchedLoading } =
    useGetAllMatchedClientsQuery(matchedPayload);

  const {
    data: Clients,
    isLoading: ClientsLoading,
    refetch: refetchAllClient,
  } = useGetAllClientsQuery(filter);

  console.log('Clients', Clients);
  const raw = localStorage.getItem('UserState');
  const data = raw ? JSON.parse(raw) : null;
  const org = data?.UserState?.organization;

  const { data: slpData, isLoading: slpIsLoading } = useGetAllSlpQuery({
    status: 'Active',
    role: 'therapist',
    organization_id: org?.id,
  });
  const { data: groups } = useGetTagsQuery({
    category: 'group',
    user_id: UserFromQuery?.id,
  }) as any;

  const [rowSelection, setRowSelection] = useState({});
  const [matchedRowSelection, setMatchedRowSelection] = useState({});
  const [selectAllMatchedContacts, setSelectAllMatchedContacts] =
    useState(false);
  const [allContactsSelected, setAllContactsSelected] = useState(false);

  const [search, setSearch] = useState('');
  const { copy } = useCopy();
  const [clientLoading, setClientLoading] = useState(false);

  const handleCopyAllMatchedContacts = () => {
    setSelectAllMatchedContacts(true);

    const newRowSelection = allMatchedClients?.data?.reduce(
      (acc: any, client: any) => {
        acc[client.id] = true; // Mark each client ID as selected
        return acc;
      },
      {}
    );

    setMatchedRowSelection(newRowSelection);
    // setRowSelection(newRowSelection);
  };

  const handleClearAllSelectedMatchedContacts = () => {
    setSelectAllMatchedContacts(false);
  };

  // checks if all the rows are selected

  useEffect(() => {
    if (rowSelection && Object.keys(rowSelection).length === filter.size) {
      setAllContactsSelected(true);
    } else {
      setAllContactsSelected(false);
    }
  }, [rowSelection, allContactsSelected, filter.size]);

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      setFilter((prevFilter) => {
        if (prevFilter.search !== search) {
          return { ...prevFilter, search };
        }
        return prevFilter;
      });
    }, 600);

    return () => clearTimeout(debounceTimer);
  }, [search, setFilter]);

  const handleInputChange = (event: any) => {
    setSearch(event.target.value);
    setFilter({ ...filter, currentPage: 1 });
  };

  // console.log('allMatchedClients', allMatchedClients?.data);
  // console.log('Clients', Clients?.data);

  //   if (Object.keys(rowSelection).length === 0) {
  //     toaster.create({
  //       description: 'No contacts selected!',
  //       type: 'error',
  //     });
  //     return;
  //   }

  //   const sourceData = selectAllMatchedContacts
  //     ? allMatchedClients?.data
  //     : Clients?.data;
  //   const selection = selectAllMatchedContacts
  //     ? matchedRowSelection
  //     : rowSelection;

  //   const arrayOfSelected = Object?.entries(selection).map(([key, value]) => {
  //     if (!value) return;
  //     return Number(key);
  //   });

  //   const selectedContactData = sourceData?.filter((_item: any, index: any) => {
  //     return arrayOfSelected.includes(Number(index));
  //   });
  //   // const isSelectAll = arrayOfSelected.length === filter.size;
  //   const finalData = selectAllMatchedContacts
  //     ? sourceData
  //     : selectedContactData;

  //   const csvContent =
  //     'data:text/csv;charset=utf-8,' +
  //     finalData
  //       .map((contact: any) => {
  //         const formattedDate = contact.lead_created
  //           ? moment
  //               .utc(contact.lead_created)
  //               .local()
  //               .format('MMM D, YYYY, h:mm A')
  //           : '';

  //         return `${contact.first_name},${contact.last_name},${contact.phone},${
  //           (contact?.client_emails &&
  //             getPrimaryEmail(contact?.client_emails)) ||
  //           contact.email ||
  //           contact?.initial_email ||
  //           ''
  //         },${changeProvinceShortForm(contact.province)},${contact.stage},${
  //           contact.stage === 'SQL' ? contact.lead_quality : ''
  //         },${formattedDate}`;
  //       })
  //       .join('\n');

  //   const encodedUri = encodeURI(csvContent);
  //   const link = document.createElement('a');
  //   link.setAttribute('href', encodedUri);
  //   link.setAttribute('download', 'contacts.csv');
  //   document.body.appendChild(link);
  //   link.click();
  // };

  // const copyToClipboard = () => {
  //   if (Object.keys(rowSelection).length === 0) {
  //     toaster.create({
  //       description: 'No contacts selected!',
  //       type: 'error',
  //     });
  //     return;
  //   }
  //   const arrayOfSelected = Object.entries(rowSelection).map(([key, value]) => {
  //     if (!value) return;
  //     return Number(key);
  //   });

  //   const selectedContactData = Clients?.data?.filter(
  //     (_item: any, index: any) => {
  //       return arrayOfSelected.includes(Number(index));
  //     }
  //   );

  //   const isSelectAll = arrayOfSelected.length === filter.size;
  //   const finalData = isSelectAll ? Clients?.data : selectedContactData;

  //   const formattedData = finalData
  //     .map((contact: any) => {
  //       return `${contact.first_name} ${contact.last_name} - ${
  //         contact.phone
  //       } - ${
  //         contact.email ||
  //         contact?.initial_email ||
  //         contact.client_emails ||
  //         contact.client_emails[0] ||
  //         contact.client_emails[0].email
  //       } - ${changeProvinceShortForm(contact.province)} - ${contact.stage} - ${
  //         contact.stage === 'SQL' ? contact.lead_quality : ''
  //       }`;
  //     })
  //     .join('\n');
  //   copy(formattedData);
  // };
  const exportAsCsv = () => {
    if (Object.keys(rowSelection).length === 0) {
      toaster.create({
        description: 'No contacts selected!',
        type: 'error',
      });
      return;
    }

    const sourceData = selectAllMatchedContacts
      ? allMatchedClients?.data
      : Clients?.data;
    const selection = selectAllMatchedContacts
      ? matchedRowSelection
      : rowSelection;

    const arrayOfSelected = Object.entries(selection)
      .filter(([, value]) => value)
      .map(([key]) => Number(key));

    const selectedContactData = sourceData?.filter((_item: any, index: any) => {
      return arrayOfSelected.includes(Number(index));
    });

    const finalData = selectAllMatchedContacts
      ? sourceData
      : selectedContactData;

    //CSV Headers
    const headers = [
      'First Name',
      'Last Name',
      'Phone',
      'Email',
      'Province',
      'Stage',
      'Total Lifetime',
      'Date Created',
    ];

    const rows = finalData.map((contact: any) => {
      const formattedDate = contact.lead_created
        ? moment.utc(contact.lead_created).local().format('MMM D, YYYY, h:mm A')
        : '';

      // Calculate total lifetime value from invoices
      const totalLifetime = contact.invoices
        ? contact.invoices.reduce(
            (sum: number, invoice: any) => sum + (invoice.total_price || 0),
            0
          )
        : '';

      return [
        contact.first_name || '',
        contact.last_name || '',
        contact.phone || '',
        (contact?.client_emails && getPrimaryEmail(contact?.client_emails)) ||
          getPrimaryEmail(contact?.emails) ||
          contact.email ||
          contact?.initial_email ||
          '',
        changeProvinceShortForm(contact.province) || '',
        contact.stage === 'SQL' ? contact.lead_quality || '' : '',
        totalLifetime,
        formattedDate,
      ]
        .map(
          (field) =>
            `"${field
              .toString()
              .replace(/"/g, '""')
              .replace(/\r?\n|\r/g, ' ')}"`
        ) // Escape quotes & remove newlines
        .join(',');
    });

    const csvContent = `data:text/csv;charset=utf-8,${[headers.join(','), ...rows].join('\n')}`;

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', 'contacts.csv');
    document.body.appendChild(link);
    link.click();
  };

  // const copyToClipboard = () => {
  //   if (Object.keys(rowSelection).length === 0) {
  //     toaster.create({
  //       description: 'No contacts selected!',
  //       type: 'error',
  //     });
  //     return;
  //   }

  //   const sourceData = selectAllMatchedContacts
  //     ? allMatchedClients?.data
  //     : Clients?.data;
  //   const selection = selectAllMatchedContacts
  //     ? matchedRowSelection
  //     : rowSelection;

  //   const arrayOfSelected = Object.entries(selection)
  //     .filter(([, value]) => value)
  //     .map(([key]) => Number(key));

  //   const selectedContactData = sourceData?.filter((_item: any, index: any) => {
  //     return arrayOfSelected.includes(Number(index));
  //   });

  //   // const isSelectAll = arrayOfSelected.length === filter.size;
  //   const finalData = selectAllMatchedContacts
  //     ? sourceData
  //     : selectedContactData;

  //   const formattedData = finalData
  //     ?.map((contact: any) => {
  //       const formattedDate = contact.lead_created
  //         ? moment
  //             .utc(contact.lead_created)
  //             .local()
  //             .format('MMM D, YYYY, h:mm A')
  //         : '';

  //       const row = [
  //         contact.first_name,
  //         contact.last_name,
  //         contact.phone,
  //         (contact?.client_emails && getPrimaryEmail(contact?.client_emails)) ||
  //           contact.email ||
  //           contact?.initial_email ||
  //           '',
  //         changeProvinceShortForm(contact.province),
  //         contact.stage,
  //       ];

  //       // Only include `lead_quality` if `stage` is `SQL`
  //       if (contact.stage === 'SQL') {
  //         row.push(contact.lead_quality);
  //       }

  //       // Add the formatted date as the last column
  //       row.push(formattedDate);

  //       return row
  //         .map((field) => (field ? `"${field}"` : '""')) // Wrap fields with quotes to handle commas within values
  //         .join(',');
  //     })
  //     .join('\n');
  //   copy(formattedData);
  // };
  const copyToClipboard = () => {
    if (Object.keys(rowSelection).length === 0) {
      toaster.create({
        description: 'No contacts selected!',
        type: 'error',
      });
      return;
    }

    const sourceData = selectAllMatchedContacts
      ? allMatchedClients?.data
      : Clients?.data;
    const selection = selectAllMatchedContacts
      ? matchedRowSelection
      : rowSelection;

    const arrayOfSelected = Object.entries(selection)
      .filter(([, value]) => value)
      .map(([key]) => Number(key));

    const selectedContactData = sourceData?.filter((_item: any, index: any) =>
      arrayOfSelected.includes(Number(index))
    );

    const finalData = selectAllMatchedContacts
      ? sourceData
      : selectedContactData;

    const formattedData = finalData
      ?.map((contact: any) => {
        const formattedDate = contact.lead_created
          ? moment
              .utc(contact.lead_created)
              .local()
              .format('MMM D, YYYY, h:mm A')
          : '';

        const row = [
          contact.first_name,
          contact.last_name,
          contact.phone,
          (contact?.client_emails && getPrimaryEmail(contact?.client_emails)) ||
            getPrimaryEmail(contact?.emails) ||
            contact.email ||
            contact?.initial_email ||
            '',
          changeProvinceShortForm(contact.province),
          contact.stage,
          contact.stage === 'SQL' ? contact.lead_quality : '',
          formattedDate,
        ]
          .map(
            (field) =>
              `"${
                field
                  ? field
                      .toString()
                      .replace(/"/g, '""') // Escape quotes
                      .replace(/\r?\n|\r/g, ' ') // Remove newlines
                  : ''
              }"`
          )
          .join(',');

        return row;
      })
      .join('\n');

    copy(formattedData);
    // toaster.create({
    //   description: 'Contacts copied to clipboard!',
    //   type: 'success',
    // });
  };

  const filtersData: FilterData[] = [
    {
      id: 'province',
      name: 'Province',
      options: provinceOptions,
      selected: filter.provinceFilter,
    },
    {
      id: 'stage',
      name: 'Stage',
      options: contactStagesOptions,
      selected: filter.stageFilter,
    },

    {
      id: 'lead_quality',
      name: 'Lead Quality',
      options: [
        { value: '', label: 'N/A' },
        { value: 'High', label: 'High' },
        { value: 'Medium', label: 'Medium' },
        { value: 'Low', label: 'Low' },
      ],
      selected: filter.leadFilter,
    },
    {
      id: 'slp',
      name: 'SLP',
      options: slpData?.map((item: any) => ({
        value: item?.id,
        label: `${item?.first_name} ${item?.last_name}`,
      })) as [],
      selected: filter.slpFilter,
    },
    {
      id: 'goal',
      name: 'Goals',
      options: [
        { value: '1', label: 'Accent' },
        { value: '2', label: 'Public Speaking/Presentation Skills' },
        { value: '3', label: 'Voice' },
        { value: '4', label: 'Speech Therapy (disorders)' },
        { value: '5', label: 'Clear Communication' },
        { value: '6', label: 'Professional Communication' },
      ],
      selected: filter.goal,
    },
    {
      id: 'group',
      name: 'Group',
      options: Array.isArray(groups)
        ? groups.map((item: any) => ({
            value: item?.name,
            label: `${item?.name}`,
          }))
        : [],
      selected: filter.group,
    },
    {
      id: 'active_clients',
      name: 'Status',
      options: [
        { value: 'true', label: 'Active' },
        { value: 'false', label: 'Inactive' },
      ],
      selected: filter.active_clients,
    },
  ];

  // const handleFilterChange = (event: any, filterType: any, value: any) => {
  //   switch (filterType) {
  //     case 'province':
  //       if (event.checked) {
  //         setFilter({
  //           ...filter,
  //           provinceFilter: [...filter.provinceFilter, value],
  //           currentPage: 1,
  //         });
  //       } else {
  //         setFilter({
  //           ...filter,
  //           provinceFilter: [
  //             ...filter.provinceFilter.filter((p) => p !== value),
  //           ],
  //         });
  //       }
  //       break;
  //     case 'slp':
  //       if (event.checked) {
  //         setFilter({
  //           ...filter,
  //           slpFilter: [...filter.slpFilter, value],
  //           currentPage: 1,
  //         });
  //       } else {
  //         setFilter({
  //           ...filter,
  //           slpFilter: [...filter.slpFilter.filter((p) => p !== value)],
  //         });
  //       }
  //       break;
  //     case 'goal':
  //       if (event.checked) {
  //         setFilter({
  //           ...filter,
  //           goal: [...filter.goal, value],
  //           currentPage: 1,
  //         });
  //       } else {
  //         setFilter({
  //           ...filter,
  //           goal: [...filter.goal.filter((p) => p !== value)],
  //         });
  //       }
  //       break;
  //     case 'stage':
  //       if (event.checked) {
  //         setFilter({
  //           ...filter,
  //           stageFilter: [...filter.stageFilter, value],
  //           currentPage: 1,
  //         });
  //       } else {
  //         setFilter({
  //           ...filter,
  //           stageFilter: [...filter.stageFilter.filter((p) => p !== value)],
  //         });
  //       }
  //       break;
  //     case 'lead_quality':
  //       if (event.checked) {
  //         setFilter({
  //           ...filter,
  //           leadFilter: [...filter.leadFilter, value],
  //           currentPage: 1,
  //         });
  //       } else {
  //         setFilter({
  //           ...filter,
  //           leadFilter: [...filter.leadFilter.filter((p) => p !== value)],
  //         });
  //       }
  //       break;
  //     case 'group':
  //       if (event.checked) {
  //         setFilter({
  //           ...filter,
  //           group: [...filter.group, value],
  //           currentPage: 1,
  //         });
  //       } else {
  //         setFilter({
  //           ...filter,
  //           group: [...filter.group.filter((p) => p !== value)],
  //         });
  //       }
  //       break;
  //     case 'active_clients':
  //       if (event.checked) {
  //         setFilter({
  //           ...filter,
  //           active_clients: [...filter.active_clients, value],
  //         });
  //       } else {
  //         setFilter({
  //           ...filter,
  //           active_clients: filter.active_clients.filter((p) => p !== value),
  //         });
  //       }
  //       break;
  //     default:
  //       break;
  //   }
  // };
  const handleFilterChange = (event: any, filterType: any, value: any) => {
    // This function is now only used for legacy compatibility
    // The actual filtering is handled by the modal's apply functionality
    // Remove the immediate filter updates since we want "apply on click" behavior

    switch (filterType) {
      case 'province':
        if (event.checked || event) {
          setFilter({
            ...filter,
            provinceFilter: [...filter.provinceFilter, value],
            currentPage: 1,
          });
        } else {
          setFilter({
            ...filter,
            provinceFilter: [
              ...filter.provinceFilter.filter((p) => p !== value),
            ],
          });
        }
        break;
      case 'slp':
        if (event.checked || event) {
          setFilter({
            ...filter,
            slpFilter: [...filter.slpFilter, value],
            currentPage: 1,
          });
        } else {
          setFilter({
            ...filter,
            slpFilter: [...filter.slpFilter.filter((p) => p !== value)],
          });
        }
        break;
      case 'goal':
        if (event.checked || event) {
          setFilter({
            ...filter,
            goal: [...filter.goal, value],
            currentPage: 1,
          });
        } else {
          setFilter({
            ...filter,
            goal: [...filter.goal.filter((p) => p !== value)],
          });
        }
        break;
      case 'stage':
        if (event.checked || event) {
          setFilter({
            ...filter,
            stageFilter: [...filter.stageFilter, value],
            currentPage: 1,
          });
        } else {
          setFilter({
            ...filter,
            stageFilter: [...filter.stageFilter.filter((p) => p !== value)],
          });
        }
        break;
      case 'lead_quality':
        if (event.checked || event) {
          setFilter({
            ...filter,
            leadFilter: [...filter.leadFilter, value],
            currentPage: 1,
          });
        } else {
          setFilter({
            ...filter,
            leadFilter: [...filter.leadFilter.filter((p) => p !== value)],
          });
        }
        break;
      case 'group':
        if (event.checked || event) {
          setFilter({
            ...filter,
            group: [...filter.group, value],
            currentPage: 1,
          });
        } else {
          setFilter({
            ...filter,
            group: [...filter.group.filter((p) => p !== value)],
          });
        }
        break;
      case 'active_clients':
        if (event.checked || event) {
          setFilter({
            ...filter,
            active_clients: [...filter.active_clients, value],
          });
        } else {
          setFilter({
            ...filter,
            active_clients: filter.active_clients.filter((p) => p !== value),
          });
        }
        break;
      default:
        break;
    }
  };
  const updateClient = async () => {
    setClientLoading(true);
    const allInvoices = await fetchInvoicesWithLimit();
    const groupedInvoices: any = allInvoices.reduce(
      (acc: any, invoice: any) => {
        const clientId = invoice.client_id;
        if (!acc[clientId]) {
          acc[clientId] = [];
        }
        acc[clientId].push(invoice);
        return acc;
      },
      {}
    );

    const latestInvoicesArray = Object.entries(groupedInvoices)
      .map(([clientId, clientInvoices]: any) => {
        const latestInvoice = clientInvoices.sort(
          (a: any, b: any) =>
            new Date(b.invoice_date).getTime() -
            new Date(a.invoice_date).getTime()
        )[0];
        const parsedClientId = parseInt(clientId, 10);

        return {
          id: !isNaN(parsedClientId) ? parsedClientId : null,
          active_slp: latestInvoice.slp_id,
        };
      })
      .filter((item) => item.id !== null && item.id !== undefined);
    // console.log('Latest invoices is ', latestInvoicesArray);
    await supabase.from(tableNames.clients).upsert(latestInvoicesArray, {
      onConflict: 'id',
    });

    setClientLoading(false);
  };

  // const handleSelectAll = (filterType: any, options: any[]) => {
  //   switch (filterType) {
  //     case 'province':
  //       setFilter({
  //         ...filter,
  //         provinceFilter: options.map((option) => option.value),
  //       });
  //       break;
  //     case 'slp':
  //       setFilter({
  //         ...filter,
  //         slpFilter: options.map((option) => option.value),
  //       });
  //       break;
  //     case 'goal':
  //       setFilter({
  //         ...filter,
  //         goal: options.map((option) => option.value),
  //       });
  //       break;
  //     case 'stage':
  //       setFilter({
  //         ...filter,
  //         stageFilter: options.map((option) => option.value),
  //       });
  //       break;
  //     case 'lead_quality':
  //       setFilter({
  //         ...filter,
  //         leadFilter: options.map((option) => option.value),
  //       });
  //       break;
  //     case 'group':
  //       setFilter({
  //         ...filter,
  //         group: options.map((option) => option.value),
  //       });
  //       break;
  //     case 'active_clients':
  //       setFilter({
  //         ...filter,
  //         active_clients: options.map((option) => option.value),
  //       });
  //       break;
  //     default:
  //       break;
  //   }
  // };
  const handleSelectAll = (filterType: any, options: any[]) => {
    // This is now handled directly in the modal component
    // Keep this function for backward compatibility but the modal handles its own select all
    switch (filterType) {
      case 'province':
        setFilter({
          ...filter,
          provinceFilter: options.map((option) => option.value),
        });
        break;
      case 'slp':
        setFilter({
          ...filter,
          slpFilter: options.map((option) => option.value),
        });
        break;
      case 'goal':
        setFilter({
          ...filter,
          goal: options.map((option) => option.value),
        });
        break;
      case 'stage':
        setFilter({
          ...filter,
          stageFilter: options.map((option) => option.value),
        });
        break;
      case 'lead_quality':
        setFilter({
          ...filter,
          leadFilter: options.map((option) => option.value),
        });
        break;
      case 'group':
        setFilter({
          ...filter,
          group: options.map((option) => option.value),
        });
        break;
      case 'active_clients':
        setFilter({
          ...filter,
          active_clients: options.map((option) => option.value),
        });
        break;
      default:
        break;
    }
  };
  return {
    data: {
      ...Clients,
      data: Array.isArray(Clients?.data)
        ? Clients.data.map((item: any) => ({
            ...item,
            name:
              item?.display_name ||
              (item?.first_name && item?.last_name
                ? `${item?.first_name} ${item?.last_name}`
                : `${item.first_name ?? ''} ${item.last_name ?? ''}`),
          }))
        : [],
    },
    isLoading: ClientsLoading || slpIsLoading || isStagesLoading,
    rowSelection,
    setRowSelection,
    refetchAllClient,
    handleInputChange,
    search,
    setSearch,
    exportAsCsv,
    copyToClipboard,
    allContactsSelected,
    allMatchedClients,
    isMatchedLoading,
    selectAllMatchedContacts,
    handleCopyAllMatchedContacts,
    handleClearAllSelectedMatchedContacts,
    filtersData,
    handleFilterChange,
    updateClient,
    clientLoading,
    handleSelectAll,
    Clients,
    ClientsLoading,
    filter,
    setFilter,
    importDisclosure,
    setImportFile,
    importFile,
    importFileRef,
    resetFilter,
  };
};

export type TGetClientHook = ReturnType<typeof useGetClientsHook>;
