import React from 'react';
import Contact from './contact';
import { Metadata } from 'next';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default function page({ params }: { params: { id: string } }) {
  return <Contact id={params.id} />;
}
