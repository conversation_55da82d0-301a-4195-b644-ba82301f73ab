'use client';
import EditInvoice from '@/app/(dashboard)/billing/invoices/EditInvoice';
import LinkSlpModal from '@/app/(dashboard)/billing/invoices/LinkSlpModal';
import LinkedInvoiceDetails from '@/app/(dashboard)/invoices/LinkedInvoiceDetails';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import Status from '@/components/elements/status/Status';
import { Tooltip } from '@/components/ui/tooltip';
import { IInvoices } from '@/shared/interface/invoice';
import { getSessionColor } from '@/utils/color-helper';
import { Box, Center } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import 'moment-timezone';
import 'moment/locale/en-ca';
import moment from 'moment/moment';

const raw = localStorage && localStorage?.getItem('UserState');
const data = raw ? JSON.parse(raw) : null;
const org = data?.UserState?.organization;
interface InvoiceDisplay extends Omit<IInvoices, 'slp'> {
  slp?: any;
  slp_notes: any;
  amount_due: number;
  total_duration_minutes: number;
}

const columnHelper = createColumnHelper<InvoiceDisplay>();

// Base columns that are always shown
const baseColumns = [
  columnHelper.accessor('name', {
    cell: (info) => {
      const isVoid = info.row.original.status === 'Void';
      return <Box color={isVoid ? 'gray.100' : 'black'}>{info.getValue()}</Box>;
    },
    header: 'Client Name',
    id: 'client-name',
  }),
  columnHelper.accessor('invoice_number', {
    cell: (info) => {
      const isVoid = info.row.original.status === 'Void';
      return <Box color={isVoid ? 'gray.100' : 'black'}>{info.getValue()}</Box>;
    },
    header: 'Invoice No',
    id: 'invoice-number',
  }),
  columnHelper.accessor('invoice_date', {
    cell: (info) => {
      const isVoid = info.row.original.status === 'Void';
      return (
        <Box color={isVoid ? 'gray.100' : 'black'}>
          {moment(info.getValue()).utc().format('MMMM D, YYYY')}
        </Box>
      );
    },
    header: 'Invoice Date',
    id: 'invoice_date',
    sortingFn: 'datetime',
  }),
  columnHelper.accessor('total_price', {
    cell: (info) => {
      return <Box>{formatMoney(info.getValue())}</Box>;
    },
    header: 'Total Price  ',
    id: 'total_price',
  }),
  columnHelper.accessor('amount_due', {
    cell: (info) => {
      return <Box>{formatMoney(info.getValue())}</Box>;
    },
    header: 'Amount Due',
    id: 'amount_due',
  }),
];

// Session Type column - only included when org.id === 1
const sessionTypeColumn =
  org?.id === 1
    ? [
        columnHelper.display({
          cell: (props) => {
            const sessionType = props.row.original.session_type;
            const isVoid = props.row.original.status === 'Void';
            return (
              <Box minW={'8rem'}>
                <Tooltip
                  content={props.row.original.product}
                  aria-label="A tooltip"
                  interactive
                  contentProps={{ css: { '--tooltip-bg': 'gray' } }}
                >
                  <Center
                    px={1}
                    py={1}
                    fontWeight="medium"
                    rounded="md"
                    color={
                      isVoid ? 'gray.50' : getSessionColor(sessionType).color
                    }
                    border={
                      isVoid
                        ? '1px solid gray'
                        : getSessionColor(sessionType).borderColor
                    }
                    bg={
                      isVoid ? 'gray.100' : getSessionColor(sessionType).bgColor
                    }
                    maxW={'fit-content'}
                  >
                    {sessionType}
                  </Center>
                </Tooltip>
              </Box>
            );
          },
          header: 'Session Type',
          id: 'Session Type',
        }),
      ]
    : [];

// Columns that are only shown when org.id === 1
const slpColumns =
  org?.id === 1
    ? [
        columnHelper.display({
          id: 'slp',
          cell: (props) => {
            const isVoid = props.row.original.status === 'Void';
            return (
              <Box color={isVoid ? 'gray.100' : 'black'}>
                {props.row.original?.slp?.first_name ||
                props.row.original?.slp?.last_name
                  ? `${props.row.original?.slp?.first_name || ''} ${
                      props.row.original?.slp?.last_name || ''
                    }`.trim()
                  : props.row.original?.slp}
              </Box>
            );
          },
          header: 'SLP',
        }),
        columnHelper.display({
          header: 'SLP Note',
          id: 'slp-note',
          cell: (props) => {
            const invoice = props.row.original;
            return (
              <Box>
                {invoice?.slp_notes?.length > 0 ? (
                  <LinkedInvoiceDetails
                    data={{
                      invoices: {
                        ...invoice,
                        slp:
                          invoice?.slp?.first_name || invoice?.slp?.last_name
                            ? `${invoice?.slp?.first_name || ''} ${
                                invoice?.slp?.last_name || ''
                              }`.trim()
                            : invoice?.slp,
                      },
                      id: invoice?.slp_notes?.[0]?.id,
                    }}
                  />
                ) : (
                  <LinkSlpModal
                    invoice={
                      {
                        ...invoice,
                        slp:
                          invoice?.slp?.first_name || invoice?.slp?.last_name
                            ? `${invoice?.slp?.first_name || ''} ${
                                invoice?.slp?.last_name || ''
                              }`.trim()
                            : invoice?.slp,
                      } as any
                    }
                  />
                )}
              </Box>
            );
          },
        }),
      ]
    : [];

// Remaining columns that are always shown
const remainingColumns = [
  columnHelper.accessor(
    org?.id === 1 ? 'total_hours' : 'total_duration_minutes',
    {
      cell: (info) => {
        const isVoid = info.row.original.status === 'Void';
        return (
          <Box color={isVoid ? 'gray.100' : 'black'}>{info.getValue()}</Box>
        );
      },
      header: 'Total Minutes',
      id: 'total-hours',
    }
  ),
  columnHelper.accessor('memo', {
    cell: (info) => {
      const isVoid = info.row.original.status === 'Void';
      return <Box color={isVoid ? 'gray.100' : 'black'}>{info.getValue()}</Box>;
    },
    header: 'Memo',
    id: 'memo',
  }),
  columnHelper.accessor('status', {
    cell: (info) => <Status name={info.getValue() as string} />,
    header: 'Status',
    id: 'status',
  }),
  columnHelper.display({
    id: 'edit-slp',
    cell: (props) => {
      const isVoid = props.row.original.status === 'Void';
      return (
        <Box>
          <EditInvoice
            invoice={props.row.original}
            color={isVoid ? 'gray.100' : 'rgb(79 70 229)'}
          />
        </Box>
      );
    },
    header: 'Action',
  }),
];

// Combine all columns
export const columnDef = [
  ...baseColumns,
  ...sessionTypeColumn,
  ...slpColumns,
  ...remainingColumns,
];
