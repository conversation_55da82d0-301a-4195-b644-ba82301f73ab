import { createColumnHelper } from '@tanstack/react-table';
import { Box } from '@chakra-ui/react';
import { IBookings } from '@/shared/interface/consultation';
import moment from 'moment/moment';
import 'moment-timezone';
import 'moment/locale/en-ca';
import { changeProvinceShortForm } from '@/utils/province-helper';
import Status from '@/components/elements/status/Status';

const columnHelper = createColumnHelper<IBookings>();

export const columnDef = [
  columnHelper.accessor('created_dt', {
    cell: (info) => (
      <Box>
        {moment(info.getValue())
          ? moment(info.getValue()).format('MMM D, YYYY h:mma')
          : 'N/A'}
      </Box>
    ),
    header: 'Booking Date',
    id: 'booking-datetime',
  }),
  columnHelper.accessor('appointment', {
    cell: (info) => (
      <Box>
        {moment(info.getValue())
          ? moment(info.getValue()).format('MMM D, YYYY h:mma')
          : 'N/A'}
      </Box>
    ),
    header: 'Appointment Date',
    id: 'appointment-datetime',
  }),
  columnHelper.accessor('event', {
    cell: (info) => <Box>{changeProvinceShortForm(info.getValue())}</Box>,
    header: 'Event',
    id: 'event',
  }),

  columnHelper.accessor('email', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Email',
    id: 'email',
  }),

  columnHelper.accessor('assigned_to', {
    cell: (info) => <Box>{info.getValue()}</Box>,
    header: 'Assigned to',
    id: 'assigned-to',
  }),
  columnHelper.accessor('calendly_event_type', {
    cell: (info) => (
      <Status
        name={
          info.getValue() === 'invitee.created'
            ? 'Created'
            : info.getValue() === 'invitee.canceled'
              ? 'Cancelled'
              : 'Active'
        }
      />
    ),
    header: 'Status',
    id: 'calendly-status',
  }),
];
