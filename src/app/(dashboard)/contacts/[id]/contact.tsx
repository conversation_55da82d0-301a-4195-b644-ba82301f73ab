'use client';

import { useGetClientStripeLinkMutation } from '@/api/clients/get-stripe-link';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { useGetUserProfile } from '@/hooks/admin/users/useGetProfile';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { useGetSingleClientHook } from '@/hooks/receptionist/contacts/useGetSingleClientHook';
import Packages from '@/reuseables/Packages';
import {
  Avatar,
  Box,
  Button,
  Center,
  createListCollection,
  Flex,
  Heading,
  HStack,
  Icon,
  Portal,
  Select,
  Tabs,
} from '@chakra-ui/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { IconType } from 'react-icons';
import { CiFolderOn } from 'react-icons/ci';
import { FaFileWaveform } from 'react-icons/fa6';
import { FiEdit3, FiPackage, FiUser } from 'react-icons/fi';

import { LuCalendar, LuStickyNote } from 'react-icons/lu';
import { MdOutlineMail } from 'react-icons/md';
import { RiMoneyDollarBoxLine } from 'react-icons/ri';
import Booking from './bookings/booking';
import { ClientDocument } from './document/ClientDocument';
import EmailTab from './emails/EmailTab';
import Invoices from './invoices/invoices';
// import NewPackage from './packages/Packages';
import StagesContainer from '@/components/elements/StagesContainer';
import AllForms from './forms/AllForms';
import Profile from './profile/profile';
import Purchases from './purchases/Purchases';
import Soap from './soap/soap';

// Define type for tab options
interface TabOption {
  value: string;
  label: string;
  icon: IconType;
}

export default function Contact({ id }: { id: string }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const user = useGetUserProfile();
  const timeoutRef = useRef<NodeJS.Timeout>();

  const [show, setShow] = useState(true);
  const [showEditClient, setShowEditClient] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState(id);
  const { UserFromQuery } = useSupabaseSession();
  const { mutateAsync: CopyStripeLink, isLoading: CSLLoading } =
    useGetClientStripeLinkMutation();
  const isSpeakfluent =
    !searchParams.get('organization_id') &&
    UserFromQuery?.organization_id === 1;

  const validTabs = useMemo(
    () => [
      'profile',
      'bookings',
      'invoices',
      'packages',
      'SOAP',
      'forms',
      'email',
      'document',
    ],
    []
  );

  const tabFromUrl = searchParams.get('tab') || 'profile';
  const validatedUrlTab = validTabs.includes(tabFromUrl)
    ? tabFromUrl
    : 'profile';

  const [activeTab, setActiveTab] = useState(validatedUrlTab);

  const hookDependency = useMemo(
    () => ({
      id: activeTab === 'packages' ? selectedClientId : id,
    }),
    [activeTab, selectedClientId, id]
  );

  const getSingleClienthook = useGetSingleClientHook(hookDependency);
  const { Client, refetch: refetchClient } = getSingleClienthook;
  const data: any = Client;
  const primaryEmail = data?.client_emails?.find(
    (item: any) => item?.is_primary_email
  )?.email;

  console.log('logg 9', data);

  const filteredData = useMemo(() => {
    if (!data?.packages) return [];

    const sorted = [...data.packages].sort(
      (a, b) =>
        new Date(b.transaction_dt).getTime() -
        new Date(a.transaction_dt).getTime()
    );

    const statusFilter = show ? 'deleted' : 'voided';
    return sorted.filter((item) =>
      show
        ? item.status?.toLowerCase() !== statusFilter
        : item.status?.toLowerCase() === statusFilter
    );
  }, [data?.packages, show]);

  useEffect(() => {
    setActiveTab(validatedUrlTab);
  }, [validatedUrlTab]);

  const handleValueChange = useCallback(
    (details: { value: string }) => {
      const newTab = details.value;
      const organizationId = searchParams.get('organization_id');

      // Validate tab
      if (!validTabs.includes(newTab)) {
        console.warn(`Invalid tab: ${newTab}`);
        return;
      }

      setActiveTab(newTab);

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // // Update URL immediately
      // const newUrl = `/contacts/${id}?tab=${newTab}`;
      // router.replace(newUrl, { scroll: false });

      // Update URL immediately
      let newUrl = `/contacts/${id}?tab=${newTab}`;
      if (organizationId) {
        newUrl += `&organization_id=${organizationId}`;
      }
      router.replace(newUrl, { scroll: false });
    },
    [validTabs, id, router, searchParams]
  );

  const switchToTab = useCallback(
    (tab: string) => {
      if (validTabs.includes(tab)) {
        setActiveTab(tab);
        const organizationId = searchParams.get('organization_id');

        let newUrl = `/contacts/${id}?tab=${tab}`;
        if (organizationId) {
          newUrl += `&organization_id=${organizationId}`;
        }
        router.replace(newUrl, { scroll: false });
      }
    },
    [validTabs, id, router, searchParams]
  );

  // Define tab options
  const tabOptions = useMemo(
    (): TabOption[] => [
      { value: 'profile', label: 'Profile', icon: FiUser },
      { value: 'bookings', label: 'Bookings', icon: LuCalendar },
      { value: 'invoices', label: 'Invoices', icon: RiMoneyDollarBoxLine },
      { value: 'packages', label: 'Packages', icon: FiPackage },
      ...(user?.permissions?.includes('CAN_VIEW_CLIENT_SOAP_NOTES')
        ? [{ value: 'SOAP', label: 'Notes', icon: LuStickyNote }]
        : []),
      { value: 'email', label: 'Email', icon: MdOutlineMail },
      { value: 'Forms', label: 'Forms', icon: FaFileWaveform },
      { value: 'document', label: 'Documents', icon: CiFolderOn },
    ],
    [user?.permissions]
  );

  // Create ListCollection for Select
  const tabCollection = useMemo(
    () =>
      createListCollection({
        items: tabOptions.map((tab) => ({
          value: tab.value,
          label: tab.label,
          icon: tab.icon,
        })),
      }),
    [tabOptions]
  );

  // Custom SelectValue component
  const SelectValue = () => {
    const selectedTab = tabOptions.find((tab) => tab.value === activeTab);
    return (
      <Select.ValueText placeholder="Select tab">
        <HStack gap={'2'}>
          {selectedTab && <Icon as={selectedTab.icon} boxSize={4} />}
          {selectedTab ? selectedTab.label : 'Select tab'}
        </HStack>
      </Select.ValueText>
    );
  };

  // Loading state
  if (!data) {
    return (
      <Center h={'20rem'}>
        <AnimateLoader />
      </Center>
    );
  }

  const copyLink = async () => {
    const res = await CopyStripeLink({
      email: primaryEmail,
      name: data?.display_name,
      stripe_user_id: UserFromQuery?.organization?.stripe_user_id,
    });
    console.log('res is ', res);
    if (typeof window !== 'undefined') {
      window.open(res?.url, '_blank');
    }
  };

  return (
    <Box>
      {/* Header section */}
      <Flex
        justifyContent={'space-between'}
        w={'full'}
        alignItems={{ md: 'center' }}
        borderBottom={{ lg: '1px solid' }}
        borderColor={{ lg: 'gray.50' }}
        flexDirection={{ base: 'column', md: 'row' }}
        pb={'6'}
      >
        <Flex alignItems={'center'} gap={'3'}>
          <Avatar.Root
            variant="subtle"
            size={'2xl'}
            color={'white'}
            bg={'gray.100'}
          >
            <Avatar.Fallback name={`${data?.first_name} ${data?.last_name}`} />
          </Avatar.Root>
          <Box spaceY={{ base: '1.5', md: '2.5' }}>
            <Heading fontWeight={600} fontSize={{ base: '1.3rem', md: '2rem' }}>
              {`${data?.first_name} ${data?.last_name}`}
            </Heading>
            <StagesContainer stageName={data?.stage} />
          </Box>
        </Flex>

        <Flex alignItems={'center'} gap={'1.5rem'}>
          {UserFromQuery?.organization?.stripe_user_id && (
            <Button onClick={copyLink} loading={CSLLoading} variant={'ghost'}>
              Copy Stripe Link
            </Button>
          )}
          {activeTab === 'profile' && !showEditClient && (
            <Button
              onClick={() => setShowEditClient(true)}
              bg="#e97a5b"
              color="#fff"
              width="8rem"
              mt={{ base: '7', md: '0' }}
              p={0}
              rounded={'md'}
              fontWeight={'600'}
              gap={'3'}
              _hover={{ bg: '#d96847' }}
            >
              <FiEdit3 />
              Edit Profile
            </Button>
          )}
        </Flex>
      </Flex>

      {/* Tabs section */}
      <Tabs.Root value={activeTab} onValueChange={handleValueChange} lazyMount>
        <Box
          position={'sticky'}
          top={'20'}
          zIndex={'10'}
          bg={'white'}
          borderBottom={{ lg: '1px solid' }}
          borderColor={{ lg: 'gray.50' }}
          overflow={'hidden'}
        >
          {/* Mobile Select */}
          <Box display={{ base: 'block', lg: 'none' }}>
            <Select.Root
              collection={tabCollection}
              value={[activeTab]}
              onValueChange={(details) =>
                handleValueChange({ value: details.value[0] })
              }
              size="md"
              width="full"
              positioning={{
                placement: 'bottom',
                sameWidth: true,
              }}
            >
              <Select.HiddenSelect />
              <Select.Control>
                <Select.Trigger borderColor={'gray.50'}>
                  <SelectValue />
                </Select.Trigger>
                <Select.IndicatorGroup>
                  <Select.Indicator />
                </Select.IndicatorGroup>
              </Select.Control>
              <Portal>
                <Select.Positioner>
                  <Select.Content>
                    {tabCollection.items.map((item) => (
                      <Select.Item
                        key={item.value}
                        item={item}
                        justifyContent="flex-start"
                        _selected={{ bg: 'gray.50' }}
                      >
                        <HStack gap={'2'}>
                          <Icon as={item.icon} boxSize={4} />
                          {item.label}
                        </HStack>
                        <Select.ItemIndicator />
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select.Positioner>
              </Portal>
            </Select.Root>
          </Box>

          {/* Desktop Tabs */}
          <Tabs.List
            display={{ base: 'none', lg: 'flex' }}
            border={'none'}
            alignItems={'center'}
            //gap={'6'}
            spaceX={'4'}
            overflowY={'hidden'}
            overflowX={'auto'}
            className="scroll-container"
          >
            <Tabs.Trigger
              value={'profile'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              <Box display="flex" alignItems="center" gap={2}>
                <FiUser size={17} />
                Profile
              </Box>
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'bookings'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              <Box display="flex" alignItems="center" gap={2}>
                <LuCalendar size={17} />
                Bookings
              </Box>
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'invoices'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              <Box display="flex" alignItems="center" gap={2}>
                <RiMoneyDollarBoxLine size={17} />
                Invoices
              </Box>
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'packages'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              <Box display="flex" alignItems="center" gap={2}>
                <FiPackage size={17} />
                {!isSpeakfluent ? 'Purchases' : 'Packages'}
              </Box>
            </Tabs.Trigger>

            {user?.permissions?.includes('CAN_VIEW_CLIENT_SOAP_NOTES') && (
              <Tabs.Trigger
                value={'SOAP'}
                _selected={{ color: 'primary.500' }}
                _before={{ bg: 'primary.500' }}
                _hover={{ color: '#e97a5b' }}
                fontSize={'md'}
                px={'10'}
                py={'7'}
                color="black"
                minW="fit"
                fontWeight={'600'}
              >
                <Box display="flex" alignItems="center" gap={2}>
                  <LuStickyNote size={17} />
                  Notes
                </Box>
              </Tabs.Trigger>
            )}

            <Tabs.Trigger
              value={'forms'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              <Box display="flex" alignItems="center" gap={2}>
                <FaFileWaveform size={17} />
                Forms
              </Box>
            </Tabs.Trigger>
            <Tabs.Trigger
              value={'email'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              <Box display="flex" alignItems="center" gap={2}>
                <MdOutlineMail size={17} />
                Email
              </Box>
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'document'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'10'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              <Box display="flex" alignItems="center" gap={2}>
                <CiFolderOn size={17} strokeWidth={1.4} />
                Documents
              </Box>
            </Tabs.Trigger>
          </Tabs.List>
        </Box>

        {/* Content section */}
        <Box overflowY={'auto'} h={'full'}>
          <Tabs.Content value={'profile'}>
            <Profile
              data={data}
              showEditClient={showEditClient}
              setShowEditClient={setShowEditClient}
              getClientHook={getSingleClienthook}
              switchToTab={switchToTab}
            />
          </Tabs.Content>

          <Tabs.Content value={'bookings'}>
            <Booking data={data} />
          </Tabs.Content>

          <Tabs.Content value={'invoices'}>
            <Invoices data={data} refetchClient={refetchClient} />
          </Tabs.Content>

          <Tabs.Content value="packages">
            {!isSpeakfluent ? (
              <Purchases id={id} />
            ) : (
              <Packages
                data={filteredData || []}
                client={data}
                section={'client'}
                setShow={setShow}
                show={show}
                setSelectedClientId={setSelectedClientId}
              />
            )}
          </Tabs.Content>

          {user?.permissions?.includes('CAN_VIEW_CLIENT_SOAP_NOTES') && (
            <Tabs.Content value="SOAP">
              <Soap data={data} refetch={refetchClient} />
            </Tabs.Content>
          )}

          <Tabs.Content value="forms">
            <AllForms data={data} />
          </Tabs.Content>

          <Tabs.Content value="email">
            <EmailTab id={id} />
          </Tabs.Content>

          <Tabs.Content value="document">
            <ClientDocument client={data} />
          </Tabs.Content>
        </Box>
      </Tabs.Root>
    </Box>
  );
}
