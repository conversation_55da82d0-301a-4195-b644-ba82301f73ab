import ConsultationAction from '@/app/(dashboard)/consultations/view-all-consultations/ConsultationAction';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import { FaWpforms } from 'react-icons/fa6';
import { GoDotFill } from 'react-icons/go';

const ConsultationNotesCreated = ({ activities }: any) => {
  // Get the answer details from the activity data

  console.log('activities---344', activities);

  //   const answerDetails = activities?.details?.answer_details || [];

  //   // Calculate the number of questions
  //   const numOfQuestions = answerDetails.length;

  //   // Calculate the number of answered questions
  //   const numOfAnswered = answerDetails.filter(
  //     (item: any) =>
  //       item.ans &&
  //       (typeof item.ans === 'string'
  //         ? item.ans.trim() !== ''
  //         : Array.isArray(item.ans)
  //           ? item.ans.length > 0
  //           : item.ans !== null)
  //   ).length;

  return (
    <>
      <Box w={'100%'} h={'fit'}>
        <Flex w={'full'} direction={'row'}>
          <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
            <GoDotFill color="#000" />
            <Box minH={'1rem'} bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
          </Stack>
          <Flex
            w={'full'}
            maxW={'960px'}
            flexWrap={'wrap'}
            gap={'2'}
            direction={'column'}
            mb={'4'}
          >
            <Box>
              <Flex direction={'column'} gap={'1.5'}>
                <Flex justifyContent={'space-between'}>
                  <Text
                    textTransform={'capitalize'}
                    fontSize={'14px'}
                    fontWeight={'500'}
                  >
                    Consultation Notes
                  </Text>
                </Flex>
              </Flex>
            </Box>
            <Box
              rounded={'4.8px'}
              border={'.0625rem solid #dee1e0'}
              py={'0.8rem'}
              px={'8px'}
              display={'flex'}
              w={'100%'}
              //alignItems={'center'}
              //maxHeight={'3.5rem'}
              minHeight={'3.5rem'}
              //height={'3x.5rem'}
              gap={'1rem'}
            >
              <Box
                rounded={'4.8px'}
                fontSize={'16px'}
                display={'flex'}
                justifyContent={'center'}
                alignItems={'center'}
                minW={'36px'}
                minH={'36px'}
                maxH={'36px'}
                w={'36px'}
                maxW={'36px'}
                cursor={'pointer'}
                bg={'#faf5ff'}
                color={'#9333ea'}
              >
                <FaWpforms />
              </Box>

              <Flex flexDirection={'column'} gap={1} alignItems={'start'}>
                <Flex
                  alignItems={'start'}
                  fontWeight={'600'}
                  fontSize={'14px'}
                  gap={1}
                  width={'100%'}
                >
                  <Text>Consulted By </Text>
                  <Text> - </Text>
                  <Text>{activities?.details?.consulted_by}</Text>
                </Flex>

                <ConsultationAction row={activities} type={'contact'} />
              </Flex>
            </Box>
          </Flex>
        </Flex>
      </Box>
    </>
  );
};

export default ConsultationNotesCreated;
