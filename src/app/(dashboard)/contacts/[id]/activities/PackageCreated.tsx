'use client';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
// import { IoIosCheckmarkCircleOutline } from 'react-icons/io';
import { FiBox } from 'react-icons/fi';
import { GoDotFill } from 'react-icons/go';

export default function PackageCreated({
  total,
  product,
  handleSwitchTab,
  package_size,
}: any) {
  return (
    <Box w={'100%'} h={'fit'}>
      <Flex w={'full'} direction={'row'}>
        <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
          <GoDotFill color="#000" />
          <Box minH={'1rem'} bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
        </Stack>
        <Flex
          w={'full'}
          maxW={'960px'}
          flexWrap={'wrap'}
          gap={'2'}
          direction={'column'}
          mb={'4'}
        >
          <Box>
            <Flex direction={'column'} gap={'1.5'}>
              {/* <Text fontSize={'12px'} color="#7C7C7C">
                {moment(date).format('MMMM D, YYYY')}
              </Text> */}
              <Flex justifyContent={'space-between'}>
                <Text
                  textTransform={'capitalize'}
                  fontSize={'14px'}
                  fontWeight={'500'}
                >
                  Package Purchased
                </Text>
              </Flex>
            </Flex>
          </Box>
          <Box
            rounded={'4.8px'}
            border={'.0625rem solid #dee1e0'}
            py={'0.8rem'}
            px={'8px'}
            display={'flex'}
            w={'100%'}
            //maxHeight={'3.5rem'}
            //alignItems={'center'}
            minHeight={'3.5rem'}
            //height={'3.5rem'}
            gap={'1rem'}
            cursor={'pointer'}
            onClick={() => handleSwitchTab('packages')}
            _hover={{
              boxShadow: 'lg',
              transition: 'all 0.2s ease-in-out',
            }}
          >
            <Box
              rounded={'4.8px'}
              fontSize={'16px'}
              display={'flex'}
              justifyContent={'center'}
              alignItems={'center'}
              minW={'36px'}
              w={'36px'}
              maxW={'36px'}
              minH={'36px'}
              maxH={'36px'}
              cursor={'pointer'}
              bg={'#f0fdf4'}
              color={'#16a34a'}
            >
              <FiBox />
            </Box>

            <Box
              textAlign={'left'}
              display={'flex'}
              gap={'1.5'}
              flexDirection={'column'}
            >
              <Text
                fontSize={'14px'}
                fontWeight={'600'}
                maxWidth={'full'}
                width={'full'}
              >
                {product}
              </Text>
              <Box
                textAlign={'left'}
                display={'flex'}
                flexDirection={'row'}
                gap={'5'}
                w={'full'}
                color={'#374151'}
                fontWeight={'500'}
                fontSize={'sm'}
              >
                <Text maxWidth={'full'} whiteSpace={'nowrap'}>
                  Total: ${total}
                </Text>
                <Text maxWidth={'full'} whiteSpace={'nowrap'}>
                  Size: {package_size}
                </Text>
              </Box>
            </Box>
          </Box>
        </Flex>
      </Flex>
    </Box>
  );
}

//   <Flex alignItems={'center'} gap={'.75rem'}>
//     <Flex alignItems={'center'} gap={'.5rem'}>
//       <Text fontSize={'.8rem'} fontWeight={'500'}>
//         Total:
//       </Text>
//       <Text fontSize={'.75rem'} color="#7C7C7C">
//         {total}
//       </Text>
//     </Flex>
//     <Flex gap={'.5rem'} alignItems={'center'}>
//       <Text fontSize={'.8rem'} fontWeight={'500'}>
//         Size:
//       </Text>
//       <Text fontSize={'.75rem'} color="#7C7C7C">
//         {size}
//       </Text>
//     </Flex>
//   </Flex>
//   <Center
//     ml={'2rem'}
//     py={'.1rem'}
//     px={'.5rem'}
//     rounded={'.2rem'}
//     bg={'#ff981b'}
//     fontSize={'.8rem'}
//   >
//     Invoice created
//   </Center>
//   <Flex gap={'.5rem'} alignItems={'center'} ml={'auto'}>
//     <Text fontSize={'.8rem'} fontWeight={'500'}>
//       {moment(date).format('MMMM D, YYYY')}
//     </Text>
//     <Text fontSize={'.75rem'} color="#7C7C7C">
//       {moment(date).fromNow()}
//     </Text>
//   </Flex>
