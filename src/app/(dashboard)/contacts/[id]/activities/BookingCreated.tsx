'use client';
import Status from '@/components/elements/status/Status';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { FiCalendar } from 'react-icons/fi';
import { GoDotFill } from 'react-icons/go';

export default function BookingCreated({
  data,
  handleSwitchTab,
}: {
  data: any;
  handleSwitchTab: (tab: string) => void;
}) {
  const searchParams = useSearchParams();

  const bookingData = data?.details;
  const organizationIdFromParams = searchParams.get('organization_id');

  console.log('bookingData', bookingData);

  let url = `/slp/${bookingData?.slp_id}/create-invoice/${bookingData?.booking_id}`;

  // Add organizationId as query param if it exists
  if (organizationIdFromParams) {
    url += `?organization_id=${organizationIdFromParams}`;
  }
  return (
    <Box w={'100%'} h={'fit'}>
      <Flex w={'full'} direction={'row'}>
        <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
          <GoDotFill color="#000" />
          <Box minH={'1rem'} bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
        </Stack>
        <Flex
          w={'full'}
          maxW={'960px'}
          flexWrap={'wrap'}
          gap={'2'}
          direction={'column'}
          mb={'4'}
        >
          <Box>
            <Flex direction={'column'} gap={'1.5'}>
              {/* <Text fontSize={'12px'} color="#7C7C7C">
                {moment(data?.details?.appointment_date).format('MMMM D, YYYY')}
              </Text> */}
              <Flex justifyContent={'space-between'}>
                <Text
                  textTransform={'capitalize'}
                  fontSize={'14px'}
                  fontWeight={'500'}
                >
                  Booking Created
                </Text>

                {/* <Link href={url}>
                  <Status name={data?.details?.status} />
                </Link> */}
              </Flex>
            </Flex>
          </Box>
          <Box
            rounded={'4.8px'}
            border={'.0625rem solid #dee1e0'}
            py={'0.8rem'}
            px={'8px'}
            display={'flex'}
            w={'100%'}
            //alignItems={'center'}
            //maxHeight={'3.5rem'}
            minHeight={'3.5rem'}
            height={'3x.5rem'}
            gap={'1rem'}
            cursor={'pointer'}
            onClick={() => handleSwitchTab('bookings')}
            _hover={{
              boxShadow: 'lg',
              transition: 'all 0.2s ease-in-out',
            }}
          >
            <Box
              rounded={'4.8px'}
              fontSize={'16px'}
              display={'flex'}
              justifyContent={'center'}
              alignItems={'center'}
              minW={'36px'}
              w={'36px'}
              maxW={'36px'}
              maxH={'36px'}
              minH={'36px'}
              cursor={'pointer'}
              bg={'#faf5ff'}
              color={'#9333ea'}
            >
              <FiCalendar />
            </Box>

            <Box
              textAlign={'left'}
              w={'full'}
              display={'flex'}
              flexDirection={'column'}
            >
              <Flex
                justifyContent={'space-between'}
                alignItems={'center'}
                gap={'1.5'}
              >
                <Text
                  //color={'#374151'}
                  fontWeight={'600'}
                  fontSize={'14px'}
                  maxWidth={'full'}
                  width={'full'}
                >
                  Assigned to: {data?.details?.assigned_to}
                </Text>
                <Link href={url}>
                  <Status
                    name={data?.details?.status}
                    isBorder={false}
                    isDot={false}
                  />
                </Link>
              </Flex>
              <Text
                color={'#374151'}
                fontWeight={'500'}
                fontSize={'sm'}
                maxWidth={'full'}
                width={'full'}
                mt={'1'}
              >
                Event: {data?.details?.event}
              </Text>
            </Box>
          </Box>
        </Flex>
      </Flex>
    </Box>
  );
}
