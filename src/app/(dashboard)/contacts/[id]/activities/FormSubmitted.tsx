import { <PERSON>, <PERSON>lex, <PERSON><PERSON><PERSON><PERSON>, Stack, Text } from '@chakra-ui/react';
import { FaFileWaveform } from 'react-icons/fa6';
import { GoDotFill } from 'react-icons/go';
const FormSubmitted = ({ activities, handleSwitchTab }: any) => {
  // Get the answer details from the activity data

  const answerDetails = activities?.details?.answer_details || [];

  // Calculate the number of questions
  const numOfQuestions = answerDetails.length;

  // Calculate the number of answered questions
  const numOfAnswered = answerDetails.filter(
    (item: any) =>
      item.ans &&
      (typeof item.ans === 'string'
        ? item.ans.trim() !== ''
        : Array.isArray(item.ans)
          ? item.ans.length > 0
          : item.ans !== null)
  ).length;

  return (
    <>
      <Box w={'100%'} h={'fit'}>
        <Flex w={'full'} direction={'row'}>
          <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
            <GoDotFill color="#000" />
            <Box minH={'1rem'} bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
          </Stack>
          <Flex
            w={'full'}
            maxW={'960px'}
            flexWrap={'wrap'}
            gap={'2'}
            direction={'column'}
            mb={'4'}
          >
            <Box>
              <Flex direction={'column'} gap={'1.5'}>
                <Flex justifyContent={'space-between'}>
                  <Text
                    textTransform={'capitalize'}
                    fontSize={'14px'}
                    fontWeight={'500'}
                  >
                    Form Submitted
                  </Text>
                </Flex>
              </Flex>
            </Box>
            <Box
              rounded={'4.8px'}
              border={'.0625rem solid #dee1e0'}
              py={'0.8rem'}
              px={'8px'}
              display={'flex'}
              onClick={() => handleSwitchTab('forms')}
              cursor={'pointer'}
              w={'100%'}
              //maxHeight={'3.5rem'}
              //alignItems={'center'}
              minHeight={'3.5rem'}
              //height={'3x.5rem'}
              gap={'1rem'}
              //cursor={'pointer'}
            >
              <Box
                rounded={'4.8px'}
                fontSize={'16px'}
                display={'flex'}
                justifyContent={'center'}
                alignItems={'center'}
                minW={'36px'}
                cursor={'pointer'}
                minH={'36px'}
                maxH={'36px'}
                w={'36px'}
                maxW={'36px'}
                bg={'#faf5ff'}
                color={'#9333ea'}
              >
                <FaFileWaveform />
              </Box>

              <Flex
                alignItems={'start'}
                flexDirection={'column'}
                width={'100%'}
                gap={'1'}
              >
                <Text
                  maxWidth={'full'}
                  fontSize={'14px'}
                  fontWeight={'600'}
                  width={'full'}
                >
                  {activities?.details?.form_name || 'Form Name'}
                </Text>
                <HStack mt={'1'} w={'full'} gap={'5'}>
                  <Text
                    //maxWidth={'full'}
                    color={'#374151'}
                    fontWeight={'500'}
                    fontSize={'sm'}
                    //width={'full'}
                  >
                    {numOfQuestions} question{numOfQuestions !== 1 ? 's' : ''}
                  </Text>
                  <Text
                    color={'#15803d'}
                    fontWeight={'500'}
                    fontSize={'sm'}
                    // maxWidth={'full'}
                    // width={'full'}
                  >
                    <span>
                      {numOfAnswered}/{numOfQuestions}
                    </span>{' '}
                    Completed
                  </Text>
                </HStack>
              </Flex>
            </Box>
          </Flex>
        </Flex>
      </Box>
    </>
  );
};

export default FormSubmitted;
