import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { Box, Flex, Stack, Text } from '@chakra-ui/react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { FiCreditCard } from 'react-icons/fi';
import { GoDotFill } from 'react-icons/go';

const TransactionCreated = ({ activities }: any) => {
  // Get the answer details from the activity data

  console.log('activities', activities);

  const searchParams = useSearchParams();
  const organizationId = searchParams.get('organization_id');

  return (
    <>
      <Box w={'100%'} h={'fit'}>
        <Flex w={'full'} direction={'row'}>
          <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
            <GoDotFill color="#000" />
            <Box minH={'1rem'} bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
          </Stack>
          <Flex
            w={'full'}
            maxW={'960px'}
            flexWrap={'wrap'}
            gap={'2'}
            direction={'column'}
            mb={'4'}
          >
            <Box>
              <Flex direction={'column'} gap={'1.5'}>
                <Flex justifyContent={'space-between'}>
                  <Text
                    textTransform={'capitalize'}
                    fontSize={'14px'}
                    fontWeight={'500'}
                  >
                    Transaction Created via {activities?.details?.created_by}
                  </Text>

                  {activities?.details?.invoice_id && (
                    <Link
                      href={
                        organizationId
                          ? `/invoices/${activities?.details?.invoice_id}?organization_id=${organizationId}&user_id=${activities?.clients?.active_slp}`
                          : `/invoices/${activities?.details?.invoice_id}`
                      }
                    >
                      <Text
                        fontSize="sm"
                        //color={'orange.500'}
                        fontWeight={500}
                        textDecoration={'underline'}
                        _hover={{
                          color: 'orange.500',
                        }}
                        transition="color 0.2s ease"
                      >
                        Linked
                      </Text>
                    </Link>
                  )}
                </Flex>
              </Flex>
            </Box>
            <Box
              rounded={'4.8px'}
              border={'.0625rem solid #dee1e0'}
              py={'0.8rem'}
              px={'8px'}
              display={'flex'}
              w={'100%'}
              //maxHeight={'3.5rem'}
              //alignItems={'center'}
              minHeight={'3.5rem'}
              height={'3x.5rem'}
              gap={'1rem'}
              //cursor={'pointer'}
            >
              <Box
                rounded={'4.8px'}
                fontSize={'16px'}
                display={'flex'}
                justifyContent={'center'}
                alignItems={'center'}
                minW={'36px'}
                minH={'36px'}
                maxH={'36px'}
                w={'36px'}
                maxW={'36px'}
                cursor={'pointer'}
                bg={'#faf5ff'}
                color={'#9333ea'}
              >
                <FiCreditCard />
              </Box>

              <Flex
                justifyContent={'space-between'}
                alignItems={'start'}
                w={'100%'}
              >
                <Flex flexDirection={'column'} gap={1.5} alignItems={'start'}>
                  <Flex
                    alignItems={'start'}
                    gap={1}
                    width={'100%'}
                    //color={'#374151'}
                    fontWeight={'600'}
                    fontSize={'14px'}
                  >
                    <Text>Amount </Text>
                    <Text> - </Text>
                    <Text>{formatMoney(activities?.details?.amount)}</Text>
                  </Flex>
                  <Flex
                    alignItems={'start'}
                    gap={1}
                    width={'100%'}
                    color={'#374151'}
                    fontWeight={'500'}
                    fontSize={'sm'}
                  >
                    <Text>Transaction Type </Text>
                    <Text> - </Text>
                    <Text textTransform={'capitalize'}>
                      {activities?.details?.transaction_type}
                    </Text>
                  </Flex>
                </Flex>
                <Flex flexDirection={'column'} gap={1} alignItems={'start'}>
                  {Array.isArray(activities?.details?.package_items) &&
                    activities?.details?.package_items?.length > 0 &&
                    activities?.details?.package_items?.map(
                      (item: any, index: number) => (
                        <Flex
                          alignItems={'start'}
                          gap={1}
                          width={'100%'}
                          key={index}
                          color={'#374151'}
                          fontWeight={'500'}
                          fontSize={'sm'}
                        >
                          <Text>{item?.service_name} </Text>
                          <Text> - </Text>
                          <Text>${item?.service_price}</Text>
                        </Flex>
                      )
                    )}
                </Flex>
              </Flex>
            </Box>
          </Flex>
        </Flex>
      </Box>
    </>
  );
};

export default TransactionCreated;
