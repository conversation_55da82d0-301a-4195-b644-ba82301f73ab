import { useEditNoteHook } from '@/hooks/admin/notes/useEditNoteHook';
import { ActivityType } from '@/utils/enums';
import { Box, Center, Flex, Stack, Text } from '@chakra-ui/react';
import { useState } from 'react';
import { GoDotFill } from 'react-icons/go';
import EditNote from '../notes/EditNote';
import truncate from 'html-truncate';
// import { limitText } from '@/utils/limitText';

export default function NoteCreated({ data, clientId }: any) {
  const [triggerEditNote, setTriggerEditNote] = useState(false);
  const details = data?.details ?? null;
  console.log('details', details);
  // if ([NoteStatusEnum.DELETED].includes(details?.status)) {
  //   return null;
  // }
  // if ([NoteStatusEnum.HIDDEN].includes(details?.status)) {
  //   return null;
  // }

  // const { updateStatus, authorData } = useEditNoteHook({
  const { authorData } = useEditNoteHook({
    row: details,
    activityId: data?.id,
    clientId,
  });

  const noteActivityMap = {
    [ActivityType.NOTE_CREATED]: 'Consultation Note Created: ',
    [ActivityType.NOTE_MODIFIED]: 'Last Edited: ',
  };
  const onNoteClick = () => {
    setTriggerEditNote(true);
  };
  return (
    <Box w={'100%'} h={'fit'}>
      <Flex w={'full'} direction={'row'}>
        <Stack maxW={'3rem'} minW={'3rem'} gap={0} alignItems={'center'}>
          <GoDotFill color="#000" />
          <Box minH={'1rem'} bg={'#dee1e0'} h={'100%'} w={'.5px'}></Box>
        </Stack>
        <Flex
          w={'full'}
          maxW={'960px'}
          flexWrap={'wrap'}
          gap={'2'}
          direction={'column'}
          mb={'4'}
        >
          <Box>
            <Flex direction={'column'} gap={'1.5'}>
              {/* <Text fontSize={'12px'} color="#7C7C7C">
                {moment(details?.note_date).format('MMMM D, YYYY')}
              </Text> */}
              <Flex justifyContent={'space-between'}>
                <Center
                  textTransform={'capitalize'}
                  fontSize={'14px'}
                  fontWeight={'500'}
                  gap={'1'}
                >
                  {/* <GoNote size={'16px'} /> */}
                  {noteActivityMap[data?.activity_type as ActivityType]}
                  {authorData ? (
                    <Text>
                      {authorData?.first_name} {authorData?.last_name}
                    </Text>
                  ) : null}
                  {data?.details?.consulted_by ? (
                    <Text>{data?.details?.consulted_by}</Text>
                  ) : null}
                </Center>
              </Flex>
            </Flex>
          </Box>
          <Box
            rounded={'4.8px'}
            border={'.0625rem solid #dee1e0'}
            py={'0.8rem'}
            px={'8px'}
            display={'flex'}
            //alignItems={'center'}
            w={'100%'}
            //maxHeight={'3.5rem'}
            minHeight={'3.5rem'}
            //height={'3x.5rem'}
            gap={'1rem'}
            cursor={'pointer'}
            _hover={{
              boxShadow: 'lg',
              transition: 'all 0.2s ease-in-out',
            }}
          >
            <EditNote
              isMenu={false}
              row={details}
              activity={data}
              clientId={clientId}
              triggerEditNote={triggerEditNote}
              setTriggerEditNote={setTriggerEditNote}
            />
            <Box
              textAlign={'left'}
              display={'flex'}
              flexDirection={'column'}
              fontSize={'12px'}
              flex={'1'}
              truncate
              whiteSpace={'nowrap'}
              textOverflow={'ellipsis'}
            >
              {/* <Text
                textTransform={'capitalize'}
                // fontSize={'.9rem'}
                // fontWeight={'bold'}
              >
                {details?.title ?? 'Note Title'}
              </Text> */}

              <Text
                maxWidth={'full'}
                width={'full'}
                truncate
                whiteSpace={'nowrap'}
                overflow={'hidden'}
                textOverflow={'ellipsis'}
                onClick={onNoteClick}
                fontSize={'14px'}
                dangerouslySetInnerHTML={{
                  __html: truncate(
                    details?.notes?.replace(/\s+/g, ' ').replace(/\n/g, ' '),
                    60
                  ),
                }}
              ></Text>
              {/* <Text onClick={onNoteClick} cursor={'pointer'} fontSize={'10px'}>
                {truncate(details?.notes, 30, { ellipsis: '...' })}
              </Text> */}
              {/* <Text
              whiteSpace="preserve"
              dangerouslySetInnerHTML={{ __html: details?.notes }}
            ></Text> */}
            </Box>

            {/* <Box
            rounded={'4.8px'}
            fontSize={'16px'}
            display={'flex'}
            justifyContent={'center'}
            alignItems={'center'}
            w={'36px'}
            bg={'#F0F3F2'}
            cursor={'pointer'}
            position={'relative'}
          >
            <MenuRoot>
              <MenuTrigger
                display={'flex'}
                justifyContent={'center'}
                alignItems={'center'}
                w={'100%'}
                h={'100%'}
                // color={'gray.500'}
              >
                <BsThreeDotsVertical />
              </MenuTrigger>
              <MenuContent
                cursor={'pointer'}
                position={'absolute'}
                right={'2px'}
              >
                {[NoteStatusEnum.HIDDEN].includes(details?.status) ? (
                  <MenuItem
                    onClick={() =>
                      updateStatus(NoteStatusEnum.ACTIVE, details?.id, data)
                    }
                    value="show"
                  >
                    Show Note
                  </MenuItem>
                ) : null}

                {[NoteStatusEnum.ACTIVE].includes(details?.status) ? (
                  <MenuItem
                    onClick={() =>
                      updateStatus(NoteStatusEnum.HIDDEN, details?.id, data)
                    }
                    value="hide"
                  >
                    Hide Note
                  </MenuItem>
                ) : null}
              </MenuContent>
            </MenuRoot>
          </Box> */}
          </Box>
        </Flex>
      </Flex>
    </Box>
  );
}
