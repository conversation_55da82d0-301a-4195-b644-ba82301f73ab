'use client';
import React from 'react';
import { Box, Flex, Text, HStack, Separator } from '@chakra-ui/react';
import moment from 'moment';
import { FiCalendar, FiClock, FiUser } from 'react-icons/fi';

const ClientCreated = ({ activities }: any) => {
  const isCreatedViaManual =
    activities?.details?.created_by.toLowerCase() === 'manual';

  const createdBy = isCreatedViaManual
    ? 'Manually'
    : `via ${activities?.details?.created_by}`;

  const {
    first_name,
    last_name,
    utm_medium,
    utm_source,
    utm_campaign,
    utm_content,
  } = activities?.clients || {};

  const formatValue = (val: string) =>
    val?.replace(/[^a-zA-Z0-9\s]/g, ' ') || '';

  // console.log('activities', activities);

  return (
    <Box
      alignItems="center"
      maxW="60rem"
      p="1rem"
      //backgroundColor="gray.50/30"
      borderRadius="8px"
      flexWrap={'wrap'}
      rounded={'4.8px'}
      border={'.0625rem solid #dee1e0'}
      py={'0.8rem'}
      px={'1rem'}
      w={'100%'}
    >
      {/* Header */}
      <Flex justifyContent={'space-between'} align="center" mb={3}>
        <Box display={'flex'} justifyContent={'center'} gap={'.8rem'}>
          <Box
            rounded={'full'}
            fontSize={'16px'}
            display={'flex'}
            justifyContent={'center'}
            alignItems={'center'}
            minW={'36px'}
            w={'36px'}
            minH={'36px'}
            maxH={'36px'}
            maxW={'36px'}
            cursor={'pointer'}
            bg={'#eff6ff'}
            color={'#2563eb'}
          >
            <FiUser />
          </Box>
          <Box>
            <Text
              textTransform={'capitalize'}
              fontSize={'md'}
              fontWeight={'600'}
            >
              {first_name} {last_name}
            </Text>
            <Text color={'#374151'} fontWeight={'500'} fontSize={'sm'}>
              Created {createdBy}
            </Text>
          </Box>
        </Box>

        <Box>
          <HStack
            gap={4}
            mb={3}
            color={'#374151'}
            fontWeight={'500'}
            fontSize={'sm'}
          >
            <HStack>
              <FiCalendar size={15} />
              <Text>
                {moment(activities?.created_at).format('MMMM D, YYYY')}
              </Text>
            </HStack>
            <HStack>
              <FiClock size={15} />
              <Text>{moment(activities?.created_at).format('h:mm A')}</Text>
            </HStack>
          </HStack>
        </Box>
      </Flex>

      {utm_medium || utm_source || utm_campaign || utm_content ? (
        <>
          <Separator borderColor="#c0c0c2" />
          {/* UTM Tracking Details */}
          <Text fontWeight={'600'} fontSize={'sm'} my={3}>
            UTM Tracking Details
          </Text>
        </>
      ) : null}

      <Flex w={'full'} gap={3} flexWrap="wrap" mb={3}>
        {utm_medium ? (
          <Box
            h={'4rem'}
            py={2}
            px={4}
            rounded={'4.8px'}
            border={'.0625rem solid #dee1e0'}
            flexGrow={1}
          >
            <Box>
              <Box gap={'.5rem'} display={'flex'}>
                <Box
                  rounded={'full'}
                  fontSize={'16px'}
                  display={'flex'}
                  justifyContent={'center'}
                  alignItems={'center'}
                  minW={'10px'}
                  w={'10px'}
                  minH={'10px'}
                  maxH={'10px'}
                  maxW={'10px'}
                  bg={'pink'}
                >
                  {' '}
                </Box>
                <Text color={'#374151'} fontWeight={'600'} fontSize={'xs'}>
                  MEDIUM
                </Text>
              </Box>
              <Box mt={3} fontWeight={'bold'} fontSize={'sm'}>
                {' '}
                {formatValue(utm_medium)}
              </Box>
            </Box>
          </Box>
        ) : null}
        {utm_content ? (
          <Box
            flexGrow={1}
            py={2}
            px={4}
            rounded={'4.8px'}
            border={'.0625rem solid #dee1e0'}
          >
            <Box>
              <Box alignItems={'center'} gap={'.5rem'} display={'flex'}>
                <Box
                  rounded={'full'}
                  fontSize={'16px'}
                  display={'flex'}
                  justifyContent={'center'}
                  alignItems={'center'}
                  minW={'10px'}
                  w={'10px'}
                  minH={'10px'}
                  maxH={'10px'}
                  maxW={'10px'}
                  bg={'#067832'}
                >
                  {' '}
                </Box>
                <Text color={'#374151'} fontWeight={'600'} fontSize={'xs'}>
                  Content
                </Text>
              </Box>
              <Box mt={3} fontWeight={'bold'} fontSize={'sm'}>
                {' '}
                {formatValue(utm_content)}
              </Box>
            </Box>
          </Box>
        ) : null}
        {utm_source ? (
          <Box
            flexGrow={1}
            py={2}
            px={4}
            rounded={'4.8px'}
            border={'.0625rem solid #dee1e0'}
          >
            <Box>
              <Box alignItems={'center'} gap={'.5rem'} display={'flex'}>
                <Box
                  rounded={'full'}
                  fontSize={'16px'}
                  display={'flex'}
                  justifyContent={'center'}
                  alignItems={'center'}
                  minW={'10px'}
                  w={'10px'}
                  minH={'10px'}
                  maxH={'10px'}
                  maxW={'10px'}
                  bg={'#c20cb0'}
                >
                  {' '}
                </Box>
                <Text color={'#374151'} fontWeight={'600'} fontSize={'xs'}>
                  SOURCE
                </Text>
              </Box>
              <Box
                textTransform={'capitalize'}
                mt={3}
                fontWeight={'bold'}
                fontSize={'sm'}
              >
                {' '}
                {formatValue(utm_source)}
              </Box>
            </Box>
          </Box>
        ) : null}
        {utm_campaign ? (
          <Box
            flexGrow={1}
            py={2}
            px={4}
            rounded={'4.8px'}
            border={'.0625rem solid #dee1e0'}
          >
            <Box>
              <Box gap={'.5rem'} display={'flex'}>
                <Box
                  rounded={'full'}
                  fontSize={'16px'}
                  display={'flex'}
                  justifyContent={'center'}
                  alignItems={'center'}
                  minW={'10px'}
                  w={'10px'}
                  minH={'10px'}
                  maxH={'10px'}
                  maxW={'10px'}
                  bg={'#1056eb'}
                >
                  {' '}
                </Box>
                <Text color={'#374151'} fontWeight={'600'} fontSize={'xs'}>
                  CAMPAIGN
                </Text>
              </Box>
              <Box mt={3} fontWeight={'bold'} fontSize={'sm'}>
                {' '}
                {formatValue(utm_campaign)}
              </Box>
            </Box>
          </Box>
        ) : null}
      </Flex>
    </Box>
  );
};

export default ClientCreated;

// 'use client';
// import React from 'react';
// import moment from 'moment';
// import { Flex, Text } from '@chakra-ui/react';

// const ClientCreated = ({ activities }: any) => {
//   const isCreatedViaManual =
//     activities?.details?.created_by.toLowerCase() === 'manual';

//   console.log('activities', activities);
//   return (
//     <Flex
//       alignItems="center"
//       maxW="60rem"
//       p="1rem"
//       backgroundColor="gray.50/30"
//       borderRadius="8px"
//       flexWrap={'wrap'}
//     >
//       <Flex flexDirection="column" gap="0.5rem">
//         <Flex alignItems="center" gap="0.5rem">
//           <Text fontSize="1rem" fontWeight="600" color="#1C3C72">
//             {activities?.clients?.first_name} {activities?.clients?.last_name}
//           </Text>
//           <Text fontSize="1rem" color="#7C7C7C">
//             Created {!isCreatedViaManual && 'via'}{' '}
//             {isCreatedViaManual
//               ? 'manually'
//               : activities?.details?.created_by.toLowerCase()}
//           </Text>
//         </Flex>
//         <Flex textTransform={'capitalize'} gap={'.5rem'}>
//           {activities?.clients?.utm_term ? (
//             <Text fontSize="0.85rem" color="#7C7C7C">
//               UTM Term:{' '}
//               <b>
//                 {activities?.clients?.utm_term?.replace(/[^a-zA-Z0-9]/g, ' ')}
//               </b>
//             </Text>
//           ) : null}
//           {activities?.clients?.utm_medium ? (
//             <Text fontSize="0.85rem" color="#7C7C7C">
//               UTM Medium:{' '}
//               <b>
//                 {activities?.clients?.utm_medium?.replace(/[^a-zA-Z0-9]/g, ' ')}
//               </b>
//             </Text>
//           ) : null}
//           {activities?.clients?.utm_source ? (
//             <Text fontSize="0.85rem" color="#7C7C7C">
//               UTM Source:{' '}
//               <b>
//                 {activities?.clients?.utm_source?.replace(/[^a-zA-Z0-9]/g, ' ')}
//               </b>
//             </Text>
//           ) : null}
//           {activities?.clients?.utm_content ? (
//             <Text fontSize="0.85rem" color="#7C7C7C">
//               UTM Content:
//               <b>
//                 {' '}
//                 {activities?.clients?.utm_content?.replace(
//                   /[^a-zA-Z0-9]/g,
//                   ' '
//                 )}
//               </b>
//             </Text>
//           ) : null}
//           {activities?.clients?.utm_campaign ? (
//             <Text fontSize="0.85rem" color="#7C7C7C">
//               UTM Campaign:{' '}
//               <b>
//                 {activities?.clients?.utm_campaign?.replace(
//                   /[^a-zA-Z0-9]/g,
//                   ' '
//                 )}
//               </b>
//             </Text>
//           ) : null}
//         </Flex>
//         <Text fontSize="0.85rem" color="#7C7C7C">
//           {moment(activities?.created_at).format('MMMM D, YYYY, h:mm A')}
//         </Text>
//       </Flex>
//     </Flex>
//   );
// };

// export default ClientCreated;
