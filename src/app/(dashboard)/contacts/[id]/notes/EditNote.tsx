import { CustomModal } from '@/components/elements/modal/custom-modal';
// import TextEditor from '@/components/Input/CustomEditor';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { Box, Grid, GridItem, Stack } from '@chakra-ui/react';
import { useEffect } from 'react';
// import { FormFieldError } from '@/components/Input/FormFieldErrors';
import { useEditNoteHook } from '@/hooks/admin/notes/useEditNoteHook';
import { INotes } from '@/shared/interface/notes';
import { FiEdit } from 'react-icons/fi';
// import CustomDatePicker from '@/components/elements/date-picker/date-picker';
//import TextEditor from '@/components/Input/CustomEditor';
import CustomSelect from '@/components/Input/CustomSelect';
import TextEditorNew from '@/components/Input/NewTextEditor';

type EditNoteType = {
  row: INotes;
  isMenu?: boolean;
  activity?: any;
  clientId: number;
  triggerEditNote?: any;
  setTriggerEditNote?: any;
};

export default function EditNote({
  row,
  isMenu,
  clientId,
  activity,
  triggerEditNote,
  setTriggerEditNote,
}: EditNoteType) {
  const {
    loading,
    onClose,
    isOpen,
    errors,
    handleChange,
    onOpen,
    handleSubmit,
    setFieldValue,
    // setValues,
    touched,
    values,
    noteStatusOptions,
    selectedOption,
  } = useEditNoteHook({ row, activityId: activity?.id, clientId });
  console.log('values', values);

  console.log('row', row);
  // const timezone = 'Canada/Eastern';

  useEffect(() => {
    if (triggerEditNote) {
      onOpen();
      setTriggerEditNote(false);
    }
  }, [onOpen, setTriggerEditNote, triggerEditNote]);

  return (
    <>
      {isMenu ? (
        <Box
          fontWeight={500}
          onClick={onOpen}
          cursor={'pointer'}
          color={'rgb(79 70 229)'}
          minWidth={'100%'}
        >
          Edit
        </Box>
      ) : (
        <Box
          rounded={'4.8px'}
          onClick={onOpen}
          fontSize={'16px'}
          display={'flex'}
          justifyContent={'center'}
          alignItems={'center'}
          minW={'36px'}
          minH={'36px'}
          maxH={'36px'}
          w={'36px'}
          maxW={'36px'}
          // color={'gray.500'}
          cursor={'pointer'}
          bg={'#eff6ff'}
          color={'#2563eb'}
        >
          <FiEdit />
        </Box>
      )}
      <CustomModal
        w={{ base: '90%', md: '50%' }}
        headertext={'Edit Note'}
        open={isOpen}
        onOpenChange={() => {
          if (loading.update) {
            return;
          }
          onClose();
        }}
      >
        <form onSubmit={handleSubmit}>
          <Stack gap={'2rem'}>
            <Grid templateColumns="repeat(2, 1fr)" width="100%" gap={'6'}>
              <GridItem>
                {/* <CustomDatePicker
                  onChange={(e) => {
                    setFieldValue('note_date', e);
                  }}
                  defaultDate={values.note_date}
                  showTime={true}
                  timeZone={timezone}
                  // isDisabled={}
                /> */}
                <StringInput
                  inputProps={{
                    defaultValue: values.note_date as unknown as string,
                    type: 'datetime-local',

                    name: 'note_date',
                    value: values.note_date as unknown as string,
                    onChange: handleChange,
                  }}
                  fieldProps={{
                    label: 'Note Date',
                  }}
                />
              </GridItem>
              <GridItem>
                <CustomSelect
                  placeholder="Status "
                  options={noteStatusOptions}
                  onChange={(val) => {
                    setFieldValue('status', val.value);
                  }}
                  label="Status"
                  selectedOption={selectedOption}
                />
              </GridItem>
            </Grid>
            <StringInput
              inputProps={{
                name: 'title',
                value: values.title || '',
                onChange: handleChange,
              }}
              fieldProps={{
                invalid: touched.title && !!errors.title,
                label: 'Note Title',
                errorText: errors.title,
              }}
            />

            <TextEditorNew
              initialContent={values.notes}
              initialPresent={!!values.notes}
              saveContent={(notes: any) => setFieldValue('notes', notes)}
            />
            <Button
              bg={'primary.500'}
              disabled={loading.update}
              type="submit"
              loading={loading.update}
            >
              Save
            </Button>
          </Stack>
        </form>
      </CustomModal>
    </>
  );
}
