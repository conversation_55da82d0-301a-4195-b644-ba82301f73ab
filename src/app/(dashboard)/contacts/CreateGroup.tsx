import SearchContact from '@/components/elements/search/SearchContact';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { TuseGroups } from '@/hooks/clients/useGroups';
import {
  For,
  Heading,
  HStack,
  Icon,
  Span,
  Stack,
  StackSeparator,
  Text,
} from '@chakra-ui/react';
import React from 'react';
import { LuX } from 'react-icons/lu';

export function CreateGroup({ hook }: { hook: TuseGroups }) {
  const {
    tagName,
    groupClients,
    submitIsLoading,
    handleAddClient,
    handleRemoveClient,
    handleCreateSubmit,
    setTagName,
  } = hook;
  const canSubmit = groupClients?.length > 0;

  return (
    <Stack gap={'8'} separator={<StackSeparator />}>
      <HStack justifyContent={'space-between'}>
        <Heading size={'sm'}>Details</Heading>

        <Stack>
          <StringInput
            inputProps={{
              placeholder: 'Enter group name',
              onChange: (e) => setTagName(e?.target?.value),
              value: tagName,
            }}
            fieldProps={{ label: 'Group name' }}
          />
        </Stack>
      </HStack>
      <HStack justifyContent={'space-between'} alignItems={'start'}>
        <Stack width={'1/3'}>
          <Heading size={'sm'}>Clients</Heading>
        </Stack>

        <Stack width={'2/3'}>
          <Text>Search for client</Text>
          <SearchContact selectExistingUser={handleAddClient} />
          <Stack gap={'2.5'} pt={'4'}>
            <For each={groupClients || []}>
              {(item: any, index) => (
                <HStack key={item?.client_id} justifyContent={'space-between'}>
                  <Text textTransform={'capitalize'}>
                    <Span fontWeight={'medium'}>{index + 1}: </Span>
                    {item?.name}
                  </Text>

                  <Icon
                    bg={'gray.100'}
                    size={'sm'}
                    cursor={'pointer'}
                    onClick={() => handleRemoveClient(item?.client_id, false)}
                  >
                    <LuX color="red" />
                  </Icon>
                </HStack>
              )}
            </For>
          </Stack>
        </Stack>
      </HStack>

      <HStack justifyContent={'flex-end'}>
        <Button
          bg={'primary.500'}
          onClick={handleCreateSubmit}
          disabled={!canSubmit}
          loading={submitIsLoading}
        >
          Submit
        </Button>
      </HStack>
    </Stack>
  );
}
