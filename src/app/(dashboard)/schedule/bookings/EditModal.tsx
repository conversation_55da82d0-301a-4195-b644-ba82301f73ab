import { CustomModal } from '@/components/elements/modal/custom-modal';
import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import { ToastMessages } from '@/constants/toast-messages';
import supabase from '@/lib/supabase/client';
import { Box, Flex, HStack, Skeleton, Stack, Text } from '@chakra-ui/react';

// import { useGetAllProductsQuery } from '@/api/products/get-all-products';
import { useGetAllSlpQuery } from '@/api/users/get-slps';
import { useMemo, useState } from 'react';
import { useGetServicesQuery } from '@/api/services/get-services-by-slp';

function EditModal({
  row,
  onClose,
  isOpen,
  refetchBookings,
}: {
  row: any;
  onClose: any;
  isOpen: any;
  refetchBookings?: any;
}) {
  const raw = localStorage.getItem('UserState');
  const dataOrg = raw ? JSON.parse(raw) : null;
  const org = dataOrg?.UserState?.organization;
  // console.log('dataOrg >>>', dataOrg);

  const searchParams = new URLSearchParams(window.location.search);
  const orgIdFromUrl = searchParams.get('organization_id');

  // const { data: slpData, isLoading: slpLoading } = useGetAllUsersQuery();
  const { data, isLoading: AllUsersLoading } = useGetAllSlpQuery({
    organization_id: orgIdFromUrl ? Number(orgIdFromUrl) : org?.id,
  });

  // const { data: AllProducts, isLoading: AllProductsLoading } =
  //   useGetAllProductsQuery();

  // const productOptions =
  //   AllProducts && !AllProductsLoading
  //     ? AllProducts.allProducts
  //         .filter((product: any) => product?.status?.toLowerCase() === 'active') // Filter active products
  //         .map((product: any) => ({
  //           label: product.description,
  //           value: product.name,
  //         }))
  //     : [];

  const { data: ServicesData, isLoading: ServicesLoading } =
    useGetServicesQuery(dataOrg?.UserState?.organization_id);

  const servicesOption = useMemo(
    () =>
      ServicesData?.services?.map((item: any) => ({
        label: item?.name,
        value: item,
      })) || [],
    [ServicesData]
  );

  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState({
    event: row.event,
    appointment_raw: row.appointment_raw,
    appointment: row.appointment,
    assigned_to: row.assigned_to,
  });

  const filteredData = useMemo(() => {
    return data
      ? data
          .filter((user: any) => user.status === 'Active')
          ?.map((item: any) => ({
            label: `${item?.first_name} ${item?.last_name}`,
            value: item?.email,
          }))
      : [];
  }, [data]);

  const updatePackage = async (e: any) => {
    try {
      e.preventDefault();
      setLoading(true);
      await supabase
        .from(tableNames.bookings)
        .update({ ...form, event: form.event.name, service_id: form.event.id })
        .eq('id', row.id);

      await refetchBookings();

      toaster.create({
        description: 'Booking updated successfully',
        type: 'success',
      });
      onClose();
    } catch (error: any) {
      toaster.create({
        description: error.message || ToastMessages.somethingWrong,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const formatDateTimeForInput = (isoString: any) => {
    if (!isoString) return '';
    const date = new Date(isoString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}T${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
  };

  return (
    <CustomModal
      w={{ base: '30%', md: '30%' }}
      onOpenChange={onClose}
      open={isOpen}
    >
      <Text fontWeight={500} fontSize={'1.2rem'} textAlign={'center'}>
        Edit Bookings For{' '}
        {`${row?.client_data?.first_name || row?.clients?.first_name || ''} ${
          row?.client_data?.last_name || row?.clients?.last_name || ''
        }`}
      </Text>
      <form onSubmit={updatePackage}>
        <Stack my={'2rem'} gap={'1.5rem'}>
          {/* <HStack>
            <StringInput
              inputProps={{
                name: 'first_name',
                value:
                  row?.client_data?.first_name ||
                  row?.clients?.first_name ||
                  undefined,
                readOnly: true,
              }}
              fieldProps={{
                label: 'First Name',
              }}
            />
            <StringInput
              inputProps={{
                name: 'last_name',
                value:
                  row?.client_data?.last_name ||
                  row?.clients?.last_name ||
                  undefined,
                readOnly: true,
              }}
              fieldProps={{
                label: 'Last Name',
              }}
            />
          </HStack>

          <HStack>
            <StringInput
              inputProps={{
                name: 'email',
                type: 'email',
                value:
                  row?.client_data?.initial_email ||
                  row?.clients?.initial_email ||
                  undefined,
                readOnly: true,
              }}
              fieldProps={{
                label: 'Email',
              }}
            />
            <StringInput
              inputProps={{
                name: 'phone',
                value:
                  row?.client_data?.phone || row?.clients?.phone || undefined,
                readOnly: true,
              }}
              fieldProps={{
                label: 'Phone',
              }}
            />
          </HStack> */}

          <HStack>
            <StringInput
              inputProps={{
                type: 'datetime-local',
                name: 'appointment',
                value: formatDateTimeForInput(form.appointment), // Ensure correct formatting
                onChange: (e) => {
                  const newDateTime = e.target.value; // Get new datetime value in 'YYYY-MM-DDTHH:mm' format
                  setForm({
                    ...form,
                    appointment: new Date(newDateTime).toISOString(), // Convert to UTC ISO format
                  });
                },
              }}
              fieldProps={{
                label: 'Appointment Time',
              }}
            />

            <Box width={'100%'}>
              {ServicesLoading ? (
                <Skeleton
                  height="40px"
                  background="rgba(0, 0, 0, 0.1)" // Slightly darker for visibility
                  opacity={0.7}
                  width="100%"
                  borderRadius="md"
                  mt="30px"
                />
              ) : (
                <CustomSelect
                  placeholder={'Select Service'}
                  options={servicesOption}
                  onChange={(val) => {
                    setForm({ ...form, event: val.value });
                  }}
                  label="Select Service"
                  defaultValue={servicesOption?.find(
                    (item: any) => item?.value === row.event
                  )}
                />
              )}
            </Box>
          </HStack>

          {AllUsersLoading ? (
            <Skeleton
              height="40px"
              width="100%"
              background="rgba(0, 0, 0, 0.1)" // Slightly darker for visibility
              opacity={0.7}
              borderRadius="md"
            />
          ) : (
            <CustomSelect
              placeholder="Type search..."
              options={filteredData}
              label="Assigned To"
              onChange={(val) => {
                setForm({ ...form, assigned_to: val.value });
              }}
              value={form.assigned_to}
              defaultValue={filteredData.find(
                (item: any) => item.value === form.assigned_to
              )}
              controlStyle={{ border: '1px solid #4F4F4F' }}
            />
          )}

          <Flex
            mt={'1.5rem'}
            alignItems={'center'}
            justifyContent={'space-between'}
          >
            <Button onClick={onClose} variant={'outline'} minH={'3rem'}>
              Cancel
            </Button>
            <Button
              loading={loading}
              minH={'3rem'}
              bg={'primary.500'}
              type="submit"
            >
              Save
            </Button>
          </Flex>
        </Stack>
      </form>
    </CustomModal>
  );
}

export default EditModal;
