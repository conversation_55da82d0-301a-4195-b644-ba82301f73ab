'use client';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTable from '@/components/table/CustomTable';
import { useBookingsHook } from '@/hooks/schedule/bookings/useBookingsHook';
import { Flex, Box, Heading, Center, Stack } from '@chakra-ui/react';
import React from 'react';
import { columnDef } from './columnDef';
import { IoIosArrowDown } from 'react-icons/io';
import AddBookingModal from './AddBookingModal';
import StringInput from '@/components/Input/StringInput';
import {
  PopoverArrow,
  PopoverBody,
  PopoverContent,
  PopoverRoot,
  PopoverTitle,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';

export default function AllBookings() {
  const {
    data,
    isLoading,
    setPageSize,
    setCurrentPage,
    current_page,
    page_size,
    eventOptions,
    setEvent,
    event,
    refetch,
    search,
    setSearch,
  } = useBookingsHook();
  console.log('data is ', data);

  return (
    <div>
      <Flex
        alignItems={'center'}
        justifyContent={'space-between'}
        flexWrap={'wrap'}
      >
        <Flex
          width={'100%'}
          alignItems={'start'}
          justifyContent={'space-between'}
        >
          <Heading
            mb={'.5rem'}
            fontWeight={500}
            fontSize={{ base: '1.5rem', md: '2.5rem' }}
          >
            Bookings
          </Heading>
          {/* <Text>Raw of Calendly events</Text> */}

          <AddBookingModal
            refetch={refetch}
            variant={2}
            buttonName="new booking"
          />
        </Flex>

        <Flex mt={6} alignItems={'end'} gap={'1rem'} width={'100%'}>
          <Box position="relative" w={'50%'}>
            <StringInput
              inputProps={{
                onChange: (e) => setSearch(e.target.value),
                value: search,
                placeholder: '<EMAIL>',
              }}
              fieldProps={{
                label: 'Search by Email',
              }}
            />
          </Box>

          <PopoverRoot>
            <PopoverTrigger asChild>
              <Button
                size="sm"
                variant="ghost"
                _hover={{ bg: 'transparent' }}
                display={'flex'}
                alignItems={'center'}
              >
                Event
                <IoIosArrowDown color="gray" />
              </Button>
            </PopoverTrigger>
            <PopoverContent>
              <PopoverArrow />
              <PopoverBody>
                <PopoverTitle fontWeight="medium">Form</PopoverTitle>
                <form className="space-y-4">
                  <Stack mt={'1rem'}>
                    {eventOptions?.map((option: any) => {
                      return (
                        <Checkbox
                          name={option.label}
                          // defaultValue={option.value}
                          key={option.label}
                          checked={event?.includes(option.value as any)}
                          onChange={(e: any) => {
                            if (e.target.checked) {
                              if (option.value === 'others') {
                                setEvent(['others']);
                                return;
                              }
                              setEvent([...event, option.value]);
                            } else {
                              setEvent(
                                event.filter(
                                  (item: any) => item !== option.value
                                )
                              );
                            }
                          }}
                        >
                          {option.label}
                        </Checkbox>
                      );
                    })}
                  </Stack>
                </form>
              </PopoverBody>
            </PopoverContent>
          </PopoverRoot>
        </Flex>
      </Flex>

      {/* ========================================= */}
      <Box minH={'20rem'} mt={'2rem'}>
        {isLoading ? (
          <Center h={'20rem'}>
            <AnimateLoader />
          </Center>
        ) : (
          <CustomTable
            columnDef={columnDef(refetch)}
            data={data?.data || (Array.isArray(data) ? data : [])}
            total={Number(data?.total_count || 0)}
            pagination={{
              row: page_size,
              page: current_page,
            }}
            setPagination={{
              onPageChange: (e) => setCurrentPage(e),
              onRowChange: (e) => setPageSize(e),
            }}
            tableOptions={{
              pageCount: 1,
              manualPagination: true,
            }}
            enableSorting={true}
          />
        )}
      </Box>
    </div>
  );
}
