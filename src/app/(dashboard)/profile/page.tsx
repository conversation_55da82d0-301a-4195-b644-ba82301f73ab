// import General from './General';
import { getUserByEmail } from '@/app/service/user';
import { createSupabaseServer } from '@/lib/supabase/server';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';
import { Metadata } from 'next';
import dynamic from 'next/dynamic';

const General = dynamic(() => import('./General'), {
  ssr: false,
});

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default async function page() {
  const { auth } = createSupabaseServer();
  const user = await auth.getUser();
  const userFromServer = await getUserByEmail(
    user?.data?.user?.email as string
  );

  return (
    <div>
      <General user={userFromServer} />
    </div>
  );
}
