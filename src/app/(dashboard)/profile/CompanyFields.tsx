'use client';

import { useState, useEffect } from 'react';

import {
  Box,
  Stack,
  Text,
  Input,
  HStack,
  VStack,
  Spinner,
  Flex,
} from '@chakra-ui/react';
import { FiPlus, FiTrash2 } from 'react-icons/fi';
import supabase from '@/lib/supabase/client';
import { toaster } from '@/components/ui/toaster';
import { Button } from '@/components/ui/button';
import { useQueryClient } from '@/lib/react-query';
import { queryKey } from '@/constants/query-key';

// Default colors for stages
const DEFAULT_COLORS = [
  '#EF4444', // Cyan
  '#22C55E', // Brown
  '#6B7280', // Gray
  '#DC2626', // Red (darker)
  '#059669', // Green (darker)
  '#7C3AED', // Violet
  '#DB2777', // Pink (darker)
  '#0891B2', // Sky
  '#CA8A04', // Yellow
  '#BE123C', // Rose
];

export const CompanyFields = ({ hook }: any) => {
  const { data: orgData } = hook;
  const queryClient = useQueryClient();
  const [stages, setStages] = useState<any>([]);
  const [newStageLabel, setNewStageLabel] = useState('');
  const [newStageColor, setNewStageColor] = useState(DEFAULT_COLORS[0]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize stages from organization data
  useEffect(() => {
    if (orgData?.organization?.contact_stages) {
      // Add default colors to existing stages if they don't have colors
      const stagesWithColors = orgData.organization.contact_stages.map(
        (stage: any, index: number) => ({
          ...stage,
          color: stage.color || DEFAULT_COLORS[index % DEFAULT_COLORS.length],
        })
      );
      setStages(stagesWithColors);
    }
  }, [orgData]);

  // Check if there are changes to save
  useEffect(() => {
    const originalStages = orgData?.organization?.contact_stages || [];
    setHasChanges(JSON.stringify(stages) !== JSON.stringify(originalStages));
  }, [stages, orgData]);

  const handleAddStage = () => {
    if (!newStageLabel.trim()) {
      toaster.create({
        title: 'Error',
        description: 'Stage name cannot be empty',
        duration: 3000,
      });
      return;
    }

    const newStage: any = {
      id: `custom_${Date.now()}`,
      label: newStageLabel.trim(),
      color: newStageColor,
    };

    setStages((prev: any) => [...prev, newStage]);
    setNewStageLabel('');
    // Set next default color
    setNewStageColor(
      DEFAULT_COLORS[(stages.length + 1) % DEFAULT_COLORS.length]
    );
  };

  const handleRemoveStage = (id: string) => {
    if (stages.length <= 1) {
      toaster.create({
        type: 'error',
        description: 'You must have at least one stage',
      });
      return;
    }
    setStages((prev: any) => prev.filter((stage: any) => stage?.id !== id));
  };

  const handleColorChange = (stageId: string, color: string) => {
    setStages((prev: any) =>
      prev.map((stage: any) =>
        stage.id === stageId ? { ...stage, color } : stage
      )
    );
  };

  const handleLabelChange = (stageId: string, label: string) => {
    setStages((prev: any) =>
      prev.map((stage: any) =>
        stage.id === stageId ? { ...stage, label } : stage
      )
    );
  };

  const handleSave = async () => {
    if (stages.length === 0) {
      toaster.create({
        type: 'error',
        description: 'You must have at least one stage',
      });
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('organizations')
        .update({ contact_stages: stages })
        .eq('id', orgData.organization.id);

      if (error) throw error;

      await queryClient.invalidateQueries({
        queryKey: [queryKey.users.getById, Number(orgData.id)],
      });

      toaster.create({
        type: 'success',
        description: 'Stages saved successfully',
      });
    } catch (error) {
      console.error('Error saving stages:', error);
      toaster.create({
        type: 'error',
        description: 'Failed to save stages',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Stack border="1px solid #d1d5db" rounded={'md'} px={'5'} py={'3'}>
      <Text fontSize="2xl" fontWeight={'600'}>
        Customize Client Stages
      </Text>

      <Text color="gray.400" fontSize={'md'} mt={'-1.5'}>
        Add, remove, or modify your client stages below. You can also customize
        the color for each stage.
      </Text>

      <VStack spaceY={2} align="stretch" mt={'6'} mb={'2'}>
        {stages.map((stage: any) => (
          <HStack
            key={stage.id}
            role="group"
            p={3}
            px={'6'}
            bg="white"
            border="1px solid #d1d5db"
            borderRadius="md"
            justifyContent="space-between"
            align="center"
          >
            <HStack spaceX={1} flex={1} alignItems={'center'}>
              <Box
                width="12px"
                height="12px"
                borderRadius="full"
                bg={stage.color}
              />
              <Input
                value={stage.label}
                onChange={(e) => handleLabelChange(stage.id, e.target.value)}
                //variant="unstyled"
                fontSize="md"
                fontWeight="medium"
                px={'0'}
                outline={'none'}
                border={'none'}
                flex={1}
              />
            </HStack>

            <HStack spaceX={3}>
              <input
                type="color"
                value={stage.color}
                onChange={(e) => handleColorChange(stage.id, e.target.value)}
                style={{
                  width: '50px',
                  height: '30px',
                  border: 'none',
                  padding: '0',
                  background: 'transparent',
                  borderRadius: '0',
                  cursor: 'pointer',
                  appearance: 'none',
                  WebkitAppearance: 'none',
                }}
                className="color-input"
              />

              <Box
                as={FiTrash2}
                color="red.500"
                cursor="pointer"
                opacity={0}
                _groupHover={{ opacity: 1 }}
                transition="opacity 0.2s ease"
                onClick={() => handleRemoveStage(stage.id)}
              />
            </HStack>
          </HStack>
        ))}
      </VStack>

      <VStack
        spaceY={1}
        align="stretch"
        py={'3'}
        borderTop="1px solid #d1d5db"
        borderBottom="1px solid #d1d5db"
      >
        <Text fontSize="xl" fontWeight={'500'}>
          Add New Stage
        </Text>
        <HStack
          spaceX={2}
          border="1px"
          borderStyle="dashed"
          borderColor="#d1d5db"
          rounded={'md'}
          py={'3'}
          px={'5'}
        >
          <Box
            width="12px"
            height="12px"
            borderRadius="full"
            bg={newStageColor}
          />
          <Input
            value={newStageLabel}
            onChange={(e) => setNewStageLabel(e.target.value)}
            placeholder="Enter New stage name"
            onKeyDown={(e) => e.key === 'Enter' && handleAddStage()}
            flex={1}
            // px={'6'}
            // h={'45px'}
            fontSize={'md'}
            border="none"
            _placeholder={{ fontSize: 'md' }}
          />
          <input
            type="color"
            value={newStageColor}
            onChange={(e) => setNewStageColor(e.target.value)}
            style={{
              width: '50px',
              height: '30px',
              border: 'none',
              padding: '0',
              background: 'transparent',
              borderRadius: '0',
              cursor: 'pointer',
              appearance: 'none',
              WebkitAppearance: 'none',
            }}
            className="color-input"
          />
          <Button
            onClick={handleAddStage}
            bg="primary.500"
            disabled={!newStageLabel.trim()}
          >
            <FiPlus /> Add Stage
          </Button>
        </HStack>
      </VStack>
      <Flex justifyContent={'end'} w={'full'} pt={'3'}>
        <Button
          onClick={handleSave}
          bg="primary.500"
          width={'10rem'}
          h={'45px'}
          disabled={!hasChanges || stages.length === 0}
        >
          {isLoading ? <Spinner /> : 'Save Changes'}
        </Button>
      </Flex>
    </Stack>
  );
};
