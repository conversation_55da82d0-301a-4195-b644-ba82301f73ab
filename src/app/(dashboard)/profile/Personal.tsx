import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import { Button } from '@/components/ui/button';
import { env } from '@/constants/env';
import { TUseSlpHook } from '@/hooks/slp/useSlpHook';
import {
  Box,
  Center,
  Flex,
  Heading,
  HStack,
  Icon,
  Progress,
  SimpleGrid,
  Stack,
  Text,
} from '@chakra-ui/react';
import { AiOutlineExclamationCircle } from 'react-icons/ai';
import { IoMdCheckmarkCircle } from 'react-icons/io';

const data = [
  { label: 'America/Toronto', value: 'Canada/Eastern' },
  { label: 'America/Vancouver', value: 'America/Vancouver' },
];

export function Personal({ hook }: { hook: TUseSlpHook }) {
  const {
    data: SlpData,
    payload,
    isLoading,
    handleChange,
    isEdit,
    handleSubmit,
    isFetching,
    eventForm,
    setEventForm,
    UserBySlugData,
    UserBySlugLoading,
    UserBySlugSuccess,
  } = hook;

  if (isFetching)
    return (
      <Center h={'20rem'}>
        <AnimateLoader />
      </Center>
    );

  return (
    <Stack
      border={'1px solid #d1d5db'}
      rounded={'10px'}
      px={'4'}
      py={'6'}
      gap={'8'}
    >
      <Stack gap={'-2.5'}>
        <Heading>Personal Information</Heading>
        <Text fontSize={'0.8rem'} fontWeight={'medium'} color={'gray.500'}>
          Update your personal details and contact information
        </Text>
      </Stack>
      <SimpleGrid gap={'4'} columns={{ base: 1, md: 2 }}>
        <StringInput
          inputProps={{
            name: 'first_name',
            type: 'text',
            placeholder: 'first Name',
            defaultValue: SlpData?.first_name ?? '',
            value: payload?.first_name,
            onChange: (e) => handleChange(e, 'first_name'),
          }}
          fieldProps={{ readOnly: !isEdit, label: 'First Name' }}
        />
        <StringInput
          inputProps={{
            name: 'last_name',
            type: 'text',
            placeholder: 'Last name',
            defaultValue: SlpData?.last_name ?? '',
            value: payload?.last_name,
            onChange: (e) => handleChange(e, 'last_name'),
          }}
          fieldProps={{ readOnly: !isEdit, label: 'Last Name' }}
        />
        <StringInput
          inputProps={{
            name: 'email',
            type: 'email',
            placeholder: 'Email',
            defaultValue: SlpData?.email,
          }}
          fieldProps={{ readOnly: true, label: 'Email' }}
        />

        <CustomSelect
          isDisabled={!isEdit}
          placeholder="Timezone"
          options={data}
          onChange={(val) => {
            handleChange(val?.value, 'timezone');
          }}
          selectedOption={
            payload?.timezone
              ? data.find((item) => item.value === payload.timezone)
              : data.find(
                  (item) =>
                    item.value.toLowerCase() ===
                    SlpData?.timezone?.toLowerCase()
                ) || data.find((item) => item.value === 'Canada/Eastern')
          }
          label="Select Your Timezone"
          defaultValue={
            data.find(
              (item) =>
                item.value.toLowerCase() === SlpData?.timezone?.toLowerCase()
            ) || data.find((item) => item.value === 'Canada/Eastern')
          }
        />
        <StringInput
          inputProps={{
            name: 'Title',
            type: 'text',
            placeholder: 'Therapist',
            defaultValue: SlpData?.office_title ?? '',
            value: payload?.office_title,
            onChange: (e) => handleChange(e, 'office_title'),
          }}
          fieldProps={{ label: 'Title' }}
        />
        <StringInput
          inputProps={{
            name: 'registration',
            type: 'text',
            placeholder: '**************',
            defaultValue: SlpData?.registration || ' ',
          }}
          fieldProps={{ readOnly: true, label: 'Registration' }}
        />
      </SimpleGrid>

      {isEdit && (
        <Stack borderTop={'1px solid #d1d5db'} pt={'6'}>
          <Heading>Professional Registration</Heading>
          <SimpleGrid gap={'4'} columns={{ base: 1, md: 2 }}>
            <StringInput
              inputProps={{
                name: 'entity',
                type: 'text',
                placeholder: 'HHYCCG',
                onChange: (e) => handleChange(e, 'entity'),
              }}
              fieldProps={{ label: 'Entity' }}
            />
            <StringInput
              inputProps={{
                name: 'regNum',
                type: 'text',
                placeholder: '#4446',
                onChange: (e) => handleChange(e, 'regNum'),
              }}
              fieldProps={{ label: 'Reg Number' }}
            />
          </SimpleGrid>

          <Box mt={'1rem'}>
            <Heading fontWeight={600}>My Link</Heading>
            <Text fontSize={'0.8rem'} fontWeight={'medium'} color={'gray.500'}>
              Changing your Speakfluent URL will mean that all of your copied
              links will no longer work and will need to be updated.
            </Text>
            {/* ========================== */}
            <Flex alignItems={'center'} gap={'0.7rem'} mt={'5'}>
              <Text fontSize={'14'}>{env.FRONTEND_URL}/book</Text>
              <StringInput
                inputProps={{
                  defaultValue: SlpData?.event_slug ?? '',
                  fontSize: '1rem',
                  value: eventForm.tempSearch,
                  onChange: (e) =>
                    setEventForm((prev: any) => ({
                      ...prev,
                      tempSearch: e.target.value?.trim()?.toLowerCase(),
                    })),
                }}
                fieldProps={{ required: true }}
              />
            </Flex>
            <Stack gap={'2'} ml={'auto'} w={'10rem'} my={'1rem'}>
              {UserBySlugLoading && eventForm?.search && (
                <Box>
                  <Progress.Root
                    maxW="100%"
                    value={null}
                    colorPalette={'orange'}
                  >
                    <Progress.Track>
                      <Progress.Range />
                    </Progress.Track>
                  </Progress.Root>
                </Box>
              )}
              {UserBySlugSuccess && UserBySlugData?.length === 0 && (
                <Flex
                  w={'100%'}
                  color={'green.600'}
                  alignItems={'center'}
                  gap={'.8rem'}
                  justifyContent={'flex-end'}
                >
                  <Icon boxSize={'1.2rem'}>
                    <IoMdCheckmarkCircle />
                  </Icon>
                  <Text>available</Text>
                </Flex>
              )}
              {UserBySlugSuccess && UserBySlugData?.length > 0 && (
                <Flex
                  w={'100%'}
                  color={'red.600'}
                  alignItems={'center'}
                  gap={'.8rem'}
                  justifyContent={'flex-end'}
                >
                  <Icon boxSize={'1.2rem'}>
                    <AiOutlineExclamationCircle />
                  </Icon>
                  <Text>unavailable</Text>
                </Flex>
              )}
            </Stack>
            {/* ========================== */}
          </Box>
        </Stack>
      )}
      <HStack justifyContent={'flex-end'} gap={'4'}>
        <Button
          bg={'primary.500'}
          onClick={handleSubmit}
          loading={isLoading}
          disabled={!isEdit || (UserBySlugLoading && eventForm.search)}
        >
          Save Changes
        </Button>
      </HStack>
    </Stack>
  );
}
