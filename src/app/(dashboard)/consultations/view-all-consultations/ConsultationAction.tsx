import { CustomModal } from '@/components/elements/modal/custom-modal';
import { useContactStages } from '@/hooks/clients/useGetContactsStages';
import { Box, Text, useDisclosure } from '@chakra-ui/react';
import { FiEdit } from 'react-icons/fi';
import AddConsultation from './AddConsultation';

export default function ConsultationAction({
  row,
  type,
}: {
  row: any;
  type?: string;
}) {
  const addDisclosure = useDisclosure();
  const { contactStagesOptions } = useContactStages(false);

  // console.log('row is ', row);

  return (
    <div>
      {type === 'contact' ? (
        <Text
          color={'blue.500'}
          onClick={addDisclosure.onOpen}
          cursor={'pointer'}
          fontSize={'11px'}
        >
          Show More
        </Text>
      ) : (
        <Box>
          {row?.clients &&
          Array.isArray(row?.notes) &&
          row?.notes.length > 0 ? (
            <FiEdit
              color="grey"
              fontSize={'1.3rem'}
              onClick={addDisclosure.onOpen}
              cursor={'pointer'}
            />
          ) : (
            <Text
              color={'rgb(79 70 229)'}
              fontWeight={500}
              onClick={addDisclosure.onOpen}
              cursor={'pointer'}
            >
              Add
            </Text>
          )}
        </Box>
      )}

      <CustomModal
        w={{ base: '90%', md: '45rem' }}
        open={addDisclosure.open}
        onOpenChange={addDisclosure.onClose}
      >
        <AddConsultation
          row={row}
          contactStagesOptions={contactStagesOptions || []}
          onClose={addDisclosure.onClose}
        />
      </CustomModal>
    </div>
  );
}
