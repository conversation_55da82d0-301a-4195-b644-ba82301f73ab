import React from 'react';
import ViewAll from './view-all-consultations/view-all';
import { Metadata } from 'next';
import { Box } from '@chakra-ui/react';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default async function page() {
  return (
    <Box>
      <ViewAll />
    </Box>
  );
}
