'use client';

import { useGetBookingByIdQuery } from '@/api/bookings/get-booking-by-id';
import { useGetPurchasedPackagesQuery } from '@/api/packages/get-purchased-packages';
import { useGetUserByIdQuery } from '@/api/users/use-get-user-by-id';
import { useTemplateHookReturnType } from '@/app/(dashboard)/admin/template/AddTemplate';
import { LogoLoader } from '@/components/elements/loader/Loader';
import PageLoader from '@/components/elements/loader/PageLoader';
import { Button } from '@/components/ui/button';
import { useNotesHook } from '@/hooks/admin/notes/useNoteHook';
import { useCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import { IUser } from '@/shared/interface/user';
import {
  Box,
  Flex,
  Icon,
  // Separator,
  Stack,
  Tabs,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import moment from 'moment';
import { useRouter } from 'next/navigation';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { IoArrowBackOutline } from 'react-icons/io5';
import ClientNote from '../../sessions/ClientNote';
import PackageInformation from '../../sessions/PackageInformation';
import SoapNote from '../../sessions/SoapNote';
import SoapNoteComponent from '../../sessions/SoapNoteComponent';
import SoapNoteFlow from '../../sessions/soapNoteFlow/SoapNoteFlow';
import PackageItem from './PackageItem';
import EmptyState from '@/components/elements/EmptyState';

export default function CreateInvoice({
  id,
  slpId,
  templateHook,
}: {
  id: any;
  slpId: any;
  templateHook: useTemplateHookReturnType;
}) {
  const { data: initialBooking, isFetching } = useGetBookingByIdQuery(
    Number(id),
    {
      enabled: Boolean(id),
    }
  );

  console.log('initialBooking in create invoice is ', initialBooking);
  const router = useRouter();
  const soapNoteHook = useCreateInvoiceHook({
    initialBooking: initialBooking?.[0] as any,
    onClose: () => {
      // refetch();
      router.back();
    },
    isOpen: true,
  });

  const { open: isRightPanelOpen, onToggle: toggleRightPanel } = useDisclosure({
    defaultOpen: true,
    // defaultOpen: useBreakpointValue({ base: false, lg: true }),
  });

  const { data: SlpData, isLoading: SlpDataLoading } = useGetUserByIdQuery(
    Number(slpId),
    {
      enabled: Boolean(slpId),
    }
  );
  // console.log('SlpData >>>', SlpData);

  const {
    linkedClientOptions,
    SoapNotes,
    SoapNotesLoading,
    selectedClientId,
    setSelectedClientId,
    LinkedClientLoading,
    CurrentClient,
    ClientLoading,
    currentIncompletePackage,
    values,
    booking,
  } = soapNoteHook;
  const pageLoading = LinkedClientLoading || ClientLoading || SlpDataLoading;

  const { data: ClientPurchasedPackages, isLoading: ClientPackagesLoading } =
    useGetPurchasedPackagesQuery(CurrentClient?.id, {
      enabled: Boolean(CurrentClient?.id),
    });

  const noteHook: any = useNotesHook({
    client: CurrentClient,
    size: 10,
    author: { id: slpId } as IUser,
  });

  const predictedBalance =
    currentIncompletePackage?.session_quantity -
    currentIncompletePackage?.balance +
    values?.session_count;

  if (ClientPackagesLoading || pageLoading) {
    return <PageLoader />;
  }

  return (
    <Box overflow={'hidden'} w={'100%'} maxW={'100%'}>
      <Flex
        cursor={'pointer'}
        onClick={() => router.back()}
        alignItems={'center'}
        gap={'.51rem'}
        my={'1rem'}
      >
        <Icon boxSize={'1rem'}>
          <IoArrowBackOutline />
        </Icon>
        <Text>Go back</Text>
      </Flex>
      <Box
        display={'flex'}
        flexDirection={{ base: 'column', lg: 'row' }}
        w={'full'}
        pb={'2rem'}
        gap={4}
        justifyContent={'space-between'}
        position={'relative'}
      >
        <Box flex={1} minW={0}>
          {SlpData?.organization_id == 1 ? (
            <SoapNoteComponent
              soapNoteHook={soapNoteHook}
              templateHook={templateHook}
              section="slp"
              abbr={
                SlpData?.timezone ||
                CurrentClient?.active_slp?.timezone ||
                'Canada/Eastern'
              }
            />
          ) : (
            <SoapNoteFlow
              soapNoteHook={soapNoteHook}
              templateHook={templateHook}
              initialBooking={initialBooking?.[0]}
              section="slp"
              abbr={
                SlpData?.timezone ||
                CurrentClient?.active_slp?.timezone ||
                'Canada/Eastern'
              }
              isFetching={isFetching}
            />
          )}
        </Box>

        <Flex
          borderRight={'2px solid #919494'}
          // alignItems="center"
          direction={'column'}
          // justifyContent="center"
        >
          <Button onClick={toggleRightPanel} bg={'transparent'} color={'black'}>
            {isRightPanelOpen ? <FiChevronRight /> : <FiChevronLeft />}
          </Button>
          {/* <Separator
            minH={{ base: '.5px', lg: '90rem' }}
            w={{ base: 'full', lg: 'auto' }}
            orientation="vertical"
          /> */}
        </Flex>

        {isRightPanelOpen && (
          <Box width={{ base: 'full', lg: '40%' }} flexShrink={0}>
            <Tabs.Root
              w={'full'}
              border={'none'}
              defaultValue={'soap'}
              lazyMount
            >
              <Tabs.List border={'none'}>
                <Tabs.Trigger
                  value="soap"
                  _hover={{ bg: 'pink.50', color: '#e97a5b' }}
                >
                  Notes
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="packages"
                  _hover={{ bg: 'pink.50', color: '#e97a5b' }}
                >
                  Packages
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="invoices"
                  _hover={{ bg: 'pink.50', color: '#e97a5b' }}
                >
                  Invoices
                </Tabs.Trigger>

                <Tabs.Indicator />
              </Tabs.List>
              <Tabs.Content value="soap">
                {SoapNotesLoading ? (
                  <LogoLoader />
                ) : (
                  <SoapNote
                    soapNoteHook={soapNoteHook}
                    // editProfileSoap={editProfileSoap}
                    // client={booking?.clients}
                    // onClose={onClose}
                    linkedClientOptions={linkedClientOptions}
                    LinkedClientLoading={LinkedClientLoading}
                    setSelectedClientId={setSelectedClientId}
                    selectedClientId={selectedClientId}
                    SoapNotesLoading={SoapNotesLoading}
                    SoapNotes={SoapNotes}
                    currentClient={CurrentClient}
                    section="slp"
                    defaultLinkedClient={linkedClientOptions?.find(
                      (item: any) =>
                        Number(item?.value) ===
                        Number(selectedClientId || values?.client_id)
                    )}
                    linkedClientValue={selectedClientId || booking?.client_id}
                  />
                )}
                <ClientNote clientNoteHook={noteHook} />
              </Tabs.Content>

              {SlpData?.organization_id == 1 ? (
                <Tabs.Content value="packages">
                  {currentIncompletePackage ? (
                    <PackageInformation
                      predictedBalance={predictedBalance}
                      data={currentIncompletePackage}
                    />
                  ) : (
                    <EmptyState text="No Packages" />
                  )}
                </Tabs.Content>
              ) : (
                <Tabs.Content value="packages">
                  {ClientPurchasedPackages?.length ? (
                    <Stack gap={'1.5rem'}>
                      {ClientPurchasedPackages?.sort(
                        (a: any, b: any) =>
                          new Date(b.created_at).getTime() -
                          new Date(a.created_at).getTime()
                      ).map((item: any) => {
                        return (
                          <Box key={item.id}>
                            <PackageItem
                              packageData={item}
                              booking={booking}
                              soapNoteHook={soapNoteHook}
                            />
                            {/* <Flex alignItems={'center'} gap={'.75rem'}>
                          <Flex alignItems={'center'} gap={'.5rem'}>
                            <Text fontSize={'.75rem'}>{item.product}</Text>
                          </Flex>
                          <Flex gap={'.5rem'} alignItems={'center'}>
                            <Text>{}</Text>
                          </Flex>
                          <Flex gap={'.5rem'} alignItems={'center'}>
                            <Text fontSize={'.75rem'}>
                              {item?.session_type}
                            </Text>
                          </Flex>

                          <Text fontSize={'.75rem'} color="#7C7C7C">
                            {moment(item?.created_dt).format('MMMM D, YYYY')}
                          </Text>
                        </Flex> */}
                          </Box>
                        );
                      })}
                    </Stack>
                  ) : (
                    <EmptyState text="No Packages" />
                  )}
                </Tabs.Content>
              )}

              <Tabs.Content value="invoices">
                {CurrentClient?.invoices?.length ? (
                  CurrentClient?.invoices
                    ?.sort(
                      (a: any, b: any) =>
                        new Date(b.created_dt).getTime() -
                        new Date(a.created_dt).getTime()
                    )
                    .map((item: any) => {
                      return (
                        <Box key={item.id}>
                          <Flex alignItems={'center'} gap={'.75rem'}>
                            <Flex alignItems={'center'} gap={'.5rem'}>
                              <Text fontSize={'.75rem'} whiteSpace={'nowrap'}>
                                {item?.slp?.first_name} {item?.slp?.last_name}
                              </Text>
                            </Flex>
                            <Flex gap={'.5rem'} alignItems={'center'}>
                              <Text
                                color="#7C7C7C"
                                whiteSpace={'nowrap'}
                              >{`-->`}</Text>
                            </Flex>
                            <Flex gap={'.5rem'} alignItems={'center'}>
                              <Text fontSize={'.75rem'}>
                                {item?.session_type}
                              </Text>
                            </Flex>
                            {/* <Flex gap={'.5rem'} alignItems={'center'}>
                            <Text
                              fontSize={'.8rem'}
                              fontWeight={'500'}
                              color="#7C7C7C"
                            >
                              Memo:
                            </Text>
                            <Text fontSize={'.75rem'}>{item?.memo || ''}</Text>
                          </Flex> */}
                            <Flex gap={'.5rem'} alignItems={'center'}>
                              <Text
                                fontSize={'.8rem'}
                                fontWeight={'500'}
                                color="#7C7C7C"
                              >
                                Description:
                              </Text>
                              <Text fontSize={'.75rem'}>
                                {item?.product || ''}
                              </Text>
                              <Text fontSize={'.75rem'}>
                                {item?.description || ''}
                              </Text>
                            </Flex>
                            <Text fontSize={'.75rem'} color="#7C7C7C">
                              {moment(item?.created_dt).format('MMMM D, YYYY')}
                            </Text>
                          </Flex>
                        </Box>
                      );
                    })
                ) : (
                  <EmptyState text="No Invoices" />
                )}
              </Tabs.Content>
            </Tabs.Root>
          </Box>
        )}
      </Box>
    </Box>
  );
}
