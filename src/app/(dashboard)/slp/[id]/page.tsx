import React, { Suspense } from 'react';
import SlpView from './SlpView';
import { createSupabaseServer } from '@/lib/supabase/server';
import { Metadata } from 'next';
import { generateMetadataUtils } from '@/utils/generate-page-metadata';

export async function generateMetadata(): Promise<Metadata> {
  const metadata = generateMetadataUtils();
  return {
    title: metadata.title,
    description: metadata.description,
  };
}

export default async function page({ params }: { params: { id: string } }) {
  const supabase = await createSupabaseServer();
  const { data } = await supabase.rpc('get_user_by_id', {
    id_param: Number(params.id),
  });

  // console.log('user in the slp id page is ', data);
  // if (!data) {
  //   return <UnAuthorized />;
  // }

  return (
    <div>
      <Suspense>
        <SlpView id={params.id} userFromServer={data} />
      </Suspense>
    </div>
  );
}
