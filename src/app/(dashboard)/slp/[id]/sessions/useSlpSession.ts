import { useGetSlpSessionHighLitesQuery } from '@/api/slp/get-sessions-highlites';
import { useGetSlpSessionsQuery } from '@/api/users/get-slp-sessions';
import { useGetSlpSessionsCalenderQuery } from '@/api/users/get-slp-sessions-calender';
import { toaster } from '@/components/ui/toaster';
import { tableNames } from '@/constants/table_names';
import supabase from '@/lib/supabase/client';
import { IUser } from '@/shared/interface/user';
import { formatWithTimeZone } from '@/utils/timezone-format';
import { useDisclosure } from '@chakra-ui/react';
import FullCalendar from '@fullcalendar/react';
import moment from 'moment';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

export const useSlpSession = (slpId: any, slp?: IUser) => {
  const timezone = slp?.timezone || 'Canada/Eastern';
  const calendarRef = useRef<FullCalendar | null>(null);
  const [singleEvent, setSingleEvent] = useState<any>(null);
  const {
    onOpen: onEditEventOnOpen,
    onClose: onEditEventOnClose,
    open: editEventModalOpen,
  } = useDisclosure();

  const [calendarHeight, setCalendarHeight] = useState(
    `${window.innerHeight - 100}px`
  );
  const [selectedDate, setSelectedDate] = useState(moment().toISOString());

  const router = useRouter();
  const searchParams = useSearchParams();
  const initialDate = searchParams.get('date')
    ? moment(searchParams.get('date'), 'YYYY-MM-DD')
    : moment().tz(timezone);
  const [selectDate, setSelectDate] = useState<any>(initialDate);
  const [currentMonth, setCurrentMonth] = useState(moment().format('YYYY-MM'));
  const [currentView, setCurrentView] = useState('timeGridWeek');
  const [isCalendarReady, setIsCalendarReady] = useState(false);

  const [highlightDates, setHighlightDates] = useState<Date[]>([]);
  const [currentYear, setCurrentYear] = useState(initialDate.year());

  // Get organization_id from URL params
  const organizationIdFromParams = searchParams.get('organization_id');

  // QUERIES
  const { data: SlpSessionHighlight } = useGetSlpSessionHighLitesQuery({
    slpId: slpId,
    year: currentYear,
    organization_id: organizationIdFromParams || slp?.organization_id,
  });

  console.log('organizationIdFromParams', organizationIdFromParams);

  const {
    data: SlpSessions,
    isLoading: SlpSessionsLoading,
    isFetching: SlpSessionFetching,
    refetch: sessionRefetch,
  } = useGetSlpSessionsQuery({
    date: selectDate?.format('YYYY-MM-DD'),
    slpId: slpId,
    timezone,
    organization_id: organizationIdFromParams || slp?.organization_id,
  });

  console.log('SlpSessions', SlpSessions);

  const {
    data: SlpCalendarSessions,
    isLoading: SlpSessionsCalenderLoading,
    refetch: calenderBookingsRefetch,
  } = useGetSlpSessionsCalenderQuery({
    currentMonth,
    slpId: slpId,
    timezone,
    organization_id: organizationIdFromParams || slp?.organization_id,
  });

  const abbr = {
    abbr: moment()
      ?.tz(slp?.timezone || 'Canada/Eastern')
      .zoneAbbr(),
    zone: slp?.timezone || 'Canada/Eastern',
  };

  const handleEventDrop = async (eventDropInfo: any) => {
    const { event } = eventDropInfo;

    const eventId = event.id || event.extendedProps?.id;
    if (!eventId) {
      console.error('Event ID is missing!');
      return;
    }

    try {
      const { error } = await supabase
        .from(tableNames.bookings)
        .update({
          appointment: event?.start.toISOString(),
        })
        .eq('id', Number(eventId));

      if (error) throw error;

      await calenderBookingsRefetch();
      toaster.create({
        description: 'Booking updated successfully',
        type: 'success',
      });
    } catch (error) {
      console.error('Failed to update booking:', error);
      event.revert();
    }
  };

  const events = SlpCalendarSessions?.data
    ?.map((event: any) => {
      const start = event?.appointment;
      const formattedTime = formatWithTimeZone(start, abbr?.zone);

      const reconstructedStart = moment(start)
        .set({
          hour: moment(formattedTime, 'h:mm A').hour(),
          minute: moment(formattedTime, 'h:mm A').minute(),
          second: 0,
        })
        .utc()
        .format();

      const end = moment(reconstructedStart)
        .add(event?.slp_notes?.duration, 'minutes')
        .format();

      return {
        title: event?.event,
        start: reconstructedStart,
        end,
        extendedProps: event,
      };
    })
    .filter(Boolean);

  const handleDatesSet = (dateInfo: any) => {
    const newMonth = moment(dateInfo.view.currentStart).format('YYYY-MM');
    if (newMonth !== currentMonth) {
      setCurrentMonth(newMonth);
    }
    setSelectedDate(moment(dateInfo.view.currentStart).toISOString());
    setCurrentView(dateInfo.view.type);
  };

  const handleEventClick = (eventClickInfo: any) => {
    const bookingData = eventClickInfo.event.extendedProps;
    if (!bookingData) return;

    setSingleEvent(bookingData);
    onEditEventOnOpen();
  };

  const updateCalendarSize = () => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.updateSize();
      setCalendarHeight(`${window.innerHeight - 100}px`);
    }
  };

  const handleDateChange = (newDate: Date) => {
    const formattedDate = moment(newDate).format('YYYY-MM-DD');
    if (formattedDate !== selectDate.format('YYYY-MM-DD')) {
      console.log('is changing date', organizationIdFromParams);
      setSelectDate(moment(formattedDate));
      router.replace(
        `?tab=sessions&date=${formattedDate}${organizationIdFromParams ? `&organization_id=${organizationIdFromParams}` : ''}`,
        {
          scroll: false,
        }
      );

      console.log('is changing date-2', organizationIdFromParams);
    }
  };

  const handleMonthYearChange = (month: number, year: number) => {
    if (year !== currentYear) {
      setCurrentYear(year);
    }
  };

  useEffect(() => {
    const urlDate = searchParams.get('date') || moment().format('YYYY-MM-DD');
    setSelectDate(moment(urlDate, 'YYYY-MM-DD'));
  }, [searchParams, setSelectDate]);

  useEffect(() => {
    updateCalendarSize();

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        setTimeout(() => {
          updateCalendarSize();
        }, 10);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('resize', updateCalendarSize);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('resize', updateCalendarSize);
    };
  }, []);

  useEffect(() => {
    const timeout = setTimeout(() => setIsCalendarReady(true), 50);
    return () => clearTimeout(timeout);
  }, []);

  useEffect(() => {
    if (SlpSessionHighlight) {
      const formattedHighlightedDates = SlpSessionHighlight.map((item: any) => {
        const isoDate = new Date(item.appointment);
        const dateOnly = new Date(
          isoDate.getUTCFullYear(),
          isoDate.getUTCMonth(),
          isoDate.getUTCDate()
        );
        return dateOnly;
      });

      setHighlightDates(formattedHighlightedDates);
    }
  }, [SlpSessionHighlight]);

  return {
    handleDateChange,
    handleMonthYearChange,
    SlpSessions,
    isCalendarReady,
    SlpSessionsLoading,
    SlpSessionFetching,
    handleEventClick,
    SlpSessionsCalenderLoading,
    currentView,
    events,
    calenderBookingsRefetch,
    onEditEventOnOpen,
    setCurrentYear,
    organizationIdFromParams,
    editEventModalOpen,
    onEditEventOnClose,
    calendarHeight,
    calendarRef,
    abbr,
    singleEvent,
    handleEventDrop,
    selectedDate,
    selectDate,
    handleDatesSet,
    highlightDates,
    SlpCalendarSessions,
    sessionRefetch,
  };
};
