import { TCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import { Box, Flex, Icon, Stack, Text } from '@chakra-ui/react';
import { FiChevronDown, FiChevronUp, FiFile } from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { useInvoiceHook } from '@/hooks/slp/soapNoteFlow/useInvoiceHook';
import CustomSelect from '@/components/Input/CustomSelect';
import { PiWarningFill } from 'react-icons/pi';
import PackageInformation from '@/components/elements/PackageInformation';
import { Field } from '@/components/ui/field';
import {
  NumberInputField,
  NumberInputRoot,
} from '@/components/ui/number-input';
import StringInput from '@/components/Input/StringInput';
import { durationOptions } from '@/data/options/duration';
import { FaInfoCircle } from 'react-icons/fa';
// import CustomDatePicker from '@/components/elements/date-picker/date-picker';
import moment from 'moment';
import { useEffect, useMemo } from 'react';
import { invoiceStatusOptions } from '@/data/options';
import { Checkbox } from '@/components/ui/checkbox';
import PackageItem from '../../create-invoice/[booking]/PackageItem';
import { useRouter } from 'next/navigation';

export default function InvoiceCreation({
  soapNoteHook,
  booking,
}: {
  soapNoteHook: TCreateInvoiceHook;
  booking: any;
}) {
  const {
    toggleForm,
    isOpen,
    values,
    setFieldValue,
    handleChange,
    handleFormSubmit,
    loading,
    activePackageOptions,
    servicesOption,
    ServicesData,
    CreateRedeemSessionLoading,
    isEdit,
  } = useInvoiceHook({
    soapNoteHook: soapNoteHook,
    booking,
  });
  // console.log('ServicesData is ', ServicesData);

  // const selectedSessionType = soapNoteHook?.finalSessionOptions?.find(
  //   (option: any) =>
  //     option.value?.toLowerCase() === values?.session_type?.toLowerCase()
  // );

  const serviceDetail = useMemo(() => {
    if (isEdit) {
      const purchase = booking?.services_purchases?.find(
        (item: any) =>
          item?.invoice_items?.service_id === values?.service_id ||
          booking?.slp_notes?.invoice?.service_id ||
          booking?.service_id
      );
      return purchase?.invoice_items;
    } else {
      return ServicesData?.services?.find(
        (item: any) =>
          item?.id === values?.service_id ||
          booking?.slp_notes?.invoice?.service_id ||
          booking?.service_id
      );
    }
  }, [
    isEdit,
    booking?.services_purchases,
    booking?.slp_notes?.invoice?.service_id,
    booking?.service_id,
    ServicesData?.services,
    values?.service_id,
  ]);
  const router = useRouter();

  const sessionIsPackage = values?.session_type?.toLowerCase() === 'package';
  const predictedBalance =
    soapNoteHook?.currentIncompletePackage?.session_quantity -
    soapNoteHook?.currentIncompletePackage?.balance +
    values?.session_count;

  // const lowerBound =
  //   Number(soapNoteHook?.currentIncompletePackage?.package_size) -
  //   Number(soapNoteHook?.currentIncompletePackage?.balance) +
  //   1;
  // const upperBound = lowerBound + Number(values.session_count || 0) - 1;

  const totalDuration =
    Number(values.session_count || 0) *
    Number(soapNoteHook?.currentIncompletePackage?.session_duration);
  const thePackageItemSelected = values?.package?.purchased_package_items?.find(
    (item: any) => item?.service?.id === values?.service_id
  );
  // const timezone = soapNoteHook?.CurrentClient?.active_slp?.timezone;
  // console.log('booking?.appointment', booking?.appointment);
  console.log('object', activePackageOptions);

  useEffect(() => {
    if (isEdit) {
      return;
    }
    if (servicesOption?.length > 0) {
      let serviceItem = servicesOption?.find(
        (service: any) => service?.value == booking?.service_id
      );
      if (!serviceItem) {
        serviceItem = servicesOption?.[0];
      }
      setFieldValue('service_id', serviceItem?.value);
      // setFieldValue('product', serviceItem?.value);
    }

    setFieldValue('status', 'AWAITING_PAYMENT');
  }, [
    booking?.service_id,
    isEdit,
    setFieldValue,
    servicesOption,
    values?.is_package,
  ]);
  // console.log('servicesOption is ', servicesOption);

  return (
    <Box
      border={'1px solid #FEFEFE'}
      boxShadow={'lg'}
      rounded={'12px'}
      py={'1.5rem'}
      px={'10px'}
      w={'full'}
      minH={'6rem'}
      maxH={'fit-content'}
      scrollbar={'hidden'}
    >
      <Box
        display={'grid'}
        gridTemplateColumns={'1fr auto'}
        width={'full'}
        maxWidth={'full'}
        gap={'1.5rem'}
        overflow={'hidden'}
        justifyContent={'space-between'}
      >
        <Box display={'flex'} gap={'1.25rem'}>
          <Box
            rounded={'full'}
            fontSize={'18px'}
            display={'flex'}
            justifyContent={'center'}
            alignItems={'center'}
            minW={'36px'}
            w={'36px'}
            h={'36px'}
            maxW={'36px'}
            cursor={'pointer'}
            color={'#E97A5B'}
            border={'2px solid #E97A5B'}
          >
            {' '}
            <FiFile />
          </Box>
          <Box>
            <Box
              display={'flex'}
              justifyContent={'normal'}
              alignItems={'center'}
              gap={'.5rem'}
            >
              <Text color={'GrayText'}>Invoice</Text>
              {!booking?.slp_notes?.id ? (
                <FaInfoCircle
                  color={'Gold'}
                  title={'Please Create Note First'}
                />
              ) : null}
            </Box>

            {isEdit ? (
              <Text>
                <b>Invoice Due: </b>On
                {moment(booking?.slp_notes?.invoice.due_date)?.format(
                  ' MMMM D, YYYY'
                )}
              </Text>
            ) : null}
          </Box>
        </Box>
        <Button
          rounded={'2xl'}
          fontSize={'14px'}
          bg={'transparent'}
          color={'#E97A5B'}
          title={!booking?.slp_notes?.id ? 'Please Create Note First' : ''}
          disabled={!booking?.slp_notes?.id}
          border={'2px solid #E97A5B'}
          onClick={toggleForm}
          //   onClick={soapNoteHook?.handleSaveInvoiceDraft}
          //   loading={
          //     soapNoteHook?.handleCreateInvoiceLoading ||
          //     soapNoteHook?.saveInvoiceDraft
          //   }
          //   disabled={
          //     soapNoteHook?.booking?.slp_notes?.invoice ||
          //     soapNoteHook?.handleCreateInvoiceLoading ||
          //     soapNoteHook?.saveInvoiceDraft ||
          //     soapNoteHook?.values?.product
          //   }
        >
          {isEdit ? 'Invoice Created' : 'Create Invoice'}
          {isOpen ? (
            <FiChevronUp color="#E97A5B" />
          ) : (
            <FiChevronDown color="#E97A5B" />
          )}
        </Button>
      </Box>
      <Box maxH={isOpen ? 'fit-content' : 0} overflow={'hidden'}>
        <form onSubmit={handleFormSubmit}>
          {' '}
          <Box>
            <Stack pl={'.5rem'} gap={'0.5rem'} mt={1}>
              {/* <CustomDatePicker
                onChange={(e) => {
                  setFieldValue('invoice_date', e);
                }}
                defaultDate={values.note_date}
                showTime={true}
                timeZone={timezone}
                isDisabled={loading}
              /> */}

              <CustomSelect
                placeholder={'Select Service'}
                options={servicesOption}
                selectedOption={servicesOption?.find(
                  (item: any) => item?.value === values.service_id
                )}
                onChange={(val) => {
                  // setFieldValue('product', val.value);
                  setFieldValue('service_id', val.value);
                }}
                // label={`Select Product ${productDetail?.price ? `($${productDetail?.price})` : ''}`}
                label="Select Service"
                defaultValue={servicesOption?.find(
                  (item: any) =>
                    item?.value === values.service_id || booking?.service_id
                )}
                // isDisabled={booking?.slp_notes?.invoice}
              />
              <Checkbox
                display={'none'}
                onCheckedChange={(e) => {
                  console.log('e is ', e);
                  setFieldValue('is_package', e.checked);
                  if (!e.checked) {
                    setFieldValue('package', null);
                  }
                }}
                value={values?.is_package}
                checked={values?.is_package}
                my={'.8rem'}
                // disabled={activePackageOptions?.length <= 0}
              >
                <Text fontSize={'1rem'}>Use Package</Text>
              </Checkbox>
              {values?.is_package && (
                <CustomSelect
                  placeholder={'Select Package'}
                  options={activePackageOptions || []}
                  value={values.package?.id}
                  onChange={(val) => {
                    setFieldValue('package', val.value);
                    // setFieldValue('product_id', val.id);
                  }}
                  // label={`Select Product ${productDetail?.price ? `($${productDetail?.price})` : ''}`}
                  label="Select Package"
                  // defaultValue={activePackageOptions?.find(
                  //   (item: any) => item?.id === values.package?.id
                  // )}
                  // isDisabled={booking?.slp_notes?.invoice}
                />
              )}

              {values?.is_package && values?.package && (
                <PackageItem
                  packageData={values?.package}
                  showMemo={true}
                  toUse={values?.session_count || 1}
                  serviceId={values?.service_id}
                  // isEdit={isEdit}
                />
              )}
              {/* {productDetail?.price ? (
                <StringInput
                  inputProps={{
                    value: `$${productDetail?.price}`,
                  }}
                  fieldProps={{
                    label: 'Price',
                    readOnly: true,
                  }}
                />
              ) : null} */}
              <StringInput
                inputProps={{
                  value:
                    serviceDetail?.price != null
                      ? `$${serviceDetail.price}`
                      : '$',
                }}
                fieldProps={{
                  label: 'Price',
                  readOnly: true,
                }}
              />
              <StringInput
                inputProps={{
                  // value: `${values?.duration || productDetail?.minutes} Minutes`,
                  value: `${serviceDetail?.duration_minutes || values?.duration || 30} Minutes`,
                }}
                fieldProps={{
                  label: 'Total Duration (minutes)',
                  readOnly: true,
                }}
              />
              <Field label="Session Quantity" flex={1}>
                <NumberInputRoot
                  defaultValue={values.session_count || 1}
                  min={1}
                  // disabled={thePackageItemSelected?.remaining == 0}
                  // max={thePackageItemSelected?.remaining || null}
                  value={values.session_count}
                  onValueChange={(e) => {
                    console.log('e', e);
                    if (
                      thePackageItemSelected &&
                      thePackageItemSelected?.remaining > 0
                    ) {
                      const value =
                        Number(e.value) <= thePackageItemSelected?.quantity
                          ? Number(e.value)
                          : thePackageItemSelected?.quantity;
                      return setFieldValue('session_count', Number(value));
                    }
                    setFieldValue('session_count', Number(e.value));
                  }}
                >
                  <NumberInputField />
                </NumberInputRoot>
              </Field>
              {/* <StringInput
                inputProps={{
                  placeholder: 'eg. x/12 (2023)',
                  name: 'invoice_memo',
                  value: values.invoice_memo,
                  onChange: handleChange,
                }}
                fieldProps={{
                  // label: `Memo  (Optional)`,
                  label: `Memo`,
                }}
              /> */}
              <CustomSelect
                placeholder="Status"
                options={invoiceStatusOptions}
                selectedOption={invoiceStatusOptions?.find(
                  (item: any) =>
                    item?.value?.toLowerCase() === values.status?.toLowerCase()
                )}
                onChange={(val) => setFieldValue('status', val?.value)}
                label="Status"
                defaultValue={invoiceStatusOptions?.find(
                  (item) =>
                    item.value?.toLowerCase() === values?.status?.toLowerCase()
                )}
                name="status"
              />
              <StringInput
                inputProps={{
                  type: 'date',
                  defaultValue:
                    values?.due_date || booking?.appointment?.split('T')[0],
                  name: 'due_date',
                  value: values.due_date || undefined,
                  onChange: handleChange,
                }}
                fieldProps={{
                  label: 'Invoice Due Date',
                }}
              />
              {/* <StringInput
                inputProps={{
                  placeholder: 'Internal Memo',
                  name: 'internal_memo',
                  value: values.internal_memo,
                  onChange: handleChange,
                }}
                fieldProps={{
                  label: `Internal Memo`,
                }}
              /> */}

              {soapNoteHook?.isIncompletePackageMoreThanOne && (
                <Flex alignItems={'center'} gap={'1rem'}>
                  <Text fontSize={'.75rem'} color={'red'}>
                    WARNING !! There are more than one Incomplete packages{' '}
                  </Text>
                  <Icon boxSize={'1rem'} color={'gold'}>
                    <PiWarningFill />
                  </Icon>
                </Flex>
              )}
              {sessionIsPackage && !soapNoteHook?.currentIncompletePackage && (
                <Flex alignItems={'center'} gap={'1rem'}>
                  <Text fontSize={'.75rem'} color={'red'}>
                    WARNING !! This client has no package{' '}
                  </Text>
                  <Icon boxSize={'1rem'} color={'gold'}>
                    <PiWarningFill />
                  </Icon>
                </Flex>
              )}
              {sessionIsPackage && soapNoteHook?.currentIncompletePackage && (
                <PackageInformation
                  predictedBalance={predictedBalance}
                  data={soapNoteHook?.currentIncompletePackage}
                />
              )}
              <Flex alignItems={'center'} gap={'1rem'}>
                {sessionIsPackage && soapNoteHook?.currentIncompletePackage && (
                  <Field label="Session Quantity" flex={1}>
                    <NumberInputRoot
                      defaultValue={values.session_count}
                      min={1}
                      max={soapNoteHook?.currentIncompletePackage?.balance}
                      value={values.session_count}
                      onValueChange={(e) =>
                        setFieldValue('session_count', Number(e.value))
                      }
                    >
                      <NumberInputField />
                    </NumberInputRoot>
                  </Field>
                )}
                {sessionIsPackage &&
                  !soapNoteHook?.currentIncompletePackage && (
                    <Field label="Session Quantity" flex={1}>
                      <NumberInputRoot
                        defaultValue={values.session_count}
                        min={1}
                        value={values.session_count}
                        onValueChange={(e) =>
                          setFieldValue('session_count', Number(e.value))
                        }
                      >
                        <NumberInputField />
                      </NumberInputRoot>
                    </Field>
                  )}
                {sessionIsPackage && soapNoteHook?.currentIncompletePackage && (
                  <Box flex={1}>
                    <StringInput
                      inputProps={{
                        value: `${soapNoteHook?.currentIncompletePackage?.session_duration} Minutes`,
                      }}
                      fieldProps={{ readOnly: true, label: 'Unit Duration' }}
                    />
                  </Box>
                )}
              </Flex>
              {sessionIsPackage && soapNoteHook?.currentIncompletePackage && (
                <StringInput
                  inputProps={{
                    value: `${totalDuration} Minutes`,
                  }}
                  fieldProps={{
                    label: 'Total Duration (minutes)',
                    readOnly: true,
                  }}
                />
              )}
              {Boolean(
                [
                  'package',
                  'payg',
                  'assessment',
                  'referral',
                  '',
                  'undefined',
                  'null',
                ].includes(String(values?.session_type)?.toLowerCase())
              ) && (
                <Stack>
                  {!sessionIsPackage &&
                    soapNoteHook?.Slp?.organization?.id === 1 && (
                      <Box>
                        <CustomSelect
                          placeholder="Select Duration"
                          options={durationOptions}
                          onChange={(val) =>
                            setFieldValue('duration', val.value)
                          }
                          label="Duration (Minutes)"
                          selectedOption={durationOptions?.find(
                            (item) => item?.value === values.duration
                          )}
                        />
                      </Box>
                    )}

                  {sessionIsPackage &&
                    !soapNoteHook?.currentIncompletePackage && (
                      <Box>
                        <CustomSelect
                          placeholder="Select duration"
                          options={durationOptions}
                          onChange={(val) => {
                            setFieldValue('duration', val.value);
                          }}
                          label="Duration (Minutes)"
                          defaultValue={durationOptions?.find(
                            (item) => item?.value === values.duration
                          )}
                        />
                      </Box>
                    )}

                  {/* {sessionIsPackage &&
                    soapNoteHook?.currentIncompletePackage && (
                      <StringInput
                        inputProps={{
                          value: `${lowerBound} -  ${upperBound} / ${soapNoteHook?.currentIncompletePackage?.session_quantity}`,
                        }}
                        fieldProps={{
                          label: `Suggested Memo`,
                          // isReadOnly: true,
                        }}
                      />
                    )} */}
                </Stack>
              )}
            </Stack>
            <Flex justifyContent={'right'}>
              {!isEdit ? (
                <Button
                  mt={'1rem'}
                  bg="primary.500"
                  loading={loading || CreateRedeemSessionLoading}
                  type="submit"
                  minW="10rem"
                  rounded={'2xl'}
                >
                  Create Invoice
                </Button>
              ) : (
                <Button
                  mt={'1rem'}
                  bg="primary.500"
                  loading={loading || CreateRedeemSessionLoading}
                  type="button"
                  minW="10rem"
                  rounded={'2xl'}
                  onClick={() => {
                    router.push(
                      `/invoices/edit/${booking?.slp_notes?.invoice?.id}`
                    );
                  }}
                >
                  Edit Invoice
                </Button>
              )}
            </Flex>
          </Box>
        </form>
      </Box>
    </Box>
  );
}
