import { useUpdateInvoiceMutation } from '@/api/invoices/update-invoice';
import { useCreateTransactionMutation } from '@/api/transactions/create-transaction';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import NumericInput from '@/components/Input/NumericInput';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

import LinkExistingTransactions from '@/app/(dashboard)/invoices/newSFInvoiceFlow/LinkExistingTransactions';
import {
  transactionMethodOptions,
  // transactionStatusOptions,
  transactionTypeOptions,
} from '@/data/options';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { determineInvoiceStatus } from '@/reuseables/invoice/helpers';
import { Box, Heading, Stack, Tabs, Text } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import moment from 'moment';
import { useState } from 'react';
import * as yup from 'yup';

export const initialTransaction = {};
const transactionMethodValues = [
  'CASH',
  'E_TRANSFER',
  'BANK_TRANSFER',
  'CREDIT_CARD',
  'PAYPAL',
  'OTHERS',
];
const transactionTypeValues = ['PAYMENT', 'REFUND', 'CHARGE_BACK', 'DEPOSIT'];
// const transactionStatusValues = [
//   'PAID',
//   'PENDING',
//   'PARTIALLY_PAID',
//   'AWAITING_PAYMENT',
//   'COMPLETED',
//   'FAILED',
//   'REFUNDED',
// ];

export const transactionSchema = yup.object().shape({
  amount: yup
    .number()
    .typeError('Amount must be a number')
    .min(1, 'Amount must be greater than 0')
    .required('Amount is required'),

  transaction_type: yup
    .string()
    .oneOf(transactionTypeValues, 'Invalid transaction type')
    .required('Transaction type is required'),

  // status: yup
  //   .string()
  //   .oneOf(transactionStatusValues, 'Invalid status')
  //   .required('Status is required'),

  currency_code: yup
    .string()
    .length(3, 'Currency code must be 3 characters')
    .required('Currency code is required'),

  payment_method: yup
    .string()
    .oneOf(transactionMethodValues, 'Invalid payment method')
    .required('Payment method is required'),

  note: yup.string().max(255, 'Note cannot exceed 255 characters').optional(),

  transaction_date: yup
    .string()
    .test('valid-date', 'Invalid date format', (value) =>
      moment(value, 'YYYY-MM-DD', true).isValid()
    )
    .required('Transaction date is required'),
});

export default function PaymentForm({
  amountDue,
  initialBooking,
  createPaymentDisclosure,
  // invalidateQueryKey = queryKey.bookings.getById,
  linkTransactionHook,
}: any) {
  const queryClient = useQueryClient();
  const {
    mutateAsync: CreateTransaction,
    isLoading: CreateTransactionLoading,
  } = useCreateTransactionMutation();
  const { UserFromQuery } = useSupabaseSession();
  const { mutateAsync: updateInvoices } = useUpdateInvoiceMutation();

  const [activeTab, setActiveTab] = useState('newTransaction');

  const {
    values,
    handleBlur,
    handleChange,
    setFieldValue,
    errors,
    touched,
    handleSubmit,
  } = useFormik({
    initialValues: {
      amount: amountDue,
      transaction_type: 'PAYMENT',
      status: 'SUCCESS',
      currency_code: 'USD',
      payment_method: 'CASH',
      //   invoice_number: '',
      note: '',
      transaction_date: moment().format('YYYY-MM-DD'),
    },
    validationSchema: transactionSchema,
    onSubmit: async (values) => {
      // console.log('values is ', values);
      // console.log('UserFromQuery is ', UserFromQuery);
      if (!UserFromQuery?.organization_id || !UserFromQuery?.id) {
        toaster.create({ description: 'Invalid Session!', type: 'error' });
        return;
      }
      if (!initialBooking?.slp_notes?.invoice?.id) {
        toaster.create({
          description: 'Please create invoice for this session!',
          type: 'error',
        });
        return;
      }
      const payload = {
        ...values,
        organization_id: UserFromQuery?.organization_id,
        user_id: UserFromQuery?.id,
        invoice_id: initialBooking?.slp_notes?.invoice?.id,
        client_id: initialBooking?.client_id,
      };
      console.log('payload is ', payload);
      //   return;

      await CreateTransaction(payload);
      const statusData = {
        transactions: [
          ...initialBooking.slp_notes.invoice.transactions,
          payload,
        ],
        total_price: initialBooking.slp_notes.invoice.total_price,
        tax_value: 0,
        discount: initialBooking.slp_notes.invoice.discount,
      };
      const invoiceStatus = determineInvoiceStatus(statusData);
      await updateInvoices({
        data: { status: invoiceStatus },
        id: initialBooking?.slp_notes?.invoice?.id,
      });
      console.log('invoiceStatus', invoiceStatus);
      await createPaymentDisclosure.onClose();

      await queryClient.invalidateQueries({
        queryKey: [queryKey.bookings.getById],
        exact: false,
        refetchType: 'all',
      });
      await queryClient.invalidateQueries({
        queryKey: ['newsf-get-invoice-by-id'],
        exact: false,
        refetchType: 'all',
      });

      await queryClient.invalidateQueries({
        queryKey: ['newsf-get-all-invoices'],
      });

      await queryClient.invalidateQueries({
        queryKey: [queryKey.transactions.getAll],
      });
      queryClient.invalidateQueries([
        queryKey.client.getActivities,
        initialBooking?.client_id,
      ]);
    },
  });

  const handleValueChange = (details: { value: string }) => {
    setActiveTab(details.value);
  };

  console.log('errors is ', errors);
  console.log('values is ', values);

  return (
    <>
      <Heading textAlign={'center'} mb={'1.5'}>
        Add Payment
      </Heading>
      <Text textAlign={'center'}>Record a payment for this invoice</Text>
      <Tabs.Root
        border={'none'}
        defaultValue={'services'}
        lazyMount
        size={{ base: 'sm', md: 'md' }}
        value={activeTab}
        onValueChange={handleValueChange}
      >
        <Box
          style={{ position: 'sticky', zIndex: 1, top: '80px' }}
          bg={'white'}
          borderBottom={{ lg: '1px solid' }}
          borderColor={{ lg: 'gray.50' }}
        >
          <Tabs.List
            display={'flex'}
            border={'none'}
            alignItems={'center'}
            gap={'6'}
            overflowY={'hidden'}
          >
            <Tabs.Trigger
              value={'newTransaction'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Record New Transaction
            </Tabs.Trigger>

            <Tabs.Trigger
              value={'linkTransaction'}
              textTransform={'capitalize'}
              _selected={{ color: 'primary.500' }}
              _before={{ bg: 'primary.500' }}
              _hover={{ color: '#e97a5b' }}
              fontSize={'md'}
              px={'5'}
              py={'7'}
              color="black"
              minW="fit"
              fontWeight={'600'}
            >
              Link Existing Transactions
            </Tabs.Trigger>
          </Tabs.List>
        </Box>

        <Box
          pt={'3'}
          paddingRight={'2'}
          paddingLeft={'2'}
          flex={1}
          // h={{
          //   base: 'calc(100% - 7.5rem)', // Adjusted for base view
          //   md: 'calc(100% - 52.051rem)', // Adjusted for md view
          // }}
          pb={'20'}
        >
          <Tabs.Content value={'newTransaction'}>
            <Box>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  handleSubmit();
                }}
              >
                <Stack gap={'1rem'}>
                  <StringInput
                    inputProps={{
                      name: 'transaction_date',
                      type: 'date',
                      placeholder: 'Select Date',
                      onBlur: handleBlur,
                      value: values.transaction_date,
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'Date',
                      required: true,
                      invalid:
                        touched.transaction_date && !!errors.transaction_date,
                      errorText: String(errors.transaction_date),
                      onBlur: handleBlur,
                    }}
                  />
                  <NumericInput
                    inputProps={{
                      name: 'amount',
                      placeholder: 'Add Amount',
                      value: values?.amount,
                    }}
                    onValueChange={(item: any) => {
                      setFieldValue('amount', item.floatValue);
                    }}
                    fieldProps={{
                      label: 'Amount',
                      invalid: !!errors.amount,
                      errorText: String(errors.amount),
                      required: true,
                    }}
                  />
                  <CustomSelect
                    placeholder="Transaction Method"
                    required={true}
                    onChange={(val) =>
                      setFieldValue('payment_method', val?.value)
                    }
                    options={transactionMethodOptions}
                    label="Transaction Method"
                    name="payment_method"
                    defaultValue={transactionMethodOptions?.find(
                      (item) =>
                        item.value?.toLocaleLowerCase() ===
                        values.payment_method?.toLowerCase()
                    )}
                    errors={errors}
                  />
                  <CustomSelect
                    placeholder="Transaction Type"
                    required={true}
                    onChange={(val) =>
                      setFieldValue('transaction_type', val?.value)
                    }
                    options={transactionTypeOptions}
                    label="Transaction Type"
                    defaultValue={transactionTypeOptions?.find(
                      (item) =>
                        item.value?.toLocaleLowerCase() ===
                        values.transaction_type?.toLowerCase()
                    )}
                    name="transaction_type"
                    errors={errors}
                  />
                  {/* <CustomSelect
            placeholder="Status"
            required={true}
            onChange={(val) => setFieldValue('status', val?.value)}
            options={transactionStatusOptions}
            label="Status"
            defaultValue={transactionStatusOptions?.find(
              (item) =>
                item.value?.toLocaleLowerCase() ===
                values?.status?.toLowerCase()
            )}
            name="status"
            errors={errors}
          /> */}

                  <CustomTextArea
                    inputProps={{
                      name: 'note',
                      placeholder: 'Internal view only',
                      onChange: handleChange,
                    }}
                    fieldProps={{
                      label: 'Transaction Memo',
                      invalid: touched.note && !!errors.note,
                      errorText: String(errors.note),
                    }}
                  />

                  <Button
                    disabled={Object.keys(errors).length > 0}
                    w={'100%'}
                    bg={'#e97a5b'}
                    type="submit"
                    loading={CreateTransactionLoading}
                  >
                    Submit
                  </Button>
                </Stack>
              </form>
            </Box>
          </Tabs.Content>

          <Tabs.Content value={'linkTransaction'}>
            <LinkExistingTransactions
              linkTransactionHook={linkTransactionHook}
              createPaymentDisclosure={createPaymentDisclosure}
            />
          </Tabs.Content>
        </Box>
      </Tabs.Root>
    </>
  );
}
