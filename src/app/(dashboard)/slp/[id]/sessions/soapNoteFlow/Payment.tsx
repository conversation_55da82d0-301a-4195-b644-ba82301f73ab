import { useUpdateInvoiceMutation } from '@/api/invoices/update-invoice';
import { useDeleteTransactionMutation } from '@/api/transactions/delete-transaction';
import { useLinkInvoiceItemHook } from '@/app/(dashboard)/invoices/newSFInvoiceFlow/_hook/useLinkInvoiceItemHook';
import { ConsentDialog } from '@/components/elements/dialog/ConsentDialog';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { queryKey } from '@/constants/query-key';
import { TCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import { determineInvoiceStatus } from '@/reuseables/invoice/helpers';
import {
  Box,
  // Center,
  Flex,
  MenuContent,
  MenuItem,
  MenuRoot,
  MenuSeparator,
  MenuTrigger,
  // Icon,
  Separator,
  Text,
  chakra,
  useDisclosure,
} from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import moment from 'moment';
import { Fragment, useState } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { IoWalletOutline } from 'react-icons/io5';
import EditPaymentForm from './EditPaymentForm';
import PaymentForm from './PaymentForm';

export default function Payment({
  //   soapNoteHook,
  // section,
  // abbr,
  initialBooking,
}: {
  soapNoteHook: TCreateInvoiceHook;
  //   section: 'slp' | 'client';
  abbr?: any;
  initialBooking: any;
}) {
  //   console.log('initialBooking is ', initialBooking);
  const paymentDisclosure = useDisclosure();
  const EditPaymentDisclosure = useDisclosure();
  const queryClient = useQueryClient();
  const { transactions, total_price } =
    initialBooking?.slp_notes?.invoice ?? {};

  const linkTransactionHook = useLinkInvoiceItemHook({
    invoice_id: initialBooking?.slp_notes?.invoice?.id,
  });

  // const isPackage = Boolean(package_used?.[0]);
  const totalPaid = transactions?.reduce(
    (prev: number, currentTransaction: any) => {
      return prev + Number(currentTransaction?.amount || 0);
    },
    0
  );
  const totalPrice = Number(total_price);
  const amountDue = totalPaid > totalPrice ? 0 : totalPrice - Number(totalPaid);

  const {
    mutateAsync: DeleteTransaction,
    isLoading: DeleteTransactionLoading,
  } = useDeleteTransactionMutation();
  const deleteTransactionDisclosure = useDisclosure();
  const [selectedTransaction, setSelectedTransaction] = useState(null) as any;
  const { mutateAsync: updateInvoices } = useUpdateInvoiceMutation();

  // delete transaction
  const handleRemoveTransaction = async () => {
    if (!selectedTransaction?.id) {
      return;
    }
    await DeleteTransaction(selectedTransaction?.id);
    const statusData = {
      transactions: [
        ...initialBooking.slp_notes.invoice.transactions.filter(
          (transaction: any) => transaction.id !== selectedTransaction.id
        ),
      ],
      total_price: initialBooking.slp_notes.invoice.total_price,
      tax_value: 0,
      discount: initialBooking.slp_notes.invoice.discount,
    };
    const invoiceStatus = determineInvoiceStatus(statusData);
    await updateInvoices({
      data: { status: invoiceStatus },
      id: initialBooking?.slp_notes?.invoice?.id,
    });
    deleteTransactionDisclosure.onClose();
    setSelectedTransaction(null);
    await queryClient.invalidateQueries({
      queryKey: [queryKey.bookings.getById, Number(initialBooking?.id)],
    });
    await queryClient.invalidateQueries({
      queryKey: [queryKey.transactions.getAll],
    });
    await queryClient.invalidateQueries([
      queryKey.client.getActivities,
      initialBooking?.client_id,
    ]);
  };

  return (
    <Box
      border={'1px solid #FEFEFE'}
      boxShadow={'lg'}
      rounded={'12px'}
      py={'1.5rem'}
      px={'10px'}
      w={'full'}
      minH={'6rem'}
      position={'relative'}
      maxH={'fit-content'}
    >
      <Box display={'flex'} width={'full'} gap={'1.25rem'} overflow={'hidden'}>
        <Box
          rounded={'full'}
          fontSize={'18px'}
          display={'flex'}
          justifyContent={'center'}
          alignItems={'center'}
          minW={'36px'}
          w={'36px'}
          h={'36px'}
          maxW={'36px'}
          cursor={'pointer'}
          color={'#E97A5B'}
          border={'2px solid #E97A5B'}
        >
          <IoWalletOutline />
        </Box>

        <Box overflow={'hidden'} width={'full'} maxWidth={'full'}>
          <Text color={'GrayText'}>Manage Payments</Text>
          {initialBooking?.slp_notes?.invoice?.id ? (
            <Box>
              <Flex gap={'.5rem'} mt={'1rem'} alignItems={'center'}>
                <Text>
                  <b>Amount Due:</b> {formatMoney(amountDue)}
                </Text>
                {/* {!initialTransaction && ( */}
                {amountDue > 0 && (
                  <Flex gap={'.5rem'} alignItems={'center'}>
                    <Box w={'1rem'} h={'1px'} bg={'black'}></Box>

                    <Text>
                      <chakra.span
                        cursor={'pointer'}
                        color={'primary.500'}
                        fontWeight={500}
                        onClick={paymentDisclosure.onOpen}
                      >
                        Record a payment
                      </chakra.span>{' '}
                      manually
                    </Text>
                  </Flex>
                )}
                {amountDue === 0 && transactions && transactions?.length && (
                  <Text ml={'auto'}>
                    {' '}
                    <b>Status:</b> Your invoice is paid in full
                  </Text>
                )}
              </Flex>
              {transactions && transactions?.length
                ? transactions?.map((transaction: any) => (
                    <Fragment key={transaction.id}>
                      <Box>
                        <Separator my={'1rem'} />
                        <Flex>
                          <Box w={'full'}>
                            <Flex>
                              <Text>
                                {/* <b> Date</b>:{' '} */}
                                {moment(transaction?.transaction_date).format(
                                  'MMMM D, YYYY'
                                )}
                                :{' '}
                                <b>
                                  {/* {transaction?.currency_code === 'USD' && '$'}
                                  {transaction?.amount} */}
                                  {formatMoney(transaction?.amount)}
                                </b>{' '}
                                {transaction.transaction_type
                                  ?.toLowerCase()
                                  .replace(/_/g, ' ')}{' '}
                                received via{' '}
                                <b>
                                  {transaction?.payment_method
                                    ?.toLowerCase()
                                    .replace(/_/g, ' ')}
                                </b>
                              </Text>
                            </Flex>
                          </Box>
                          <Box w={'fit-content'} ml={'auto'}>
                            <MenuRoot>
                              <MenuTrigger cursor={'pointer'}>
                                <BsThreeDotsVertical />
                              </MenuTrigger>
                              <MenuContent
                                cursor={'pointer'}
                                position={'absolute'}
                                zIndex={10}
                                right={0}
                              >
                                {/* <MenuItem value="send">
                                  <Button
                                    p={'0'}
                                    h={'1rem'}
                                    fontWeight={600}
                                    color={'primary.500'}
                                    bg={'transparent !important'}
                                    outline={'none !important'}
                                  >
                                    Send a receipt
                                  </Button>
                                </MenuItem> */}
                                {/* <MenuSeparator /> */}
                                <MenuItem value="edit">
                                  <Button
                                    p={'0'}
                                    h={'1rem'}
                                    fontWeight={600}
                                    color={'primary.500'}
                                    bg={'transparent !important'}
                                    outline={'none !important'}
                                    justifyContent={'start'}
                                    w={'full'}
                                    onClick={() => {
                                      setSelectedTransaction(transaction);
                                      EditPaymentDisclosure.onOpen();
                                    }}
                                  >
                                    Edit payment
                                  </Button>
                                </MenuItem>
                                <MenuSeparator />
                                <MenuItem value="delete">
                                  <Button
                                    p={'0'}
                                    h={'1rem'}
                                    fontWeight={600}
                                    color={'primary.500'}
                                    bg={'transparent !important'}
                                    outline={'none !important'}
                                    justifyContent={'start'}
                                    w={'full'}
                                    onClick={() => {
                                      setSelectedTransaction(transaction);
                                      deleteTransactionDisclosure.onOpen();
                                    }}
                                  >
                                    Remove Payment
                                  </Button>
                                </MenuItem>
                              </MenuContent>
                            </MenuRoot>
                          </Box>
                        </Flex>
                      </Box>
                    </Fragment>
                  ))
                : null}
            </Box>
          ) : (
            <Text mt={'0.5rem'}>Please create an invoice</Text>
          )}
        </Box>
      </Box>

      {/* payment form */}
      <CustomModal
        w={{ base: '90%', md: '50%' }}
        open={paymentDisclosure.open}
        onOpenChange={paymentDisclosure.onClose}
      >
        <PaymentForm
          initialBooking={initialBooking}
          createPaymentDisclosure={paymentDisclosure}
          amountDue={amountDue}
          linkTransactionHook={linkTransactionHook}
        />
      </CustomModal>

      {/* Edit payment form */}
      <CustomModal
        w={{ base: '90%', md: '50%' }}
        open={EditPaymentDisclosure.open}
        onOpenChange={EditPaymentDisclosure.onClose}
      >
        <EditPaymentForm
          initialBooking={initialBooking}
          selectedTransaction={selectedTransaction}
          editPaymentDisclosure={EditPaymentDisclosure}
          amountDue={amountDue}
        />
      </CustomModal>

      <ConsentDialog
        open={deleteTransactionDisclosure.open}
        onOpenChange={deleteTransactionDisclosure.onClose}
        handleSubmit={handleRemoveTransaction}
        note={`This action will remove this transaction from this invoice.`}
        isLoading={DeleteTransactionLoading}
        heading={'Are you sure?'}
        // firstBtnText="Back"
        // secondBtnText="Yes, Disconnect"
      />
    </Box>
  );
}
