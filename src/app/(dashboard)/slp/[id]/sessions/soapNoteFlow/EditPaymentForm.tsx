import { useUpdateInvoiceMutation } from '@/api/invoices/update-invoice';
import { useUpdateTransactionMutation } from '@/api/transactions/edit-transaction';
import CustomSelect from '@/components/Input/CustomSelect';
import CustomTextArea from '@/components/Input/CustomTextArea';
import NumericInput from '@/components/Input/NumericInput';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { toaster } from '@/components/ui/toaster';
import { queryKey } from '@/constants/query-key';

import {
  transactionMethodOptions,
  // transactionStatusOptions,
  transactionTypeOptions,
} from '@/data/options';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { determineInvoiceStatus } from '@/reuseables/invoice/helpers';
import { Heading, Stack, Text } from '@chakra-ui/react';
import { useQueryClient } from '@tanstack/react-query';
import { useFormik } from 'formik';
import moment from 'moment';
import * as yup from 'yup';

export const initialTransaction = {};
const transactionMethodValues = [
  'CASH',
  'E_TRANSFER',
  'BANK_TRANSFER',
  'CREDIT_CARD',
  'PAYPAL',
  'OTHERS',
];
const transactionTypeValues = ['PAYMENT', 'REFUND', 'CHARGE_BACK', 'DEPOSIT'];
// const transactionStatusValues = [
//   'PAID',
//   'PENDING',
//   'PARTIALLY_PAID',
//   'AWAITING_PAYMENT',
//   'COMPLETED',
//   'FAILED',
//   'REFUNDED',
// ];

export const transactionSchema = yup.object().shape({
  amount: yup
    .number()
    .typeError('Amount must be a number')
    .min(1, 'Amount must be greater than 0')
    .required('Amount is required'),

  transaction_type: yup
    .string()
    .oneOf(transactionTypeValues, 'Invalid transaction type')
    .required('Transaction type is required'),

  // status: yup
  //   .string()
  //   .oneOf(transactionStatusValues, 'Invalid status')
  //   .required('Status is required'),

  currency_code: yup
    .string()
    .length(3, 'Currency code must be 3 characters')
    .required('Currency code is required'),

  payment_method: yup
    .string()
    .oneOf(transactionMethodValues, 'Invalid payment method')
    .required('Payment method is required'),

  note: yup.string().max(255, 'Note cannot exceed 255 characters').optional(),

  transaction_date: yup
    .string()
    .test('valid-date', 'Invalid date format', (value) =>
      moment(value, 'YYYY-MM-DD', true).isValid()
    )
    .required('Transaction date is required'),
});

export default function EditPaymentForm({
  selectedTransaction,
  editPaymentDisclosure,
  // amountDue,
  initialBooking,
  // invalidateQueryKey = queryKey.bookings.getById,
}: any) {
  const queryClient = useQueryClient();
  const {
    mutateAsync: UpdateTransaction,
    isLoading: UpdateTransactionLoading,
  } = useUpdateTransactionMutation();
  const { UserFromQuery } = useSupabaseSession();
  const { mutateAsync: updateInvoices } = useUpdateInvoiceMutation();
  const {
    values,
    handleBlur,
    handleChange,
    setFieldValue,
    errors,
    touched,
    handleSubmit,
  } = useFormik({
    initialValues: {
      amount: selectedTransaction?.amount,
      transaction_type: selectedTransaction?.transaction_type,
      // status: selectedTransaction?.status,
      currency_code: selectedTransaction?.currency_code,
      payment_method: selectedTransaction?.payment_method,
      note: selectedTransaction?.note,
      transaction_date: moment(selectedTransaction?.transaction_date).format(
        'YYYY-MM-DD'
      ),
    },
    validationSchema: transactionSchema,
    onSubmit: async (values) => {
      console.log('values is ', values);
      console.log('UserFromQuery is ', UserFromQuery);
      if (!UserFromQuery?.organization_id || !UserFromQuery?.id) {
        toaster.create({ description: 'Invalid Session!', type: 'error' });
        return;
      }
      if (!initialBooking?.slp_notes?.invoice?.id) {
        toaster.create({
          description: 'Please create invoice for this session!',
          type: 'error',
        });
        return;
      }
      const payload = {
        ...values,
        organization_id: UserFromQuery?.organization_id,
        user_id: UserFromQuery?.id,
        invoice_id: initialBooking?.slp_notes?.invoice?.id,
      };
      console.log('payload is ', payload);
      //   return;

      await UpdateTransaction({
        payload,
        id: selectedTransaction.id,
      });
      const statusData = {
        transactions: [
          ...initialBooking.slp_notes.invoice.transactions.map(
            (transaction: any) => {
              if (transaction.id == selectedTransaction.id) {
                return {
                  ...transaction,
                  ...payload,
                };
              }
              return transaction;
            }
          ),
        ],
        total_price: initialBooking.slp_notes.invoice.total_price,
        tax_value: 0,
        discount: initialBooking.slp_notes.invoice.discount,
      };
      const invoiceStatus = determineInvoiceStatus(statusData);
      await updateInvoices({
        data: { status: invoiceStatus },
        id: initialBooking?.slp_notes?.invoice?.id,
      });
      await editPaymentDisclosure.onClose();

      await queryClient.invalidateQueries({
        queryKey: [queryKey.bookings.getById],
        exact: false,
        refetchType: 'all',
      });
      await queryClient.invalidateQueries({
        queryKey: ['newsf-get-invoice-by-id'],
        exact: false,
        refetchType: 'all',
      });
      await queryClient.invalidateQueries({
        queryKey: [queryKey.transactions.getAll],
      });
      queryClient.invalidateQueries([
        queryKey.client.getActivities,
        initialBooking?.client_id,
      ]);
      await queryClient.invalidateQueries({
        queryKey: ['newsf-get-all-invoices'],
      });
    },
  });

  // console.log('errors is ', errors);
  // console.log('values is ', values);

  return (
    <div>
      <Heading textAlign={'center'} mb={'1.5'}>
        Edit Payment Details
      </Heading>
      <Text textAlign={'center'}>
        Update the payment information for this invoice
      </Text>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
      >
        <Stack gap={'1rem'}>
          <StringInput
            inputProps={{
              name: 'transaction_date',
              type: 'date',
              placeholder: 'Select Date',
              onBlur: handleBlur,
              value: values.transaction_date,
              onChange: handleChange,
            }}
            fieldProps={{
              label: 'Date',
              required: true,
              invalid: touched.transaction_date && !!errors.transaction_date,
              errorText: String(errors.transaction_date),
              onBlur: handleBlur,
            }}
          />
          <NumericInput
            inputProps={{
              name: 'amount',
              placeholder: 'Add Amount',
              value: values?.amount,
            }}
            onValueChange={(item: any) => {
              setFieldValue('amount', item.floatValue);
            }}
            fieldProps={{
              label: 'Amount',
              invalid: !!errors.amount,
              errorText: String(errors.amount),
              required: true,
            }}
          />
          <CustomSelect
            placeholder="Transaction Method"
            required={true}
            onChange={(val) => setFieldValue('payment_method', val?.value)}
            options={transactionMethodOptions}
            label="Transaction Method"
            name="payment_method"
            defaultValue={transactionMethodOptions?.find(
              (item) =>
                item.value?.toLocaleLowerCase() ===
                values.payment_method?.toLowerCase()
            )}
            errors={errors}
          />
          <CustomSelect
            placeholder="Transaction Type"
            required={true}
            onChange={(val) => setFieldValue('transaction_type', val?.value)}
            options={transactionTypeOptions}
            label="Transaction Type"
            defaultValue={transactionTypeOptions?.find(
              (item) =>
                item.value?.toLocaleLowerCase() ===
                values.transaction_type?.toLowerCase()
            )}
            name="transaction_type"
            errors={errors}
          />
          {/* <CustomSelect
            placeholder="Status"
            required={true}
            onChange={(val) => setFieldValue('status', val?.value)}
            options={transactionStatusOptions}
            label="Status"
            defaultValue={transactionStatusOptions?.find(
              (item) =>
                item.value?.toLocaleLowerCase() ===
                values?.status?.toLowerCase()
            )}
            name="status"
            errors={errors}
          /> */}

          <CustomTextArea
            inputProps={{
              name: 'note',
              placeholder: 'Note',
              value: values.note,
              onChange: handleChange,
            }}
            fieldProps={{
              label: 'Note',
              invalid: touched.note && !!errors.note,
              errorText: String(errors.note),
            }}
          />

          <Button
            disabled={Object.keys(errors).length > 0}
            w={'100%'}
            type="submit"
            bg={'#e97a5b'}
            loading={UpdateTransactionLoading}
          >
            Update
          </Button>
        </Stack>
      </form>
    </div>
  );
}
