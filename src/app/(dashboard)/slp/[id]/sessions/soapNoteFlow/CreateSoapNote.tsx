import { useTemplateHookReturnType } from '@/app/(dashboard)/admin/template/AddTemplate';
//import TextEditor from '@/components/Input/CustomEditor';
import CustomSelect from '@/components/Input/CustomSelect';
import TextEditorNew from '@/components/Input/NewTextEditor';
import { Button } from '@/components/ui/button';
import { useSlpNoteHook } from '@/hooks/slp/soapNoteFlow/useSlpNoteHookFlow';
import { TCreateInvoiceHook } from '@/hooks/slp/useCreateInvoiceHook';
import { Box, Flex, Separator, Stack, Text } from '@chakra-ui/react';
import { FiChevronDown, FiChevronUp, FiEdit } from 'react-icons/fi';
// import CustomDatePicker from '@/components/elements/date-picker/date-picker';
// import truncate from 'html-truncate';

type CreateSoapNoteProps = {
  soapNoteHook: TCreateInvoiceHook;
  templateHook: useTemplateHookReturnType;
  booking: any;
};
// const truncateHtml = (html: any, maxLength: any) => {
//   const div = document.createElement('div');
//   div.innerHTML = html;
//   const text = div.innerText;
//   return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
// };

export default function CreateSoapNote({
  soapNoteHook,
  templateHook,
  booking,
}: CreateSoapNoteProps) {
  // console.log('booking', soapNoteHook?.booking);

  const {
    toggleForm,
    isOpen,
    values,
    handleFormSubmit,
    loading,
    setFieldValue,
  } = useSlpNoteHook({
    soapNoteHook: soapNoteHook,
    booking,
  });
  const replaceTags = (content: string, cb: (arg0: string) => void) => {
    const data = {
      clients: {
        first_name: soapNoteHook?.booking?.linked_clients?.first_name,
      },
      invoice_date: soapNoteHook?.values?.invoice_date,
    };
    const result = templateHook?.replaceTags(content, data);
    cb(result || '');
  };

  // const timezone = soapNoteHook?.CurrentClient?.active_slp?.timezone;
  return (
    <Box
      py={'1.5rem'}
      border={'1px solid #FEFEFE'}
      boxShadow={'lg'}
      rounded={'12px'}
      px={'10px'}
      w={'full'}
      minH={'6rem'}
      maxH={'fit-content'}
      scrollbar={'hidden'}
      overflow={'hidden'}
    >
      <Box
        display={'grid'}
        gridTemplateColumns={'1fr auto'}
        width={'full'}
        maxWidth={'full'}
        gap={'1.5rem'}
        overflow={'hidden'}
        justifyContent={'space-between'}
        pb={'0.5rem'}
      >
        <Box
          display={'flex'}
          width={'full'}
          gap={'1.25rem'}
          overflow={'hidden'}
          // truncate={true}
        >
          <Box
            rounded={'full'}
            fontSize={'18px'}
            display={'flex'}
            justifyContent={'center'}
            alignItems={'center'}
            minW={'36px'}
            w={'36px'}
            h={'36px'}
            maxW={'36px'}
            cursor={'pointer'}
            color={'#E97A5B'}
            border={'2px solid #E97A5B'}
          >
            {' '}
            <FiEdit />
          </Box>
          <Box
            // truncate={true}
            // whiteSpace={'nowrap'}
            overflow={'hidden'}
            width={'full'}
            maxWidth={'full'}
          >
            <Text color={'GrayText'}>Session Note</Text>
            <Text fontStyle={'italic'} fontWeight={'semibold'} fontSize={'sm'}>
              Internal notes, not visible to the client.
            </Text>
          </Box>
        </Box>
        <Button
          rounded={'2xl'}
          fontSize={'14px'}
          bg={'transparent'}
          color={'#E97A5B'}
          width={'fit-content'}
          maxWidth={'fit-content'}
          border={'2px solid #E97A5B'}
          onClick={toggleForm}
        >
          {booking?.slp_notes?.id ? 'Edit Note' : 'Create Note'}
          {isOpen ? (
            <FiChevronUp color="#E97A5B" />
          ) : (
            <FiChevronDown color="#E97A5B" />
          )}
        </Button>
      </Box>

      <Box mt={'0.5rem'} pl={'3.5rem'}>
        {booking?.slp_notes?.id && !isOpen ? (
          <Separator my={'1rem'} orientation="horizontal" />
        ) : null}
        {!isOpen ? (
          <Text
            // truncate={true}
            // whiteSpace={'nowrap'}
            // overflow="hidden"
            width={'full'}
            maxWidth={'full'}
            dangerouslySetInnerHTML={{
              __html: booking?.slp_notes?.soap_note,
              // __html: truncate(booking?.slp_notes?.soap_note, 40),
            }}
          ></Text>
        ) : null}
      </Box>
      <Box
        maxH={isOpen ? 'fit-content' : 0}
        w={'full'}
        overflow={isOpen ? '' : 'hidden'}
      >
        <form onSubmit={handleFormSubmit}>
          {' '}
          <Stack pl={'.5rem'} gap={'0.5rem'} mt={1}>
            <Box position={'relative'} gap={' 1.5rem'} my={'1rem'}>
              {/* <Box
                display={'flex'}
                alignItems={'center'}
                width={'100%'}
                justifyContent={'space-between'}
              >
                <CustomDatePicker
                  onChange={(e) => {
                    setFieldValue('note_date', e);
                  }}
                  defaultDate={values.note_date}
                  showTime={true}
                  timeZone={timezone}
                  isDisabled={loading}
                  placement="right-end"
                />
              </Box> */}

              {soapNoteHook?.linkedClientOptions?.length > 1 && (
                <Box>
                  <CustomSelect
                    placeholder="Select one"
                    options={soapNoteHook?.linkedClientOptions}
                    onChange={(val) => {
                      setFieldValue('client_id', val.value);
                    }}
                    label="Linked Clients"
                    defaultValue={soapNoteHook?.linkedClientOptions?.find(
                      (item: any) =>
                        Number(item?.value) === Number(values?.client_id)
                    )}
                    value={values?.client_id}
                  />
                </Box>
              )}
              <Box>
                <TextEditorNew
                  header={''}
                  templates={templateHook?.data}
                  replaceTags={replaceTags}
                  showIPAPicker={
                    soapNoteHook?.Slp?.organization?.id === 1 ||
                    soapNoteHook?.Slp?.office_title == 'Speech_Therapist'
                      ? true
                      : false
                  }
                  saveContent={(e: any) => {
                    setFieldValue('soap_note', e);
                  }}
                  initialContent={
                    values?.soap_note || booking?.slp_notes?.soap_note
                  }
                  initialPresent={!!booking?.slp_notes?.soap_note}
                />
              </Box>
            </Box>
            <Flex justifyContent={'right'}>
              <Button
                loading={loading}
                disabled={loading}
                bg="primary.500"
                //   onClick={(e) => handleFormSubmit(e)}
                type="submit"
                minW="10rem"
                rounded={'2xl'}
              >
                {booking?.slp_notes?.id ? 'Save Edits' : 'Submit'}
              </Button>
            </Flex>
          </Stack>
        </form>
      </Box>
    </Box>
  );
}
