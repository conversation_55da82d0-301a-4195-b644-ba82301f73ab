// import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTable from '@/components/table/CustomTable';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { useSlpClientsHook } from '@/hooks/slp/useSlpClientsHook';
import { IUser } from '@/shared/interface/user';

import { Box, Center, Flex, Heading, Stack, Text } from '@chakra-ui/react';
import { columnDef } from './columnDef';
import Filter from './Filter';
import Statistics from './Statistics';
// import { LuPlus } from 'react-icons/lu';
import CreateClientModal from '@/app/(dashboard)/contacts/[id]/CreateClientModal';
import ClientsWithOutInvoices from './ClientsWithOutInvoices';

export default function Clients({ slp }: { slp: IUser }) {
  // const [filter, setFilter] = useRecoilState(AllClientsFilterState);
  const slpClientHook = useSlpClientsHook({
    slp,
  });

  // console.log('data00033', slpClientHook.data);
  // console.log('slpClientHook', slpClientHook.show);

  // console.log('slp is ', slp);

  // console.log('slpC', slpClientHook);

  // console.log('ln 22', slpClientHook.data?.slice(0, 5));

  const processedData =
    slpClientHook?.data?.map((item: any) => ({
      ...item,
      name: `${item.first_name} ${item.last_name}`,
    })) || [];

  return (
    <Box position={'relative'} pb={'20'} pt={{ base: '3', lg: '5' }}>
      <Box>
        <Flex
          justifyContent={'space-between'}
          flexDirection={{ base: 'column', md: 'row' }}
          alignItems={{ md: 'center' }}
          gap={'3'}
        >
          <Heading
            fontSize={{ base: '1.3rem', md: '2rem' }}
            fontWeight={'semibold'}
          >
            {slp?.first_name} {slp?.last_name} Clients
          </Heading>

          <CreateClientModal id={slp?.id} />
        </Flex>

        <Stack mt={'4'} bg={'primary.500'} p={'.5'} pl={'1.5'}>
          <Box bg={'primary.50'} px={{ base: '3', md: '5' }} py={'3.5'}>
            <Text fontSize={{ base: 'xs', md: '0.9rem' }} fontWeight={'medium'}>
              <b>Note: </b>Clients information (invoices, data, etc) are grouped
              together by email, eg spouses, parent/child
            </Text>
          </Box>
        </Stack>

        <Box mt={'7'}>
          <Statistics stats={slpClientHook?.stats} />
        </Box>
        <Box
          display={'flex'}
          width={'full'}
          flexDirection={{ base: 'column', md: 'row' }}
          justifyContent={'space-between'}
          alignItems={'start'}
          mt={'7'}
          gap={'3'}
        >
          <Checkbox
            checked={slpClientHook.filter?.non_active_client}
            onChange={() =>
              slpClientHook.setFilter({
                ...slpClientHook?.filter,
                non_active_client: !slpClientHook.filter.non_active_client,
              })
            }
            gap={'.7rem'}
            colorScheme="primary"
            alignItems={'flex-start'}
          >
            <Text fontWeight={'semibold'}> Non Active Clients</Text>
            <Text color={'gray.200'}>
              Only show clients whose last session was over 6 months ago
            </Text>
          </Checkbox>

          <Switch
            checked={slpClientHook?.show}
            onCheckedChange={slpClientHook?.toggleSwitch}
            colorPalette={'orange'}
          >
            {slpClientHook?.show
              ? 'Show  all clients'
              : 'Show  invoiced clients'}
          </Switch>
        </Box>
        <Flex
          alignItems={{ md: 'center' }}
          flexDirection={{ base: 'column', md: 'row' }}
          justifyContent={'space-between'}
          mt={'10'}
          gap={'2'}
        >
          <Flex alignItems={'center'} gap={'1rem'}>
            <Button
              disabled={slpClientHook?.data?.length === 0}
              onClick={slpClientHook?.handleExportCSV}
              minW={'10rem'}
            >
              Export as CSV
            </Button>

            <Box position={'relative'}>
              <Text
                onClick={slpClientHook?.handleCopyLoginLink}
                fontSize={'.85rem'}
                fontWeight={600}
                cursor={'pointer'}
              >
                Client Portal Link
              </Text>
              {slpClientHook?.isCopied && (
                <Center
                  bg={'black'}
                  w={'5rem'}
                  h={'2rem'}
                  rounded={'.4rem'}
                  pos={'absolute'}
                  left={'-3rem'}
                  top={'-3rem'}
                  fontSize={'.85rem'}
                  color={'white'}
                  opacity={'.8'}
                >
                  Copied
                </Center>
              )}
            </Box>
          </Flex>
          <Box>
            <Filter slpClientHook={slpClientHook} />
          </Box>
        </Flex>

        {/* ===========TABLE=========== */}
        {!slpClientHook.show ? (
          <Box minH={'20rem'} mt={'2rem'}>
            <CustomTable
              loading={slpClientHook?.isLoading}
              initialSorting={slpClientHook?.sorting}
              columnDef={columnDef}
              data={processedData}
              filter={{
                tableName: 'Followup',
              }}
              total={slpClientHook?.total_count}
              pagination={{
                row: Number(slpClientHook?.filter?.items_per_page),
                page: Number(slpClientHook?.filter?.page_number),
              }}
              setPagination={{
                onPageChange: (e) => {
                  slpClientHook.setFilter({
                    ...slpClientHook.filter,
                    page_number: e,
                  });
                },
                onRowChange: (e) => {
                  slpClientHook.setFilter({
                    ...slpClientHook.filter,
                    items_per_page: e,
                  });
                },
              }}
              enableSorting={true}
              isSearchable={true}
              searchColumn="name"
            />
          </Box>
        ) : (
          <ClientsWithOutInvoices slpClientHook={slpClientHook} />
        )}
      </Box>
    </Box>
  );
}
