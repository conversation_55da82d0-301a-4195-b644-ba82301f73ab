'use client';

import { formatMoney } from '@/components/elements/format-money/FormatMoney';
import { Box, Flex, Text, chakra } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import moment from 'moment/moment';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import Status from './Status';

export interface SlpClientDisplay {
  first_name: string;
  last_name: string;
  province: string;
  email: string;
  organization_id: number;
  id: number; // Changed from client_id to match your usage
  packages: any;
  memo: string;
  total_sessions: number;
  total_spent: number;
  total_hours: number;
  ax_count: number;
  tx_count: number;
  last_session_date: string;
  slp_notes: string;
}

const columnHelper = createColumnHelper<SlpClientDisplay>();

// Client component for the Name cell
const NameCell = ({ row }: { row: any }) => {
  const searchParams = useSearchParams();
  const organizationId = searchParams.get('organization_id');

  const href = organizationId
    ? `/contacts/${row.original?.id}?organization_id=${organizationId}`
    : `/contacts/${row.original?.id}`;

  return (
    <Link href={href}>
      <Box fontWeight={500}>
        {`${row.original?.first_name} ${row.original?.last_name}`}
      </Box>
    </Link>
  );
};

export const columnDef = [
  {
    accessorFn: (row: SlpClientDisplay) =>
      `${row?.first_name} ${row?.last_name}`,
    cell: (props: any) => <NameCell row={props.row} />,
    header: 'Name',
    id: 'name',
    sortingFn: (rowA: any, rowB: any) => {
      const nameA =
        `${rowA.original.first_name} ${rowA.original.last_name}`.toLowerCase();
      const nameB =
        `${rowB.original.first_name} ${rowB.original.last_name}`.toLowerCase();
      return nameA.localeCompare(nameB);
    },
  },
  columnHelper.accessor('total_sessions', {
    cell: (info) => <Box minW={'8rem'}>{info.getValue() || '-'}</Box>,
    header: 'Sessions',
    id: 'sessions',
  }),
  columnHelper.accessor('last_session_date', {
    cell: (info) => (
      <Box minW={'8rem'}>
        {info.getValue() ? moment(info.getValue()).format('MMM D, YYYY') : '-'}
      </Box>
    ),
    header: 'Last Session',
    id: 'last-sessions',
    sortingFn: 'datetime',
  }),
  columnHelper.accessor('memo', {
    cell: (info) => (
      <Box minW={'8rem'}>
        {info.getValue()?.length <= 6 ? info.getValue() : '-'}
      </Box>
    ),
    header: 'Memo',
    id: 'last-memo',
  }),
  columnHelper.accessor('total_spent', {
    cell: (info) => <Box minW={'8rem'}>{formatMoney(info.getValue())}</Box>,
    header: 'Total Value',
    id: 'total-value',
  }),
  columnHelper.accessor('packages', {
    cell: (info) => {
      const PackageBalanceComp = ({ data }: any) => {
        let totalQuantityAllPackages = 0;
        let totalRemainingAllPackages = 0;

        data?.bookings?.forEach((booking: any) => {
          const slpNote = booking?.slp_notes;
          const purchasedPackageItems =
            slpNote?.invoice?.purchased_package?.purchased_package_items;

          if (purchasedPackageItems && purchasedPackageItems.length > 0) {
            purchasedPackageItems.forEach((item: any) => {
              totalQuantityAllPackages += item.quantity;
              totalRemainingAllPackages += item.remaining;
            });
          }
        });

        const totalUsedAllPackages =
          totalQuantityAllPackages - totalRemainingAllPackages;

        if (totalQuantityAllPackages === 0) {
          return (
            <Flex justifyContent={'center'} w={'100%'}>
              <Text>-</Text>
            </Flex>
          );
        }

        if (totalUsedAllPackages === totalQuantityAllPackages) {
          return (
            <Flex justifyContent={'center'} w={'100%'}>
              <Text>-</Text>
            </Flex>
          );
        }

        return (
          <Flex
            justifyContent={'center'}
            fontSize={'11px'}
            alignItems={'center'}
            gap={1}
          >
            <Text fontSize={'11px'}>Active Package : </Text>
            <chakra.span color="primary.500" fontWeight="bold">
              {totalUsedAllPackages}
            </chakra.span>
            /
            <chakra.span fontWeight="bold">
              {totalQuantityAllPackages}
            </chakra.span>
          </Flex>
        );
      };

      const SpeakFluentPackageComp = ({ data }: { data: any }) => {
        const inCompletePackages =
          data.packages
            ?.filter(
              (item: any) =>
                item?.status?.toLowerCase() === 'incomplete' ||
                item?.status?.toLowerCase() === 'active'
            )
            ?.sort(
              (a: any, b: any) =>
                new Date(a.created_at).getTime() -
                new Date(b.created_at).getTime()
            ) || [];

        const currentIncompletePackage: any = inCompletePackages[0];
        const packageSize = currentIncompletePackage?.package_size || 0;
        const balance = currentIncompletePackage?.balance || 0;
        const used = Math.max(0, packageSize - balance);

        if (inCompletePackages.length === 0) {
          return (
            <Flex justifyContent={'center'} w={'100%'}>
              <Text>-</Text>
            </Flex>
          );
        } else {
          return (
            <Flex
              justifyContent={'center'}
              fontSize={'11px'}
              alignItems={'center'}
              gap={1}
            >
              <Text fontSize={'11px'}>Active Package : </Text>
              <chakra.span color="primary.500" fontWeight="bold">
                {used}
              </chakra.span>
              /<chakra.span fontWeight="bold">{packageSize}</chakra.span>
            </Flex>
          );
        }
      };

      return (
        <Box>
          {info.row.original?.organization_id !== 1 && (
            <PackageBalanceComp data={info.row.original} />
          )}
          {info.row.original?.organization_id === 1 && (
            <SpeakFluentPackageComp data={info.row.original} />
          )}
        </Box>
      );
    },
    header: 'Active Package',
    id: 'activePackage',
    enableSorting: true,
  }),
  columnHelper.display({
    id: 'status',
    cell: (props) => <Status {...props.row?.original} />,
    header: 'Status',
  }),
];
