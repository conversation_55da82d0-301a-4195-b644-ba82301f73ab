import CustomSelect from '@/components/Input/CustomSelect';
import StringInput from '@/components/Input/StringInput';
import { Button } from '@/components/ui/button';
import { useSlpClientsHook } from '@/hooks/slp/useSlpClientsHook';
import {
  Box,
  Flex,
  HStack,
  Stack,
  Text,
  useDisclosure,
} from '@chakra-ui/react';
import React, { useState } from 'react';
import {
  PopoverRoot,
  PopoverArrow,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { IoIosArrowDown } from 'react-icons/io';
import { useRouter, useSearchParams } from 'next/navigation';

export type SlpClientHookReturnType = ReturnType<typeof useSlpClientsHook>;

export default function Filter({
  slpClientHook,
}: {
  slpClientHook: SlpClientHookReturnType;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { onClose, open, onToggle } = useDisclosure();
  const sessionOptions = [
    {
      label: 'Before',
      value: 'before',
    },
    {
      label: 'After',
      value: 'after',
    },
  ];
  const [filterType, setFilterType] = useState(
    searchParams.get('filter_source') ||
      slpClientHook.filter.date_filter_status ||
      'before'
  ); // Filter type: 'before' or 'after'
  const [selectedDateTime, setSelectedDateTime] = useState(
    searchParams.get('filter_date') || slpClientHook.filter.filter_date || ''
  );

  const applyFilter = () => {
    if (selectedDateTime) {
      slpClientHook.setFilter({
        ...slpClientHook.filter,
        date_filter_status: filterType,
        filter_date: selectedDateTime,
        page_number: 1,
      });
    }
    router.push(
      `?tab=clients&filter_source=${filterType}&filter_date=${selectedDateTime}`
    );
    onClose();
  };

  const clearFilter = () => {
    slpClientHook.setFilter({
      ...slpClientHook.filter,
      date_filter_status: '',
      filter_date: '',
      page_number: 1,
    });
    router.push(`?tab=clients`);
    onClose();
    setSelectedDateTime('');
    setFilterType('before');
  };

  return (
    <HStack gap={'4'}>
      <PopoverRoot>
        <PopoverTrigger asChild>
          <Button size="sm" variant="ghost" _hover={{ bg: 'transparent' }}>
            Groups
            <IoIosArrowDown color="gray" />
          </Button>
        </PopoverTrigger>
        <PopoverContent>
          <PopoverArrow />
          <PopoverBody>
            <form className="space-y-4">
              <Text
                fontSize="sm"
                color="red"
                _hover={{ color: 'black', cursor: 'pointer' }}
                onClick={() => {
                  if (Array.isArray(slpClientHook?.groups)) {
                    slpClientHook.setFilter((prev: any) => ({
                      ...prev,
                      page_number: 1,
                      groups: slpClientHook.groups.map(
                        (item: any) => item.name
                      ),
                    }));
                  }
                }}
                mb="1rem"
              >
                Select All
              </Text>

              <Stack mt={'1rem'} gap={'2'}>
                {Array.isArray(slpClientHook?.groups) &&
                  slpClientHook?.groups?.map((option: any) => (
                    <Checkbox
                      name={option.id}
                      defaultValue={option.name}
                      key={option.name}
                      checked={slpClientHook.filter.groups.includes(
                        option.name
                      )}
                      onCheckedChange={(e) => {
                        slpClientHook.setFilter((prev: any) => ({
                          ...prev,
                          page_number: 1,
                          groups: e.checked
                            ? [...prev.groups, option.name]
                            : prev.groups?.filter(
                                (item: any) => item !== option.name
                              ),
                        }));
                      }}
                    >
                      {option.name}
                    </Checkbox>
                  ))}
              </Stack>
            </form>
          </PopoverBody>
        </PopoverContent>
      </PopoverRoot>

      <PopoverRoot open={open} onOpenChange={onToggle}>
        <PopoverTrigger asChild>
          <Button size="sm" variant="ghost" _hover={{ bg: 'transparent' }}>
            Filter
            <IoIosArrowDown color="gray" />
          </Button>
        </PopoverTrigger>
        <PopoverContent>
          <PopoverArrow />
          <PopoverBody>
            <Stack>
              <Box flex={1}>
                <CustomSelect
                  placeholder="Select source"
                  options={sessionOptions}
                  onChange={(val) => {
                    setFilterType(val.value);
                  }}
                  defaultValue={sessionOptions?.find(
                    (item) => item.value === filterType
                  )}
                  label="Filter Last Session source"
                />
              </Box>
              <Box flex={1}>
                <StringInput
                  inputProps={{
                    type: 'date',
                    value: selectedDateTime,
                    onChange: (e) => setSelectedDateTime(e.target.value),
                  }}
                />
              </Box>

              <Flex
                my={'1rem'}
                gap={'1rem'}
                alignItems={'center'}
                justifyContent={'flex-end'}
              >
                <Button onClick={clearFilter} variant={'outline'} bg={'gray'}>
                  Clear
                </Button>
                <Button onClick={applyFilter} bg="primary.500">
                  Apply
                </Button>
              </Flex>
            </Stack>
          </PopoverBody>
        </PopoverContent>
      </PopoverRoot>
    </HStack>
  );
}
