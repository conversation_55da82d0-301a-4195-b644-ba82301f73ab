import CustomTable from '@/components/table/CustomTable';
import { Box } from '@chakra-ui/react';
import { getCoreRowModel, getSortedRowModel } from '@tanstack/react-table';
import { columnDef } from './NoinvClientsColumn';

const ClientsWithOutInvoices = ({ slpClientHook }: { slpClientHook: any }) => {
  return (
    <Box width={'full'}>
      <Box minH={'20rem'} mt={'2rem'}>
        <CustomTable
          loading={slpClientHook?.isLoading}
          //   sorting={slpClientHook.sorting}
          //   setSorting={slpClientHook.setSorting}
          columnDef={columnDef}
          data={slpClientHook?.NonInvData?.data || []}
          filter={{
            tableName: 'Followup',
          }}
          total={slpClientHook?.total_count}
          pagination={{
            row: Number(slpClientHook?.filterTwo.items_per_page),
            page: Number(slpClientHook?.filterTwo?.page_number),
          }}
          setPagination={{
            onPageChange: (e) => {
              slpClientHook.setFilterTwo({
                ...slpClientHook.filterTwo,
                page_number: e,
              });
            },
            onRowChange: (e) => {
              slpClientHook.setFilterTwo({
                ...slpClientHook.filterTwo,
                items_per_page: e,
              });
            },
          }}
          tableOptions={{
            // pageCount: 1,
            // manualPagination: true,
            getCoreRowModel: getCoreRowModel(),
            getSortedRowModel: getSortedRowModel(),
            // state: {
            //   sorting: slpClientHook.sorting,
            // },
            // onSortingChange: slpClientHook.setSorting,
            enableSortingRemoval: false,
            enableMultiSort: true,
            enableRowSelection: true,
          }}
        />
      </Box>
    </Box>
  );
};

export default ClientsWithOutInvoices;
