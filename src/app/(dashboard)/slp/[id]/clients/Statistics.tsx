import React from 'react';
import { Icon } from '@chakra-ui/react';
import { Box, Flex, Text } from '@chakra-ui/react';

export default function Statistics({ stats }: any) {
  return (
    <Flex alignItems={'center'} gap={5} flexWrap={'wrap'}>
      {stats?.map((item: any) => {
        const IconComponent = item.icon;
        return (
          <Box
            flex={1}
            minW="250px"
            key={item.name}
            overflow={'hidden'}
            rounded={'lg'}
            display={'flex'}
            justifyContent={'space-between'}
            px={4}
            py={5}
            border={'1px solid'}
            borderColor={'gray.50'}
            _hover={{
              boxShadow: 'lg',
              transition: 'all 0.2s ease-in-out',
            }}
          >
            <Box>
              <Text
                truncate
                fontSize={'0.9rem'}
                color={'gray.300'}
                className="truncate text-sm font-medium text-gray-500"
              >
                {item.name}
              </Text>
              <Text
                mt={1}
                fontSize={'2rem'}
                fontWeight={'bold'}
                color={'gray.900'}
                className="mt-1 text-3xl font-semibold tracking-tight text-gray-900"
              >
                {item.stat}
              </Text>
            </Box>
            {IconComponent && (
              <Icon
                as={IconComponent}
                boxSize={6}
                color={item.iconColor || 'gray.400'}
              />
            )}
          </Box>
        );
      })}
    </Flex>
  );
}
