import AddInvoiceModal from '@/app/(dashboard)/billing/invoices/AddInvoiceModal';
import AnimateLoader from '@/components/elements/loader/animate-loader';
import CustomTable from '@/components/table/CustomTable';
import { useSlpInvoiceHook } from '@/hooks/slp/useSlpInvoice';
import { IUser } from '@/shared/interface/user';
import { Box, Center, Heading } from '@chakra-ui/react';
import React from 'react';
import { getCoreRowModel, getSortedRowModel } from '@tanstack/react-table';
//import { useRouter } from 'next/navigation';
import { createInvoiceColumnDef } from '@/reuseables/invoice-column-def';
import InvoiceSummaryCards from './InvoiceSummaryCards';
import InvoiceFilters from './InvoiceFilters';
// import moment from 'moment';
// import { columnDef } from './columnDef';

export default function Invoices({ slp }: { slp: IUser }) {
  //const router = useRouter();
  const {
    GIapi,
    pagination,
    summaryData,
    isLoading,
    isSummaryLoading,
    refetch,
    filter,
    updateFilter,
  } = useSlpInvoiceHook({ slp });
  // const [pagination, setPagination] = useState({
  //   page: 1,
  //   row: 50,
  // });

  const handlePagination = {
    onPageChange: (page: number) => {
      // setPagination((prev) => ({ ...prev, page }));
      updateFilter('page', page);
    },
    onRowChange: (row: number) => {
      updateFilter('limit', row);
      // setPagination({ row, page: 1 });
    },
  };

  console.log('slp', slp);
  // Filter invoices based on applied filters
  // const filteredData = useMemo(() => {
  //   if (!GIapi) return [];

  //   let filtered = [...GIapi];

  //   // Apply tab filter first
  //   if (filters.activeTab === 'unpaid') {
  //     filtered = filtered.filter((invoice) =>
  //       ['AWAITING_PAYMENT', 'PARTIALLY_PAID', 'OVERDUE'].includes(
  //         invoice.status || ''
  //       )
  //     );
  //   } else if (filters.activeTab === 'draft') {
  //     filtered = filtered.filter((invoice) => invoice.status === 'DRAFT');
  //   }
  //   // 'all' tab shows all invoices, no additional filtering needed

  //   // Apply status filter
  //   if (filters.status && filters.status !== 'all') {
  //     filtered = filtered.filter(
  //       (invoice) => invoice.status === filters.status
  //     );
  //   }

  //   // Apply date range filter
  //   if (filters.fromDate) {
  //     filtered = filtered.filter((invoice) => {
  //       const invoiceDate = moment(invoice.invoice_date || invoice.created_dt);
  //       return invoiceDate.isSameOrAfter(moment(filters.fromDate), 'day');
  //     });
  //   }

  //   if (filters.toDate) {
  //     filtered = filtered.filter((invoice) => {
  //       const invoiceDate = moment(invoice.invoice_date || invoice.created_dt);
  //       return invoiceDate.isSameOrBefore(moment(filters.toDate), 'day');
  //     });
  //   }

  //   // Apply invoice number filter
  //   if (filters.invoiceNumber) {
  //     filtered = filtered.filter((invoice) =>
  //       invoice.invoice_number
  //         ?.toString()
  //         .toLowerCase()
  //         .includes(filters.invoiceNumber.toLowerCase())
  //     );
  //   }

  //   return filtered;
  // }, [GIapi, filters]);

  // const paginatedData = useMemo(() => {
  //   const start = (pagination.page - 1) * pagination.row;
  //   return filteredData.slice(start, start + pagination.row);
  // }, [filteredData, pagination]);

  // const handleRowClick = (
  //   row: any,
  //   event: React.MouseEvent<HTMLTableRowElement>
  // ) => {
  //   const target = event.target as HTMLElement;

  //   if (target.closest('[data-no-row-click="true"]')) {
  //     return;
  //   }

  //   if (slp?.organization?.id !== 1) {
  //     return router.push(`/invoices/${row.original?.id}`);
  //   }
  // };

  // if (isLoading) {
  //   return <AnimateLoader />;
  // }
  // console.log('GIapi', GIapi);
  return (
    <div>
      <Box
        mt={{ base: '3', lg: '5' }}
        display={'flex'}
        alignItems={{ md: 'center' }}
        justifyContent={'space-between'}
        flexDirection={{ base: 'column', md: 'row' }}
        gap={'3'}
      >
        <Heading
          fontSize={{ base: '1.3rem', md: '2rem' }}
          fontWeight={'semibold'}
        >
          {slp?.first_name} {slp?.last_name} Invoices
        </Heading>
        <AddInvoiceModal
          variant={2}
          buttonName="New Invoice"
          refetch={refetch}
          initialValues={{
            slp,
          }}
          org_id={slp?.organization_id}
        />
      </Box>

      {slp?.organization?.id !== 1 ? (
        <>
          {/* Invoice Summary Cards */}
          <InvoiceSummaryCards
            summaryData={summaryData}
            isLoading={isSummaryLoading}
          />
          {/* Invoice Filters */}
          <InvoiceFilters
            filters={filter}
            updateFilter={updateFilter}
            summaryData={summaryData}
          />
        </>
      ) : null}

      {/* ===========TABLE=========== */}
      <Box minH={'20rem'} mt={'6'}>
        {isLoading ? (
          <Center h={'20rem'}>
            <AnimateLoader />
          </Center>
        ) : (
          <CustomTable
            columnDef={createInvoiceColumnDef(slp?.organization?.id, 'slp')}
            data={GIapi}
            // pagination={pagination || {}}
            setPagination={handlePagination}
            total={pagination?.total || 0}
            filter={{
              tableName: 'Followup',
            }}
            pagination={{
              row: Number(filter?.limit),
              page: Number(filter?.page),
            }}
            tableOptions={{
              pageCount: 1,
              manualPagination: true,
              getCoreRowModel: getCoreRowModel(),
              getSortedRowModel: getSortedRowModel(),
            }}
            //onRowClick={handleRowClick}
          />
        )}
      </Box>
    </div>
  );
}
