import React from 'react';
import { Box, HStack, Input, Badge, Flex } from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import CustomSelect from '@/components/Input/CustomSelect';
import { Colors } from '@/constants/colors';
import SelectContact from '@/components/elements/search/SelectContact';
import { TFilter } from '@/api/invoices/find-by-user';
import DateRange from '@/components/Input/DateRange';
import { InvoiceSummaryData } from '@/api/invoices/get-summary';

interface InvoiceFiltersProps {
  filters: TFilter;
  updateFilter: (
    keys: keyof TFilter | (keyof TFilter)[],
    values: any | any[]
  ) => void;
  summaryData?: InvoiceSummaryData;
}

const statusOptions = [
  { label: 'All Statuses', value: '' },
  { label: 'Awaiting Payment', value: 'AWAITING_PAYMENT' },
  { label: 'Partially Paid', value: 'PARTIALLY_PAID' },
  { label: 'Paid', value: 'PAID' },
  { label: 'Unpaid', value: 'UNPAID' },
];

export default function InvoiceFilters({
  filters,
  updateFilter,
  summaryData,
}: InvoiceFiltersProps) {
  // Count active filters (excluding default values)
  const activeFilterCount = Object.entries(filters).filter(([key, value]) => {
    if (['limit', 'page', 'org_id'].includes(key)) return false;
    return value && value !== '' && value !== 'all';
  }).length;

  return (
    <Box mb="6">
      {/* Active filters indicator */}
      {activeFilterCount > 0 && (
        <Box mb="4">
          <HStack gap="2" align="center">
            <Badge
              colorScheme="blue"
              borderRadius="full"
              bg={Colors?.ORANGE?.LIGHT}
              px="2"
              py="1"
              fontSize="xs"
            >
              {activeFilterCount} Active Filters
            </Badge>
          </HStack>
        </Box>
      )}

      {/* Filter Controls - Fully Responsive */}
      <Flex
        direction={{ base: 'column', md: 'row' }}
        gap="4"
        mb="4"
        justify={{ base: 'stretch', md: 'center' }}
        align={{ base: 'stretch', md: 'center' }}
        wrap="wrap"
      >
        {/* Customer Dropdown - responsive width */}
        <Box
          flex={{ base: 'none', md: '1' }}
          minW={{ base: 'full', md: '200px' }}
          maxW={{ base: 'full', md: 'auto' }}
          flexShrink={0}
        >
          <SelectContact
            showNone={true}
            NoneLabel={'All Clients'}
            selectedClient={filters.client_id}
            handleSelectClient={(client: any) => {
              updateFilter('client_id', client.id);
            }}
          />
        </Box>

        {/* Status Dropdown - responsive width */}
        <Box width={{ base: 'full', md: '200px', lg: '180px' }} flexShrink={0}>
          <CustomSelect
            options={statusOptions}
            value={statusOptions.find((opt) => opt.value === filters.status)}
            onChange={(selectedOption) =>
              updateFilter('status', selectedOption?.value)
            }
            placeholder="All Statuses"
            controlStyle={{
              minHeight: '2.5rem',
              height: '2.5rem',
            }}
          />
        </Box>

        {/* Date Range - responsive */}
        <Box
          // flex={{ base: 'none', md: '1' }}
          minW={{ base: 'full', md: '300px' }}
          maxW={{ base: 'full', md: '400px' }}
        >
          <DateRange
            fromValue={filters.date_from}
            toValue={filters.date_to}
            onDateRangeChange={(fromValue, toValue) => {
              updateFilter(['date_from', 'date_to'], [fromValue, toValue]);
            }}
            fromPlaceholder="From"
            toPlaceholder="To"
            height="2.5rem"
            borderRadius="0.5rem"
            border="1px solid #636D79"
          />
        </Box>

        {/* Search Input - responsive width */}
        <Box width={{ base: 'full', md: '250px', lg: '300px' }} flexShrink={0}>
          <Input
            placeholder="Enter Invoice #"
            value={filters.invoice_no}
            onChange={(e) => updateFilter('invoice_no', e.target.value)}
            height="2.5rem"
            borderRadius="0.5rem"
            border="1px solid #636D79"
          />
        </Box>
      </Flex>

      {/* Status Tabs - Centered */}
      <Flex justify="center" mb="4">
        <HStack gap="0" bg={Colors?.ORANGE?.LIGHT} borderRadius="lg" p="1">
          <Button
            shadow={filters.status === 'UNPAID' ? '2xl' : 'none'}
            variant={filters.status === 'UNPAID' ? 'solid' : 'ghost'}
            bg={filters.status === 'UNPAID' ? 'white' : 'transparent'}
            color={filters.status === 'UNPAID' ? 'black' : 'gray.600'}
            size="sm"
            borderRadius="md"
            onClick={() => updateFilter('status', 'UNPAID')}
            px="4"
            py="2"
          >
            Unpaid
            <Badge
              ml="2"
              bg={
                filters.status === 'UNPAID'
                  ? Colors?.ORANGE?.PRIMARY
                  : Colors?.ORANGE?.LIGHT
              }
              color={filters.status === 'UNPAID' ? 'white' : 'gray.600'}
              borderRadius="full"
              border={`1px solid ${Colors?.ORANGE?.PRIMARY}`}
              px="2"
              py="0.5"
              fontSize="xs"
            >
              {summaryData?.statusCounts?.unpaid || 0}
            </Badge>
          </Button>

          <Button
            shadow={!filters.status || filters.status === '' ? '2xl' : 'none'}
            variant={
              !filters.status || filters.status === '' ? 'solid' : 'ghost'
            }
            bg={
              !filters.status || filters.status === '' ? 'white' : 'transparent'
            }
            color={
              !filters.status || filters.status === '' ? 'black' : 'gray.600'
            }
            size="sm"
            borderRadius="md"
            onClick={() => updateFilter('status', '')}
            px="4"
            py="2"
          >
            All Invoices
            <Badge
              ml="2"
              bg={
                !filters.status || filters.status === ''
                  ? Colors?.ORANGE?.PRIMARY
                  : Colors?.ORANGE?.LIGHT
              }
              color={
                !filters.status || filters.status === '' ? 'white' : 'gray.600'
              }
              borderRadius="full"
              border={`1px solid ${Colors?.ORANGE?.PRIMARY}`}
              px="2"
              py="0.5"
              fontSize="xs"
            >
              {summaryData?.statusCounts?.total || 0}
            </Badge>
          </Button>
        </HStack>
      </Flex>
    </Box>
  );
}
