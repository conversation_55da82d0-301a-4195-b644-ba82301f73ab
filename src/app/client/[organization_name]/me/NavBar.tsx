import {
  Box,
  Container,
  Drawer<PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON>erR<PERSON>,
  Flex,
  HStack,
  Text,
} from '@chakra-ui/react';
import React, { useCallback } from 'react';
import { IconButton, Popover, Portal, Avatar, Menu } from '@chakra-ui/react';
import {
  LuCircleHelp,
  LuLogOut,
  LuSettings,
  LuUser,
  LuBell,
} from 'react-icons/lu';
import supabase from '@/lib/supabase/client';
import { deleteCookie } from '@/utils/cookie-helper';
import avatar from '@/assets/default-avatar.png';
import { useGetClient } from '../../_hook/useGetClient';
import Link from 'next/link';
import { useDisclosure } from '@chakra-ui/react';
import { DrawerBackdrop } from '@chakra-ui/react';
import { DrawerCloseTrigger } from '@chakra-ui/react';
import { FiMenu } from 'react-icons/fi';
import { Sidebar } from './Sidebar';
export const NotificationPopover = () => {
  return (
    <Popover.Root>
      <Popover.Trigger asChild>
        <IconButton variant="ghost" rounded="full" colorPalette="gray">
          <LuBell />
        </IconButton>
      </Popover.Trigger>
      <Portal>
        <Popover.Positioner>
          <Popover.Content maxW="fit-content">
            <Popover.Body>
              <Popover.Title fontWeight="medium">Notifications</Popover.Title>
            </Popover.Body>
          </Popover.Content>
        </Popover.Positioner>
      </Portal>
    </Popover.Root>
  );
};

export const UserMenu = ({ organization_name }: any) => {
  const { ClientData } = useGetClient(organization_name);
  const handleSignOut = useCallback(async () => {
    await supabase.auth.signOut();
    window.localStorage.clear();
    deleteCookie('user_data');
    window.location.reload();
  }, []);

  return (
    <Menu.Root positioning={{ placement: 'bottom' }}>
      <Menu.Trigger outline={'none !important'} rounded="full">
        <Flex alignItems={'center'} gap={'1rem'}>
          <Avatar.Root>
            <Avatar.Fallback />
            <Avatar.Image src={avatar.src} />
          </Avatar.Root>
          <Text
            fontSize={'.75rem'}
            fontWeight={'500'}
            display={{ base: 'none', md: 'block' }}
          >
            {ClientData?.client?.first_name} {ClientData?.client?.last_name}
          </Text>
        </Flex>
      </Menu.Trigger>
      <Portal>
        <Menu.Positioner>
          <Menu.Content>
            <Menu.Item value="profile" asChild>
              <Link href={`/client/${organization_name}/me/profile`}>
                <LuUser />
                Profile
              </Link>
            </Menu.Item>
            <Menu.Item value="settings">
              <LuSettings />
              Settings
            </Menu.Item>
            <Menu.Item value="help">
              <LuCircleHelp />
              Help & Support
            </Menu.Item>
            <Menu.Separator />
            <Menu.Item onClick={handleSignOut} value="logout">
              <LuLogOut />
              Logout
            </Menu.Item>
          </Menu.Content>
        </Menu.Positioner>
      </Portal>
    </Menu.Root>
  );
};

export default function NavBar({ organization_name }: any) {
  const { onOpen, onClose, open } = useDisclosure();
  return (
    <>
      <Box display={{ base: 'block', lg: 'none' }}>
        <DrawerRoot open={open} onOpenChange={onClose} placement={'start'}>
          <DrawerBackdrop />
          <DrawerContent pos={'absolute'} minH={'100vh'}>
            <DrawerBody pl={'0'} scrollbar={'hidden'} scrollBehavior="smooth">
              <Sidebar
                organization_name={organization_name}
                onClose={onClose}
              />
            </DrawerBody>
            <DrawerCloseTrigger />
          </DrawerContent>
        </DrawerRoot>
      </Box>
      <Container bg={'white'} height={{ base: '4.5rem', lg: '5.5rem' }}>
        <HStack alignItems={'center'} h={'100%'} justify="space-between">
          <Box display={{ base: 'none', lg: 'block' }}></Box>
          <IconButton
            aria-label="Mobile hamburger"
            onClick={onOpen}
            variant="outline"
            borderColor={'gray.50'}
            hideFrom={'lg'}
            w={'5'}
            mr={'2'}
          >
            <FiMenu />
          </IconButton>
          <HStack gap={{ base: '2', md: '3' }}>
            {/* <SearchPopover hideFrom="md" /> */}
            {/* <NotificationPopover /> */}
            <UserMenu organization_name={organization_name} />
          </HStack>
        </HStack>
      </Container>
    </>
  );
}
