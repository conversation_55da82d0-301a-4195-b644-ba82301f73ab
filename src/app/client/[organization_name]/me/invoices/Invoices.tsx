'use client';

import { useGetClientByEmailQueryCM } from '@/api/clients/module/get-client-by-email';
import { useGetClientInvoicesQueryCM } from '@/api/clients/module/get-client-invoices';
// import { useGetInvoicesByClientQuery } from '@/api/invoices/get-invoices-by-client-id';
// import { useGetUserBySlugQuery } from '@/api/users/find-by-event-slug';
import PageLoader from '@/components/elements/loader/PageLoader';
import CustomTable from '@/components/table/CustomTable';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';
import { Box, Heading } from '@chakra-ui/react';
import { createColumnDef } from './column-def';
import { useGetOrganizationBySlugQuery } from '@/api/organizations/get-by-slug';
import EmptyState from '@/components/elements/EmptyState';

export default function Invoices({
  organization_name,
}: {
  organization_name: string;
}) {
  const session = useSupabaseSession();
  const { data: OrganizationData, isLoading: OrganizationLoading } =
    useGetOrganizationBySlugQuery({
      slug: organization_name,
      isPublic: 'true',
    });
  const { data: ClientData, isLoading: ClientLoading } =
    useGetClientByEmailQueryCM(
      {
        email: String(session?.user?.email),
        organization_id: OrganizationData?.[0]?.id,
      },
      {
        enabled:
          Boolean(OrganizationData?.[0]?.id) &&
          Boolean(String(session?.user?.email)),
      }
    );
  const { data: InvoicesData, isLoading: InvoicesLoading } =
    useGetClientInvoicesQueryCM(
      {
        id: ClientData?.client_id,
        organization_id: OrganizationData?.[0]?.id,
      },
      {
        enabled:
          Boolean(ClientData?.client_id) &&
          Boolean(String(session?.user?.email)),
      }
    );

  // console.log('ClientData is ', ClientData);
  // console.log('InvoicesData is ', InvoicesData);
  // console.log('OrganizationData is ', OrganizationData);
  // console.log('organization_name is ', organization_name);
  if (OrganizationLoading || InvoicesLoading || ClientLoading) {
    return <PageLoader />;
  }
  return (
    <Box>
      <Heading
        mb={{ base: '2', md: '4' }}
        fontSize={{ base: '1.3rem', md: '1.5rem' }}
      >
        Invoices
      </Heading>
      <Box w={'full'} overflowX={'auto'}>
        {InvoicesData && InvoicesData?.length > 0 ? (
          <CustomTable
            columnDef={createColumnDef()}
            data={InvoicesData || []}
            // loading={dataIsloading}
            filter={{
              tableName: 'Invoices',
            }}
          />
        ) : (
          <EmptyState text="No invoices" />
        )}
      </Box>
    </Box>
  );
}
