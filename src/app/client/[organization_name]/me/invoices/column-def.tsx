// import Status from '@/components/elements/status/Status';
//import { formatNumber } from '@/utils/num-format';
import { Box, Text } from '@chakra-ui/react';
import { createColumnHelper } from '@tanstack/react-table';
import moment from 'moment';
import EditInvoice from './EditInvoice';
import Status from '@/components/elements/status/Status';
import { formatMoney } from '@/components/elements/format-money/FormatMoney';

const columnHelper = createColumnHelper<any>();

export const createColumnDef = () => {
  return [
    columnHelper.accessor('status', {
      cell: (info) => <Status name={info.getValue() as string} />,
      header: 'Status',
      id: 'status',
    }),
    columnHelper.accessor('invoice_date', {
      cell: (info) => {
        return (
          <Box minW={'5rem'}>
            {info.getValue() ? (
              <p>{moment(info.getValue()).utc().format('MMMM D, YYYY')}</p>
            ) : (
              <Text color={'red'}>MISSING</Text>
            )}
          </Box>
        );
      },
      header: 'Date',
      id: 'invoice_date',
      sortingFn: 'datetime',
    }),
    columnHelper.accessor('invoice_number', {
      cell: (info) => <Box minW={'5rem'}>{info.getValue()}</Box>,
      header: 'No.',
      id: 'invoice-no',
    }),

    columnHelper.display({
      header: 'Service Provider',
      id: 'slp',
      cell: (props) => <Box>{props.row.original.slp}</Box>,
    }),
    // columnHelper.display({
    //   header: 'Total Minutes',
    //   id: 'total-hours',
    //   cell: (props) => (
    //     <Box>
    //       {!props.row.original.total_hours &&
    //       props.row.original.memo?.toLowerCase() === 'free referral'
    //         ? '0.5'
    //         : props.row.original.total_hours}
    //     </Box>
    //   ),
    // }),
    columnHelper.display({
      header: 'Amount',
      id: 'total-price',
      cell: (props) => {
        return (
          <Box minW={'3.5rem'}>
            {formatMoney(props.row.original.total_price)}
          </Box>
        );
      },
    }),
    columnHelper.accessor('amount_due', {
      header: 'Amount Due',
      id: 'amount_due',
      cell: (info) => <Box>{formatMoney(info.getValue())}</Box>,
    }),
    // columnHelper.display({
    //   header: 'Status',
    //   id: 'status',
    //   cell: (props) => <Status name={props.row.original?.status} />,
    // }),
    columnHelper.display({
      id: 'actions',
      cell: (props) => <EditInvoice invoice={props.row.original} />,
      header: 'Action',
    }),
  ];
};
