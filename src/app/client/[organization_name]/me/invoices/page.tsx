import React from 'react';
import Invoices from './Invoices';
import { Metadata } from 'next';

export async function generateMetadata({
  params,
}: {
  params: { organization_name: string };
}): Promise<Metadata> {
  const fullTitle = `Soap - ${params.organization_name} - Invoice`;
  const description = `${params.organization_name} Invoice Page | Soap`;

  return {
    title: fullTitle,
    description,
    openGraph: {
      title: fullTitle,
      description,
      url: `https://app.soapnotes.online/client/${params.organization_name}/me/invoices`,
      siteName: 'Soap Dashboard',
      images: [
        {
          url: 'https://app.soapnotes.online/og-image.png',
          width: 1200,
          height: 630,
          alt: 'Soap Invoice Page',
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: ['https://app.soapnotes.online/twitter-image.png'],
    },
    metadataBase: new URL('https://app.soapnotes.online'),
  };
}

export default function page(props: any) {
  return <Invoices organization_name={props?.params.organization_name} />;
}
