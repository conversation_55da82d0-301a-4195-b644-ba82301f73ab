'use client';

import { Card, Input, Field, VStack, Grid, GridItem } from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';

interface ClientData {
  id: number;
  email: string;
  organization_id: number;
  client_id: number;
  client: {
    id: number;
    first_name: string;
    last_name: string;
    phone?: string;
  };
}

const ProfileContent = ({
  clientData,
  refetch,
}: {
  clientData: ClientData | null;
  refetch: () => void;
}) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
  });

  useEffect(() => {
    if (clientData?.client) {
      setFormData({
        firstName: clientData?.client?.first_name ?? '',
        lastName: clientData?.client?.last_name ?? '',
        phone: clientData?.client?.phone ?? '',
      });
    }
  }, [clientData?.client]);

  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    if (!clientData) return;

    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch(`/api/public/clients/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          phone: formData.phone,
          clientId: clientData.client_id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }
      await refetch();
      setMessage('Profile updated successfully!');
      setTimeout(() => setMessage(''), 3000);
    } catch (error) {
      console.error('Error updating profile:', error);
      setMessage('Failed to update profile. Please try again.');
      setTimeout(() => setMessage(''), 3000);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card.Root borderColor={'gray.50'}>
      <Card.Header gap={'0.5'} px={{ base: '3', md: '6' }}>
        <Card.Title fontSize={{ base: 'lg', lg: 'xl' }}>
          Personal Information
        </Card.Title>
        <Card.Description color="#6b7280" fontSize={{ base: 'sm', lg: 'md' }}>
          Update your personal details and contact information
        </Card.Description>
      </Card.Header>
      <Card.Body px={{ base: '3', md: '6' }}>
        <VStack gap={4} align="stretch">
          {message && (
            <div
              style={{
                padding: '12px',
                borderRadius: '6px',
                backgroundColor: message.includes('success')
                  ? '#d4edda'
                  : '#f8d7da',
                color: message.includes('success') ? '#155724' : '#721c24',
                border: `1px solid ${message.includes('success') ? '#c3e6cb' : '#f5c6cb'}`,
              }}
            >
              {message}
            </div>
          )}
          <Grid templateColumns="repeat(2, 1fr)" gap={4}>
            <GridItem>
              <Field.Root>
                <Field.Label color={'#111827'} fontWeight={'600'}>
                  First Name
                </Field.Label>
                <Input
                  value={formData.firstName}
                  onChange={(e) =>
                    handleInputChange('firstName', e.target.value)
                  }
                  borderColor={'gray.50'}
                />
              </Field.Root>
            </GridItem>
            <GridItem>
              <Field.Root>
                <Field.Label color={'#111827'} fontWeight={'600'}>
                  Last Name
                </Field.Label>
                <Input
                  value={formData.lastName}
                  onChange={(e) =>
                    handleInputChange('lastName', e.target.value)
                  }
                  borderColor={'gray.50'}
                />
              </Field.Root>
            </GridItem>
          </Grid>
          <Field.Root>
            <Field.Label color={'#111827'} fontWeight={'600'}>
              Email
            </Field.Label>
            <Input
              type="email"
              value={clientData?.email ?? ''}
              borderColor={'gray.50'}
              readOnly
              bg={'gray.50'}
              cursor={'not-allowed'}
            />
          </Field.Root>
          <Field.Root>
            <Field.Label color={'#111827'} fontWeight={'600'}>
              Phone Number
            </Field.Label>
            <Input
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              borderColor={'gray.50'}
            />
          </Field.Root>
          <Button
            alignSelf="start"
            bg={'#e97a5b'}
            _hover={{ bg: 'orange.600' }}
            onClick={handleSave}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </VStack>
      </Card.Body>
    </Card.Root>
  );
};

export default ProfileContent;

// 'use client';

// import { Card, Input, Field, VStack, Grid, GridItem } from '@chakra-ui/react';
// import { Button } from '@/components/ui/button';

// const ProfileContent = ({ clientData }: { clientData: any }) => {
//   return (
//     <Card.Root borderColor={'gray.50'}>
//       <Card.Header gap={'0.5'} px={{ base: '3', md: '6' }}>
//         <Card.Title fontSize={{ base: 'lg', lg: 'xl' }}>
//           Personal Information
//         </Card.Title>
//         <Card.Description color="#6b7280" fontSize={{ base: 'sm', lg: 'md' }}>
//           Update your personal details and contact information
//         </Card.Description>
//       </Card.Header>
//       <Card.Body px={{ base: '3', md: '6' }}>
//         <VStack gap={4} align="stretch">
//           <Grid templateColumns="repeat(2, 1fr)" gap={4}>
//             <GridItem>
//               <Field.Root>
//                 <Field.Label color={'#111827'} fontWeight={'600'}>
//                   First Name
//                 </Field.Label>
//                 <Input
//                   defaultValue={clientData?.client?.first_name ?? ''}
//                   borderColor={'gray.50'}
//                 />
//               </Field.Root>
//             </GridItem>
//             <GridItem>
//               <Field.Root>
//                 <Field.Label color={'#111827'} fontWeight={'600'}>
//                   Last Name
//                 </Field.Label>
//                 <Input
//                   defaultValue={clientData?.client?.last_name ?? ''}
//                   borderColor={'gray.50'}
//                 />
//               </Field.Root>
//             </GridItem>
//           </Grid>
//           <Field.Root>
//             <Field.Label color={'#111827'} fontWeight={'600'}>
//               Email
//             </Field.Label>
//             <Input
//               type="email"
//               defaultValue={clientData?.email ?? ''}
//               borderColor={'gray.50'}
//             />
//           </Field.Root>
//           <Field.Root>
//             <Field.Label color={'#111827'} fontWeight={'600'}>
//               Phone Number
//             </Field.Label>
//             <Input
//               defaultValue={clientData?.client?.phone ?? ''}
//               borderColor={'gray.50'}
//             />
//           </Field.Root>
//           <Button
//             alignSelf="start"
//             bg={'#e97a5b'}
//             _hover={{ bg: 'orange.600' }}
//           >
//             Save Changes
//           </Button>
//         </VStack>
//       </Card.Body>
//     </Card.Root>
//   );
// };

// export default ProfileContent;
