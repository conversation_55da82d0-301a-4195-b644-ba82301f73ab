import { Box, Center } from '@chakra-ui/react';
import React from 'react';
import { Onboarding } from './Onboarding';
import { Metadata } from 'next';
import { getOrganizationName } from '@/utils/server-cookie-helper';

export async function generateMetadata(): Promise<Metadata> {
  const organization_name = getOrganizationName();
  const fullTitle = `Soap - ${organization_name} - SLPs`;
  const description = `Soap Note platform.`;

  return {
    title: fullTitle,
    description,
  };
}
export default function page() {
  return (
    <Center minH={'vh'} w={'full'}>
      <Box
        px={'6'}
        py={'4'}
        bg={'white'}
        boxShadow={'2xl'}
        minW={{ base: '90%', md: '65%', lg: '50%' }}
        rounded={'lg'}
      >
        <Onboarding />
      </Box>
    </Center>
  );
}
