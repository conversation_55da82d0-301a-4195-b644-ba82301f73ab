import { useAddOnboardingApi } from '@/api/users/add-onboarding-id';
import { toaster } from '@/components/ui/toaster';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { OnboardingQuestionKey } from './consts';
import { useSupabaseSession } from '@/hooks/auth/useUserSession';

export const useOnboarding = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const stepFromUrl = Number(searchParams.get('step')) || 1;
  const { mutateAsync, isLoading } = useAddOnboardingApi();
  const [step, setStep] = useState(stepFromUrl);
  const { UserFromQuery } = useSupabaseSession();

  const [payload, setPayload] = useState<{
    [key in OnboardingQuestionKey]: any;
  }>({
    'i-am-a': '',
    'team-size': '',
    tools: [],
  });

  const handleStep = (step: number) => {
    const newUrl = `/onboarding?step=${step}`;
    router.replace(newUrl, { scroll: false });
    setStep(step);
  };

  const handleSubmit = async () => {
    const { data } = await mutateAsync({
      details: payload,
      userId: UserFromQuery.id,
    });
    router.push(`/slp/${data?.user_id}`);
    toaster.create({
      type: 'success',
      description: 'Onboarding submitted successfully',
    });
  };

  return {
    step,
    payload,
    isLoading,
    handleStep,
    setPayload,
    handleSubmit,
  };
};

export type OnboardingHook = ReturnType<typeof useOnboarding>;
