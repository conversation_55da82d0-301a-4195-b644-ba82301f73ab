import type { <PERSON>ada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

import { AppProvider } from '@/providers/AppProvider';

export const metadata: Metadata = {
  title: 'Soap Dashboard',
  description: 'Admin dashboard for Soap',
};

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' });

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html className={inter.className} lang="en" suppressHydrationWarning={true}>
      <body>
        <AppProvider>{children}</AppProvider>
      </body>
    </html>
  );
}
