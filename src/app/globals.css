.DraftJsPlaygroundContainer-editor {
  padding: 0;
  height: auto;
}
.custom-time-picker-availabilities input {
  opacity: 1 !important;
}
.RichEditor-root {
  font-family: 'Georgia', serif;
  font-size: 14px;
  padding: 15px;
}

.btnLink:hover {
  border: none !important;
  outline: none !important;
  text-decoration: none !important;
}

.lead-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  margin-top: 1rem;
}

.lead-table thead {
  background-color: #f3f4f6;
}

.lead-table th,
.lead-table td {
  padding: 12px 16px;
  font-weight: 500;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.lead-table tbody tr:hover {
  background-color: #f9fafb;
}

.loader {
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  background:
    radial-gradient(farthest-side, #e97a5b 94%, #0000) top/8px 8px no-repeat,
    conic-gradient(#0000 30%, #e97a5b);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 8px), #000 0);
  animation: l13 1s infinite linear;
}

@keyframes l13 {
  100% {
    transform: rotate(1turn);
  }
}

.fc-toolbar-title {
  font-size: 14px !important;
}
.fc-button {
  font-size: 14px !important;
  margin-top: 10px !important;
}

.fc-event-time {
  text-transform: uppercase;
}

.fc-timegrid-slot-label {
  text-transform: uppercase;
}

.fc-scroller {
  overflow: hidden !important;
}

.public-DraftEditor-content {
  min-height: 100px;
  /* border: 1px solid #000; */
  overflow: auto;
  outline: none;
  transition: border-color 0.3s;
  width: 100%;
  padding: 5px;
}

.public-DraftEditor-content:focus {
  /* border-color: #000;  */
}

.public-DraftEditorPlaceholder-root {
  padding: 5px; /* Match content padding */
}
.react-datepicker__day--highlighted {
  background-color: #e97a5b !important; /* Highlight color */
  color: #fff !important; /* Text color for contrast */
  border-radius: 50%; /* Circular shape */
}

/* Style for the selected day */
.react-datepicker__day--selected {
  background-color: #216ba5 !important; /* Blue for the selected day */
  color: #fff !important; /* Text color for contrast */
  border-radius: 50%; /* Circular shape */
}

/* Ensure selected day takes precedence over highlighted day */
.react-datepicker__day--selected.react-datepicker__day--highlighted {
  background-color: #216ba5 !important; /* Selected day blue takes precedence */
  color: #fff !important; /* Ensure text color matches */
}
.react-datepicker__input-container input {
  padding: 10px 12px;
  border: 2px solid #4a90e2;
  border-radius: 8px;
  font-size: 16px;
  outline: none;
  width: 100%;
}

.scroll-container {
  overflow: auto;
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: transparent transparent; /* Hide thumb */
}

/* Chrome, Safari */
.scroll-container::-webkit-scrollbar {
  width: 8px;
}
.scroll-container::-webkit-scrollbar-thumb {
  background-color: transparent; /* Hide initially */
}
.scroll-container::-webkit-scrollbar-track {
  background: transparent;
}

/* On hover: show thumb */
.scroll-container:hover {
  scrollbar-color: #c1c1c1 transparent;
}
.scroll-container:hover::-webkit-scrollbar-thumb {
  background-color: #c1c1c1;
  border-radius: 8px;
}

.RichEditor-editor {
  cursor: text;
  font-size: 16px;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.RichEditor-editor .public-DraftEditorPlaceholder-root,
.RichEditor-editor .public-DraftEditor-content {
  margin: 0 -15px -15px;
  padding: 15px;
}

.RichEditor-editor .public-DraftEditor-content {
  min-height: 1001px;
}

.RichEditor-hidePlaceholder .public-DraftEditorPlaceholder-root {
  display: none;
}

.RichEditor-editor .RichEditor-blockquote {
  border-left: 5px solid #eee;
  color: #666;
  font-family: 'Hoefler Text', 'Georgia', serif;
  font-style: italic;
  margin: 16px 0;
  padding: 10px 20px;
}

.RichEditor-editor .public-DraftStyleDefault-pre {
  background-color: rgba(0, 0, 0, 0.05);
  font-family: 'Inconsolata', 'Menlo', 'Consolas', monospace;
  font-size: 16px;
  padding: 20px;
}

.RichEditor-controls-container {
  border-bottom: 1px solid #ccc;
  background: #fff;
  /* z-index: 10; */
}

.RichEditor-controls {
  font-family: 'Helvetica', sans-serif;
  font-size: 14px;
  margin-bottom: 5px;
  user-select: none;
}

.RichEditor-styleButton {
  color: #999;
  cursor: pointer;
  margin-right: 16px;
  padding: 2px 0;
  display: inline-block;
}

.RichEditor-activeButton {
  color: #5890ff;
}

/* Style for disabled and readonly inputs, textareas, and select elements */
input:disabled,
input:read-only,
textarea:disabled,
textarea:read-only,
select:disabled,
select:read-only {
  background-color: #eeeeee;
  color: #6c6c6c;
  cursor: not-allowed;
  border-color: #d0d0d0;
  opacity: 0.7;
}

.color-input::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-input::-webkit-color-swatch {
  border: none;
  border-radius: 0;
}

/* Specific placeholder styling for disabled/readonly states */
input:disabled::placeholder,
input:read-only::placeholder,
textarea:disabled::placeholder,
textarea:read-only::placeholder {
  color: #9c9c9c;
}

/* HTML: <div class="loader"></div> */
.page-loader {
  height: 30px;
  aspect-ratio: 2.5;
  --_g: no-repeat radial-gradient(farthest-side, #e97a5b 90%, #e97a5b);
  background: var(--_g), var(--_g), var(--_g), var(--_g);
  background-size: 20% 50%;
  animation: l43 1s infinite linear;
}
@keyframes l43 {
  0% {
    background-position:
      calc(0 * 100% / 3) 50%,
      calc(1 * 100% / 3) 50%,
      calc(2 * 100% / 3) 50%,
      calc(3 * 100% / 3) 50%;
  }
  16.67% {
    background-position:
      calc(0 * 100% / 3) 0,
      calc(1 * 100% / 3) 50%,
      calc(2 * 100% / 3) 50%,
      calc(3 * 100% / 3) 50%;
  }
  33.33% {
    background-position:
      calc(0 * 100% / 3) 100%,
      calc(1 * 100% / 3) 0,
      calc(2 * 100% / 3) 50%,
      calc(3 * 100% / 3) 50%;
  }
  50% {
    background-position:
      calc(0 * 100% / 3) 50%,
      calc(1 * 100% / 3) 100%,
      calc(2 * 100% / 3) 0,
      calc(3 * 100% / 3) 50%;
  }
  66.67% {
    background-position:
      calc(0 * 100% / 3) 50%,
      calc(1 * 100% / 3) 50%,
      calc(2 * 100% / 3) 100%,
      calc(3 * 100% / 3) 0;
  }
  83.33% {
    background-position:
      calc(0 * 100% / 3) 50%,
      calc(1 * 100% / 3) 50%,
      calc(2 * 100% / 3) 50%,
      calc(3 * 100% / 3) 100%;
  }
  100% {
    background-position:
      calc(0 * 100% / 3) 50%,
      calc(1 * 100% / 3) 50%,
      calc(2 * 100% / 3) 50%,
      calc(3 * 100% / 3) 50%;
  }
}

.tiptap :first-child {
  margin-top: 0;
}

.tiptap ul ul,
.tiptap ol ul {
  list-style-type: circle;
}

.tiptap ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.tiptap ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.tiptap li {
  margin-bottom: 0.5rem;
}

.tiptap ol ol,
.tiptap ul ol {
  list-style-type: lower-roman;
}
