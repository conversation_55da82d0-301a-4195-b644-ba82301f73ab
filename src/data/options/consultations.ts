import { getOptionsFromArray } from '@/utils/get-options-from-array';

// export interface ContactStage {
//   id: string;
//   label: string;
//   isDefault?: boolean;
// }

// export const defaultStages: ContactStage[] = [
//   { id: 'lead', label: 'Lead', isDefault: true },
//   { id: 'prospect', label: 'Prospect', isDefault: true },
//   { id: 'client', label: 'Client', isDefault: true },
//   { id: 'qualified', label: 'Qualified', isDefault: true },
//   { id: 'sql', label: 'SQL', isDefault: true },
//   { id: 'customer', label: 'Customer', isDefault: true },
//   { id: 'unqualified', label: 'Unqualified', isDefault: true },
//   { id: 'no_show', label: 'No Show', isDefault: true },
//   { id: 'cancelled', label: 'Cancelled', isDefault: true },
//   { id: 'closed_lost', label: 'Closed Lost', isDefault: true },
// ];

// export const contactStages = [
//   'Lead',
//   'Prospect',
//   'Qualified',
//   'SQL', // Sales Qualified Lead
//   'Customer',
//   'Unqualified',
//   'No Show',
//   'Cancelled',
//   'Closed Lost',
// ];
// export const contactStagesOptions = getOptionsFromArray(contactStages);

export const leadQualities = ['Low', 'Medium', 'High'];
export const leadQualitiesOptions = getOptionsFromArray(leadQualities);

export const provinces = [
  'Alberta',
  'British Columbia',
  'Manitoba',
  'New Brunswick',
  'Newfoundland and Labrador',
  'Nova Scotia',
  'Ontario',
  'Prince Edward Island',
  'Quebec',
  'Saskatchewan',
  'Northwest Territories',
  'Nunavut',
  'Yukon',
  'Other - Canada',
  'Other - United States',
  'Other - International',
];

export const provinceOptions = getOptionsFromArray(provinces);

export const referralSources = [
  'Unknown',
  'Word of Mouth',
  'Instagram',
  'Tik Tok',
  'Facebook',
  'Linkedin',
  'XHS',
  'Google Search',
  'Meta Lead Form',
  'Referral Partner',
  'Email/newsletter',
  'Workshop',
  'Event',
  'Other',
];
export const referralSourceOptions = getOptionsFromArray(referralSources);

export const contactChannels = ['Phone', 'Email', 'Text'];
export const contactChannelsOptions = getOptionsFromArray(contactChannels);
