export const packageStatusOptions = [
  {
    label: 'COMPLETED',
    value: 'COMPLETED',
  },
  {
    label: 'ACTIVE',
    value: 'ACTIVE',
  },
  {
    label: 'VOID',
    value: 'VOID',
  },
  {
    label: 'REFUNDED',
    value: 'REFUNDED',
  },
];

export const currencyOptions = [
  { value: 'USD', label: 'USD - United States Dollar', symbol: '$' },
  { value: 'EUR', label: 'EUR - Euro', symbol: '€' },
  { value: 'GBP', label: 'GBP - British Pound Sterling', symbol: '£' },
  { value: 'JPY', label: 'JPY - Japanese Yen', symbol: '¥' },
  { value: 'CAD', label: 'CAD - Canadian Dollar', symbol: '$' },
  { value: 'AUD', label: 'AUD - Australian Dollar', symbol: '$' },
  { value: 'CHF', label: 'CHF - Swiss Franc', symbol: 'CHF' },
  { value: 'CNY', label: 'CNY - Chinese Yuan', symbol: '¥' },
  { value: 'INR', label: 'INR - Indian Rupee', symbol: '₹' },
  { value: 'BRL', label: 'BRL - Brazilian Real', symbol: 'R$' },
  { value: 'MXN', label: 'MXN - Mexican Peso', symbol: '$' },
  { value: 'KRW', label: 'KRW - South Korean Won', symbol: '₩' },
  { value: 'SGD', label: 'SGD - Singapore Dollar', symbol: '$' },
  { value: 'HKD', label: 'HKD - Hong Kong Dollar', symbol: '$' },
  { value: 'NOK', label: 'NOK - Norwegian Krone', symbol: 'kr' },
  { value: 'SEK', label: 'SEK - Swedish Krona', symbol: 'kr' },
  { value: 'DKK', label: 'DKK - Danish Krone', symbol: 'kr' },
  { value: 'NZD', label: 'NZD - New Zealand Dollar', symbol: '$' },
  { value: 'ZAR', label: 'ZAR - South African Rand', symbol: 'R' },
  { value: 'NGN', label: 'NGN - Nigerian Naira', symbol: '₦' },
];
export const transactionMethodOptions = [
  { label: 'Cash', value: 'CASH' },
  { label: 'E Transfer', value: 'E_TRANSFER' },
  { label: 'Bank Transfer', value: 'BANK_TRANSFER' },
  { label: 'Credit Card', value: 'CREDIT_CARD' },
  { label: 'Others', value: 'OTHERS' },
];
export const transactionTypeOptions = [
  { label: 'Payment', value: 'PAYMENT' },
  { label: 'Refund', value: 'REFUND' },
  { label: 'Deposit', value: 'DEPOSIT' },
  { label: 'Charge Back', value: 'CHARGE_BACK' },
];

export const transactionStatusOptions = [
  { label: 'Paid', value: 'PAID' },
  { label: 'Partially Paid', value: 'PARTIALLY_PAID' },
  { label: 'Awaiting Payment', value: 'AWAITING_PAYMENT' },
  { label: 'Failed', value: 'FAILED' },
  { label: 'Refunded', value: 'REFUNDED' },
];
export const invoiceStatusOptions = [
  { label: 'Pending Payment', value: 'PENDING' },
  { label: 'Paid', value: 'PAID' },
  { label: 'Partially Paid', value: 'PARTIALLY_PAID' },
  { label: 'Awaiting Payment', value: 'AWAITING_PAYMENT' },
  { label: 'Failed', value: 'FAILED' },
  { label: 'Refunded', value: 'REFUNDED' },
  { label: 'Completed', value: 'COMPLETED' },
  { label: 'Voided', value: 'VOIDED' },
  { label: 'Rescheduled', value: 'RESCHEDULED' },
];

export const isProductAvailableOptions = [
  {
    label: 'Link A Product',
    value: true,
  },
  {
    label: 'Continue Without A Product',
    value: false,
  },
];

export const sourceOptions = [
  {
    label: 'E-transfer',
    value: 'E-transfer',
  },
  {
    label: 'Stripe',
    value: 'Stripe',
  },
  {
    label: 'EFT',
    value: 'EFT',
  },
  {
    label: 'Cheque',
    value: 'Cheque',
  },
  {
    label: 'Direct Deposit',
    value: 'Direct-deposit',
  },
  {
    label: 'Comp',
    value: 'Comp',
  },
  {
    label: 'QuickBooks',
    value: 'QuickBooks',
  },
  {
    label: 'Other',
    value: 'Other',
  },
];

export const sessionDuration = [
  {
    label: '30',
    value: '30',
  },
  {
    label: '45',
    value: '45',
  },
  {
    label: '60',
    value: '60',
  },
  {
    label: '90',
    value: '90',
  },
  {
    label: '120',
    value: '120',
  },
];

export const dateRangeOptions = [
  {
    label: 'Today',
    value: 'today',
  },
  {
    label: 'Yesterday',
    value: 'yesterday',
  },
  {
    label: 'This week',
    value: 'this week',
  },
  {
    label: 'Last week',
    value: 'last week',
  },
  {
    label: 'Last 7 days',
    value: 'last 7 days',
  },
  {
    label: 'Last 14 days',
    value: 'last 14 days',
  },
  {
    label: 'Last 28 days',
    value: 'last 28 days',
  },
  {
    label: 'Last 30 days',
    value: 'last 30 days',
  },
  {
    label: 'Last 60 days',
    value: 'last 60 days',
  },
  {
    label: '1 year',
    value: '1 year',
  },
];
