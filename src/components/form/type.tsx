import { GoHash } from 'react-icons/go';
import { IoIosCheckboxOutline, IoMdRadioButtonOn } from 'react-icons/io';
import { MdDateRange } from 'react-icons/md';
import { TfiText } from 'react-icons/tfi';
interface QuestionType {
  id: number;
  type: string;
  heading: string;
  icon: React.ReactElement;
}

export const QuestionTypes: QuestionType[] = [
  {
    id: 1,
    type: 'Textbox',
    heading: 'Short answer',
    icon: <TfiText />,
  },
  {
    id: 2,
    type: 'TextArea',
    heading: 'Paragraph',
    icon: <TfiText />,
  },
  {
    id: 3,
    type: 'Number',
    heading: 'Number',
    icon: <GoHash />,
  },
  {
    id: 4,
    type: 'Single choice',
    heading: 'Single choice',
    icon: <IoMdRadioButtonOn />,
  },
  {
    id: 5,
    type: 'Multiple choice',
    heading: 'Multiple choice',
    icon: <IoIosCheckboxOutline />,
  },
  {
    id: 6,
    type: 'Date',
    heading: 'Date',
    icon: <MdDateRange />,
  },
];
