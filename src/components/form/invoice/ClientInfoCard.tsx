import { Box, Flex, Text } from '@chakra-ui/react';
import { FaCircleUser } from 'react-icons/fa6';

// Colors to match Wave's aesthetic
const Colors = {
  ORANGE: { PRIMARY: '#FF6200' },
  BORDER: '#E2E8F0',
  HOVER_BG: '#F7FAFC',
};

export const ClientDetails = ({ client }: { client: any }) => (
  <Box
    border="1px solid"
    borderColor={Colors.BORDER}
    borderRadius="md"
    p={4}
    w="15rem"
    textAlign="center"
  >
    <Flex align="center" gap={2} justify="center">
      <FaCircleUser size="2.5rem" color={Colors.BORDER} />
      <Text fontWeight="bold" color={Colors.ORANGE.PRIMARY}>
        {client?.display_name || 'Select Client'}
      </Text>
    </Flex>
    {client?.initial_email && (
      <Text fontSize="sm" color="gray.600" mt={2}>
        {client.initial_email}
      </Text>
    )}
  </Box>
);
