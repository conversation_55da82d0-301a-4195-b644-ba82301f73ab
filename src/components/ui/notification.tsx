'use client';

import { Box, Link, Text } from '@chakra-ui/react';
import { useState, useRef, useEffect } from 'react';
import { FiBell } from 'react-icons/fi';
//import { Button } from './button';
import { useEventNotification } from '@/hooks/notifications/useEventNotification';

import { NotificationCard } from './notificationCard';
import { MenuContent, MenuRoot, MenuTrigger } from './menu';
import { Colors } from '@/constants/colors';

import NextLink from 'next/link';

export default function NotificationBell({ userFromServer }: any) {
  const [open, setOpen] = useState(false);
  const bellRef = useRef(null) as any;

  const {
    eventNotifications,
    // dispatch,
    setReminder,
    // reminderDisclosure,
    cancelReminder,
  } = useEventNotification({
    slp: userFromServer,
  });

  const unreadNotifications = eventNotifications?.filter((notification) => {
    return notification?.unread == true;
  });
  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: any) => {
      if (bellRef.current && !bellRef.current?.contains(event.target)) {
        setOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <MenuRoot positioning={{ placement: 'bottom' }}>
      <MenuTrigger
        cursor={'pointer'}
        css={{ minWidth: 'auto' }}
        border={'none !important'}
        textDecor={'none !important'}
        boxShadow={'none'}
        outline={'none'}
        onClick={() => setOpen(!open)}
        position="relative"
        p={2}
        bg={'transparent'}
        color="gray.600"
        _hover={{ color: 'black' }}
        _focus={{ outline: 'none' }}
      >
        {/* <Button
          onClick={() => setOpen(!open)}
          position="relative"
          p={2}
          bg={'transparent'}
          color="gray.600"
          _hover={{ color: 'black' }}
          _focus={{ outline: 'none' }}
        > */}
        <FiBell size={24} />
        {unreadNotifications.length > 0 && (
          <Box
            position="absolute"
            top="-1"
            right="-1"
            display="inline-flex"
            alignItems="center"
            justifyContent="center"
            w="4"
            h="4"
            fontSize="xs"
            fontWeight="bold"
            color="white"
            bg="red.500"
            borderRadius="full"
          >
            {unreadNotifications.length}
          </Box>
        )}
        {/* </Button> */}
      </MenuTrigger>
      <MenuContent mt={'.75rem'}>
        {eventNotifications.length > 0 ? (
          <>
            <Text p={'9px'} fontWeight={'bold'}>
              Notifications
            </Text>
            <Box
              my={'2px'}
              w={'full'}
              display={'flex'}
              justifyContent={'space-between'}
              px={'9px'}
            >
              <Box>
                <Text fontWeight={'medium'} color={'black'}>
                  Earlier
                </Text>
              </Box>
              <Box>
                <Link
                  w={'100%'}
                  border={'none !important'}
                  textDecor={'none !important'}
                  boxShadow={'none'}
                  outline={'none'}
                  as={NextLink}
                  href={'/notifications'}
                  color={Colors?.ORANGE?.PRIMARY}
                  // _hover={{ bg: Colors?.BLUE?.LIGHT }}
                >
                  See all
                </Link>
              </Box>
            </Box>
          </>
        ) : (
          ''
        )}
        {eventNotifications.length > 0 ? (
          eventNotifications.map((notification) => (
            <NotificationCard
              key={notification?.id}
              notification={notification}
              setReminder={setReminder}
              cancelReminder={cancelReminder}
            />
          ))
        ) : (
          <>
            <Box
              textAlign={'center'}
              px={4}
              py={2}
              fontSize="sm"
              color="gray.400"
            >
              No Notifications for today
            </Box>
            <Box display={'flex'} justifyContent={'center'}>
              <Link
                w={'fit-content'}
                border={'none !important'}
                textDecor={'none !important'}
                boxShadow={'none'}
                outline={'none'}
                as={NextLink}
                href={'/notifications'}
                color={Colors?.ORANGE?.PRIMARY}
                fontSize="sm"
                textAlign={'center'}
              >
                View Notifications
              </Link>
            </Box>
          </>
        )}
      </MenuContent>
    </MenuRoot>
  );
}
