import { itemPerPageOptions } from '@/data/options/pagination';
import {
  Box,
  Center,
  Flex,
  HStack,
  Icon,
  Input,
  InputProps,
  Table,
  Text,
} from '@chakra-ui/react';
import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  TableOptions,
  useReactTable,
} from '@tanstack/react-table';
import React, { ReactNode, useMemo, useState } from 'react';
import { GoSortAsc, GoSortDesc } from 'react-icons/go';
import AnimateLoader from '../elements/loader/animate-loader';
import CustomSelect from '../Input/CustomSelect';
import {
  PaginationItems,
  PaginationNextTrigger,
  PaginationPrevTrigger,
  PaginationRoot,
} from '../ui/pagination';

interface CustomTableProps {
  pagination?: {
    row: number;
    page: number;
  };
  setPagination?: {
    onRowChange: (e: number) => void;
    onPageChange: (e: number) => void;
  };
  columnDef: any;
  data: any;
  filter?: {
    tableName: string;
    inputProps?: InputProps;
  };
  total?: number;
  tableOptions?: Partial<TableOptions<any>>;
  tableHeader?: ReactNode;
  NoDataText?: string | ReactNode;
  loading?: boolean;
  onRowClick?: (row: any, e: any) => void;
  excludeClickableColumns?: string[];
  minHeight?: string | number;
  enableSorting?: boolean;
  initialSorting?: SortingState;
  isSearchable?: boolean;
  searchColumn?: string;
  tbodyHoverColor?: string;
}

export default function CustomTable({
  pagination,
  setPagination,
  columnDef,
  data = [],
  tableOptions,
  tableHeader,
  total,
  NoDataText = 'No Data Found',
  loading = false,
  onRowClick,
  minHeight = '500px',
  excludeClickableColumns = ['select'],
  enableSorting = true,
  initialSorting = [],
  searchColumn,
  isSearchable,
  tbodyHoverColor = '#f9fafb',
}: CustomTableProps) {
  const [sorting, setSorting] = useState<SortingState>(initialSorting);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredData = useMemo(() => {
    if (!isSearchable || !searchColumn || !searchTerm) return data;
    return data.filter((item: any) => {
      return String(item[searchColumn])
        .toLowerCase()
        .includes(searchTerm.toLowerCase());
    });
  }, [data, isSearchable, searchColumn, searchTerm]);

  const table = useReactTable({
    columns: columnDef,
    data: filteredData,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
    onSortingChange: enableSorting ? setSorting : undefined,

    enableSorting,
    ...tableOptions,
    state: enableSorting
      ? { sorting, ...tableOptions?.state }
      : tableOptions?.state,
  });

  const handleRowClick = (row: any, e: React.MouseEvent) => {
    if (!onRowClick) return;

    const clickedCell = e.target as HTMLElement;
    const cellIndex = Array.from(e.currentTarget.children).findIndex((cell) =>
      cell.contains(clickedCell)
    );

    if (cellIndex !== -1) {
      const columnId = row.getVisibleCells()[cellIndex]?.column?.id;
      if (excludeClickableColumns.includes(columnId)) {
        return;
      }
    }

    onRowClick(row, e);
  };

  const isDataEmpty = !table.getRowModel().rows.length;
  const shouldShowPagination = Boolean(total);
  const currentRowsPerPage = itemPerPageOptions.find(
    (item) => Number(item.value) === Number(pagination?.row)
  );

  return (
    <Table.ScrollArea
      //border="1px solid #E2E8F0" // Light gray border
      //borderRadius={'md'}
      minHeight={minHeight}
      //boxShadow="md"
      //rounded="md"
      maxW={'100%'}
    >
      {tableHeader}
      {isSearchable && (
        <Box minH={'2rem'} minW={'10rem'} p={1}>
          <Input
            placeholder={`Search by ${String(searchColumn)}`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            mb={4}
            maxW="sm"
            outline={'none'}
            boxShadow={'none'}
          />
        </Box>
      )}
      <Table.Root size="lg" stickyHeader interactive variant="line">
        <Table.Header>
          {table.getHeaderGroups().map((headerGroup) => (
            <Table.Row
              transition="all 0.2s ease"
              key={headerGroup.id}
              bg="#f3f4f6"
              zIndex={0}
            >
              {headerGroup.headers.map((header) => (
                <Table.ColumnHeader
                  key={header.id}
                  fontFamily="body"
                  fontSize="md"
                  fontWeight="600"
                  textTransform="capitalize"
                  p="4"
                  borderBottom="1px solid #e5e7eb"
                >
                  {header.isPlaceholder ? null : (
                    <Flex
                      gap=".5rem"
                      cursor={
                        enableSorting && header.column.getCanSort()
                          ? 'pointer'
                          : 'default'
                      }
                      onClick={
                        enableSorting
                          ? header.column.getToggleSortingHandler()
                          : undefined
                      }
                    >
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      {enableSorting &&
                        {
                          asc: <Icon size={'lg'} as={GoSortAsc} />,
                          desc: <Icon size={'lg'} as={GoSortDesc} />,
                        }[header.column.getIsSorted() as string]}
                    </Flex>
                  )}
                </Table.ColumnHeader>
              ))}
            </Table.Row>
          ))}
        </Table.Header>

        <Table.Body>
          {loading || isDataEmpty ? (
            <Table.Row>
              <Table.Cell colSpan={columnDef.length} border="transparent">
                <Center h="20rem">
                  {loading ? (
                    <AnimateLoader />
                  ) : typeof NoDataText === 'string' ? (
                    <Text fontSize="1.2rem" fontWeight={500} color="gray.500">
                      {NoDataText}
                    </Text>
                  ) : (
                    NoDataText
                  )}
                </Center>
              </Table.Cell>
            </Table.Row>
          ) : (
            table.getRowModel().rows.map((row) => (
              <Table.Row
                key={row.id}
                onClick={(e) => handleRowClick(row, e)}
                cursor={onRowClick ? 'pointer' : 'default'}
                _hover={{
                  bg: onRowClick ? 'primary.50' : tbodyHoverColor,
                  transform: onRowClick ? 'translateY(-1px)' : 'none',
                  boxShadow: onRowClick ? 'sm' : 'none',
                }}
              >
                {row.getVisibleCells().map((cell) => (
                  <Table.Cell
                    key={cell.id}
                    py="5"
                    px="4"
                    borderBottom="1px solid #e5e7eb"
                    fontSize={'md'}
                    onClick={(e) => {
                      if (excludeClickableColumns.includes(cell.column.id)) {
                        e.stopPropagation();
                      }
                    }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </Table.Cell>
                ))}
              </Table.Row>
            ))
          )}
        </Table.Body>
      </Table.Root>

      {shouldShowPagination && (
        <Flex
          justifyContent={{ base: 'flex-start', md: 'flex-end' }}
          alignItems={{ base: 'flex-start', md: 'flex-end' }}
          flexDir={{ base: 'column', md: 'row' }}
          gap="4"
          pb="12"
          pt="4"
        >
          <CustomSelect
            placeholder="Select Page..."
            options={itemPerPageOptions}
            onChange={(val) => {
              setPagination?.onPageChange(1);
              setPagination?.onRowChange(Number(val.value));
            }}
            defaultValue={currentRowsPerPage}
            controlStyle={{
              border: '1px solid #4F4F4F',
              width: { base: 'full', md: '13rem' },
            }}
            label="Rows per page:"
          />
          <PaginationRoot
            count={total || 20}
            pageSize={pagination?.row}
            page={pagination?.page}
            defaultPage={1}
            w="fit-content"
            onPageChange={(e) => setPagination?.onPageChange(e.page)}
          >
            <HStack wrap="wrap">
              <PaginationPrevTrigger />
              <PaginationItems />
              <PaginationNextTrigger />
            </HStack>
          </PaginationRoot>
        </Flex>
      )}
    </Table.ScrollArea>
  );
}
