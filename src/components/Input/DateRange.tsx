import React, { useState, useRef, useEffect } from 'react';
import { Box, HStack, Text, Input, Icon } from '@chakra-ui/react';
import { MdCalendarToday } from 'react-icons/md';

interface DateRangeProps {
  fromValue?: string;
  toValue?: string;
  onFromChange?: (value: string) => void;
  onToChange?: (value: string) => void;
  onDateRangeChange?: (fromValue: string, toValue: string) => void;
  fromPlaceholder?: string;
  toPlaceholder?: string;
  height?: string;
  borderRadius?: string;
  border?: string;
  disabled?: boolean;
}

export default function DateRange({
  fromValue = '',
  toValue = '',
  onDateRangeChange,
  fromPlaceholder = 'From',
  toPlaceholder = 'To',
  height = '2.5rem',
  borderRadius = '0.5rem',
  border = '1px solid #636D79',
  disabled = false,
}: DateRangeProps) {
  // Local state for managing date values
  const [localFromValue, setLocalFromValue] = useState(fromValue);
  const [localToValue, setLocalToValue] = useState(toValue);
  const [showFromPicker, setShowFromPicker] = useState(false);
  const [showToPicker, setShowToPicker] = useState(false);

  // Refs for the hidden date inputs
  const fromInputRef = useRef<HTMLInputElement>(null);
  const toInputRef = useRef<HTMLInputElement>(null);

  // Update local state when props change
  useEffect(() => {
    setLocalFromValue(fromValue);
  }, [fromValue]);

  useEffect(() => {
    setLocalToValue(toValue);
  }, [toValue]);

  // Helper function to check if a date string is valid
  const isValidDate = (dateString: string): boolean => {
    if (!dateString) return false;
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  };

  // Helper function to format date for display
  const formatDateForDisplay = (dateString: string): string => {
    if (!isValidDate(dateString)) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Handle from date change with validation
  const handleFromDateChange = (value: string) => {
    setLocalFromValue(value);

    // If to date exists and from date is after to date, clear to date
    if (localToValue && isValidDate(value) && isValidDate(localToValue)) {
      const fromDate = new Date(value);
      const toDate = new Date(localToValue);
      if (fromDate > toDate) {
        setLocalToValue('');
      }
    }

    // Call onDateRangeChange only if both dates are valid
    if (isValidDate(value) && isValidDate(localToValue)) {
      onDateRangeChange?.(value, localToValue);
    }
    if (!value && !localToValue) {
      onDateRangeChange?.('', '');
    }
  };

  // Handle to date change with validation
  const handleToDateChange = (value: string) => {
    setLocalToValue(value);

    // If from date exists and to date is before from date, clear from date
    if (localFromValue && isValidDate(value) && isValidDate(localFromValue)) {
      const fromDate = new Date(localFromValue);
      const toDate = new Date(value);
      if (toDate < fromDate) {
        setLocalFromValue('');
      }
    }

    // Call onDateRangeChange only if both dates are valid
    if (isValidDate(localFromValue) && isValidDate(value)) {
      onDateRangeChange?.(localFromValue, value);
    }
    if (!localFromValue && !value) {
      onDateRangeChange?.('', '');
    }
  };

  // Calculate min/max dates for validation
  const getMinDate = (isToDate: boolean): string => {
    if (isToDate && isValidDate(localFromValue)) {
      return localFromValue;
    }
    return '';
  };

  const getMaxDate = (isFromDate: boolean): string => {
    if (isFromDate && isValidDate(localToValue)) {
      return localToValue;
    }
    return '';
  };

  return (
    <HStack
      gap={{ base: '2', md: '3' }}
      align="center"
      width="100%"
      flexWrap={{ base: 'wrap', md: 'nowrap' }}
    >
      {/* From Date */}
      <Box
        position="relative"
        width={{ base: 'calc(50% - 8px)', md: '150px' }}
        minW={{ base: '120px', md: '150px' }}
        flex={{ base: '1', md: 'none' }}
      >
        {/* Display Box */}
        <Box
          height={height}
          borderRadius={borderRadius}
          border={border}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          px={{ base: '2', md: '3' }}
          cursor={disabled ? 'not-allowed' : 'pointer'}
          bg={disabled ? 'gray.100' : 'white'}
          _hover={{
            borderColor: disabled ? undefined : 'gray.400',
          }}
          onClick={() => {
            if (!disabled) {
              setShowFromPicker(!showFromPicker);
              fromInputRef.current?.focus();
            }
          }}
        >
          <Text
            fontSize={{ base: 'xs', md: 'sm' }}
            color={localFromValue ? 'black' : 'gray.500'}
            flex="1"
            overflow="hidden"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
          >
            {localFromValue
              ? formatDateForDisplay(localFromValue)
              : fromPlaceholder}
          </Text>
          <Icon
            as={MdCalendarToday}
            color="gray.400"
            boxSize={{ base: '3', md: '4' }}
          />
        </Box>

        {/* Hidden Date Input */}
        <Input
          ref={fromInputRef}
          type="date"
          value={localFromValue}
          onChange={(e) => handleFromDateChange(e.target.value)}
          position="absolute"
          top="0"
          left="0"
          width="100%"
          height="100%"
          opacity="0"
          pointerEvents="none"
          disabled={disabled}
          min={getMinDate(false)}
          max={getMaxDate(true)}
        />
      </Box>

      {/* To Date */}
      <Box
        position="relative"
        width={{ base: 'calc(50% - 8px)', md: '150px' }}
        minW={{ base: '120px', md: '150px' }}
        flex={{ base: '1', md: 'none' }}
      >
        {/* Display Box */}
        <Box
          height={height}
          borderRadius={borderRadius}
          border={border}
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          px={{ base: '2', md: '3' }}
          cursor={disabled ? 'not-allowed' : 'pointer'}
          bg={disabled ? 'gray.100' : 'white'}
          _hover={{
            borderColor: disabled ? undefined : 'gray.400',
          }}
          onClick={() => {
            if (!disabled) {
              setShowToPicker(!showToPicker);
              toInputRef.current?.focus();
            }
          }}
        >
          <Text
            fontSize={{ base: 'xs', md: 'sm' }}
            color={localToValue ? 'black' : 'gray.500'}
            flex="1"
            overflow="hidden"
            textOverflow="ellipsis"
            whiteSpace="nowrap"
          >
            {localToValue ? formatDateForDisplay(localToValue) : toPlaceholder}
          </Text>
          <Icon
            as={MdCalendarToday}
            color="gray.400"
            boxSize={{ base: '3', md: '4' }}
          />
        </Box>

        {/* Hidden Date Input */}
        <Input
          ref={toInputRef}
          type="date"
          value={localToValue}
          onChange={(e) => handleToDateChange(e.target.value)}
          position="absolute"
          top="0"
          left="0"
          width="100%"
          height="100%"
          opacity="0"
          pointerEvents="none"
          disabled={disabled}
          min={getMinDate(true)}
          max={getMaxDate(false)}
        />
      </Box>
    </HStack>
  );
}
