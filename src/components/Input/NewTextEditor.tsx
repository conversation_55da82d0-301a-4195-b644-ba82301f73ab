/* eslint-disable react-hooks/exhaustive-deps */

'use client';

import Underline from '@tiptap/extension-underline';
import { EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import React, { useCallback, useEffect, useState } from 'react';
//import Code from '@tiptap/extension-code';
import ReplacementTags from '@/app/(dashboard)/admin/template/ReplacementTags';
import SoapNotesTemplate from '@/app/(dashboard)/admin/template/SoapNotesTemplate';
import { ITemplates, ReplacementTagsType } from '@/shared/interface/templates';
import { Heading } from '@chakra-ui/react';
import IPAPicker from './IPAPicker';

type TTextEditor = {
  initialContent: any;
  saveContent?: (html: string) => void;
  section?: any;
  initialPresent?: boolean;
  height?: string;
  showIPAPicker?: boolean;
  readOnly?: boolean;
  clearTrigger?: number;
  replacementTags?: Array<ReplacementTagsType>;
  handleReplacementTag?: (value: string) => void;
  replaceTags?: (value: string, callback: (value: string) => void) => void;
  templates?: ITemplates[];
  header?: any;
};

export default function TextEditorNew({
  initialContent,
  saveContent,
  section,
  initialPresent,
  height = '200px',
  showIPAPicker = false,
  readOnly = false,
  clearTrigger = 0,
  replacementTags = [],
  handleReplacementTag,
  replaceTags,
  templates,
  header,
}: TTextEditor) {
  const [showPreview] = useState(false);

  const editor = useEditor({
    extensions: [StarterKit, Underline],
    immediatelyRender: false,
    content: initialContent || '',
    onUpdate: ({ editor }) => {
      if (saveContent) {
        saveContent(editor.getHTML());
      }
    },
    editorProps: {
      attributes: {
        style: `min-height: 50px; outline: none; max-width: 750px;`,
      },
    },
  });

  // Handle initialPresent changes
  useEffect(() => {
    if (editor && initialPresent !== undefined) {
      editor.commands.setContent(initialContent || '');
    }
  }, [initialPresent]);

  // // Handle clear trigger
  useEffect(() => {
    if (clearTrigger === 0) return;
    if (editor) {
      editor.commands.clearContent();
      if (saveContent) saveContent('');
    }
  }, [clearTrigger]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (editor) {
        editor.destroy();
      }
    };
  }, [editor]);

  function stripBlockTags(html: string): string {
    return html
      .replace(/<\/?(p|div|h[1-6]|section|article|blockquote|br)[^>]*>/gi, '')
      .trim();
  }

  // Handle inserting text/HTML content
  const handleInsertText = useCallback(
    (html: string) => {
      if (editor) {
        editor.commands.focus();
        editor.commands.insertContent(html);
      }
    },
    [editor]
  );

  // Handle editor container click to focus
  const handleEditorContainerClick = useCallback(
    (event: React.MouseEvent) => {
      if (editor && !readOnly) {
        // Check if the click is on the editor content area or empty space
        const target = event.target as HTMLElement;
        const editorElement = target.closest('.tiptap-editor');

        if (editorElement) {
          editor.commands.focus();

          // If clicking on empty space, move cursor to end
          if (
            target.classList.contains('tiptap-editor') ||
            target.classList.contains('ProseMirror')
          ) {
            // Small delay to ensure focus is set first
            setTimeout(() => {
              editor.commands.focus('end');
            }, 10);
          }
        }
      }
    },
    [editor, readOnly]
  );

  // Toolbar button handlers
  const toggleBold = useCallback(() => {
    editor?.chain().focus().toggleBold().run();
  }, [editor]);

  const toggleItalic = useCallback(() => {
    editor?.chain().focus().toggleItalic().run();
  }, [editor]);

  const toggleUnderline = useCallback(() => {
    editor?.chain().focus().toggleUnderline().run();
  }, [editor]);

  const toggleCode = useCallback(() => {
    editor?.chain().focus().toggleCode().run();
  }, [editor]);

  const toggleBulletList = useCallback(() => {
    editor?.chain().focus().toggleBulletList().run();
  }, [editor]);

  const toggleOrderedList = useCallback(() => {
    editor?.chain().focus().toggleOrderedList().run();
  }, [editor]);

  const setHeading = useCallback(
    (level: 1 | 2 | 3) => {
      editor?.chain().focus().toggleHeading({ level }).run();
    },
    [editor]
  );

  const setParagraph = useCallback(() => {
    editor?.chain().focus().setParagraph().run();
  }, [editor]);

  const undo = useCallback(() => {
    editor?.chain().focus().undo().run();
  }, [editor]);

  const redo = useCallback(() => {
    editor?.chain().focus().redo().run();
  }, [editor]);

  // Check if current selection has formatting
  const isActive = useCallback(
    (name: string, attrs?: any) => {
      return editor?.isActive(name, attrs) || false;
    },
    [editor]
  );

  const canUndo = editor?.can().undo() || false;
  const canRedo = editor?.can().redo() || false;

  // Get current block type for dropdown
  const getCurrentBlockType = useCallback(() => {
    if (isActive('heading', { level: 1 })) return 'header-one';
    if (isActive('heading', { level: 2 })) return 'header-two';
    if (isActive('heading', { level: 3 })) return 'header-three';
    return 'unstyled';
  }, [isActive]);

  const handleBlockTypeChange = useCallback(
    (blockType: string) => {
      switch (blockType) {
        case 'header-one':
          setHeading(1);
          break;
        case 'header-two':
          setHeading(2);
          break;
        case 'header-three':
          setHeading(3);
          break;
        case 'unstyled':
        default:
          setParagraph();
          break;
      }
    },
    [setHeading, setParagraph]
  );

  const editorStyles = `
    .modern-editor-container {
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      background: white;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }
    
    .modern-toolbar {
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      flex-wrap: wrap;
    }
    
    .toolbar-group {
      display: flex;
      align-items: center;
      gap: 2px;
      margin-right: 8px;
      padding-right: 8px;
      border-right: 1px solid #e2e8f0;
    }
    
    .toolbar-group:last-child {
      border-right: none;
      margin-right: 0;
    }
    
    .modern-style-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border: none;
      background: transparent;
      color: #64748b;
      cursor: pointer;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.15s ease;
      position: relative;
      outline: none;
    }
    
    .modern-style-button:hover {
      background: #e2e8f0;
      color: #475569;
    }
    
    .modern-style-button.active {
      background: #E97A5B !important;
      color: white;
    }
    
    .modern-style-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .tiptap-editor {
    padding: 16px;
    min-height: ${height};
    max-height: 600px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    outline: none;
  }
    
    .tiptap-editor a {
      color: #E97A5B;
      text-decoration: underline;
      cursor: pointer;
    }
    
    .tiptap-editor .tiptap-code {
      background-color: rgba(99, 102, 241, 0.1);
      font-family: "JetBrains Mono", "Fira Code", "Consolas", monospace;
      font-size: 13px;
      padding: 2px 4px;
      border-radius: 3px;
      color: #6366f1;
    }
    
    .tiptap-editor p.is-editor-empty:first-child::before {
      content: attr(data-placeholder);
      float: left;
      color: #94a3b8;
      pointer-events: none;
      height: 0;
    }
    
    .tiptap-editor h1 {
      font-size: 2em;
      font-weight: bold;
      margin: 0.67em 0;
    }
    
    .tiptap-editor h2 {
      font-size: 1.5em;
      font-weight: bold;
      margin: 0.83em 0;
    }
    
    .tiptap-editor h3 {
      font-size: 1.17em;
      font-weight: bold;
      margin: 1em 0;
    }
    
    .tiptap-editor ul {
      padding-left: 1.5em;
      margin: 1em 0;
    }
    
    .tiptap-editor ol {
      padding-left: 1.5em;
      margin: 1em 0;
    }
    
    .tiptap-editor li {
      margin: 0.25em 0;
    }

      .tiptap-editor em {
      font-style: italic;
    }

    .block-type-dropdown {
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      padding: 4px 8px;
      font-size: 13px;
      color: #E97A5B;
      cursor: pointer;
      margin-right: 8px;
      outline: none;
    }

    .block-type-dropdown:hover {
      background: #f8fafc;
    }

    .html-preview {
      padding: 16px;
      min-height: ${height};
      max-height: 600px;
      overflow-y: auto;
      font-size: 14px;
      line-height: 1.6;
    }

    .html-preview a {
      color: #E97A5B;
      text-decoration: underline;
      cursor: pointer;
    }
  `;

  const BLOCK_TYPES = [
    { label: 'Normal', style: 'unstyled' },
    { label: 'H1', style: 'header-one' },
    { label: 'H2', style: 'header-two' },
    { label: 'H3', style: 'header-three' },
  ];

  // For direct HTML rendering
  const HtmlPreview = React.memo(() => {
    return (
      <div
        className="html-preview"
        dangerouslySetInnerHTML={{ __html: initialContent }}
      />
    );
  });
  HtmlPreview.displayName = 'HtmlPreview';

  if (!editor) {
    return <div>Loading editor...</div>;
  }

  return (
    <>
      <style>{editorStyles}</style>
      <div>
        {/* Replacement Tags */}
        {replacementTags?.length ? (
          <ReplacementTags
            replacementTags={replacementTags}
            onClick={(data) => {
              handleInsertText(data);
              if (handleReplacementTag) {
                handleReplacementTag(data);
              }
            }}
          />
        ) : null}

        {/* SOAP Notes Template */}
        {templates?.length && showIPAPicker ? (
          <SoapNotesTemplate
            templates={templates}
            onClick={(data) => {
              if (replaceTags) {
                replaceTags(data, (value) => {
                  const inlineContent = stripBlockTags(value);
                  handleInsertText(inlineContent);
                  //handleInsertText(value);
                });
              }
            }}
          />
        ) : null}

        {/* IPA Picker */}
        {showIPAPicker ? <IPAPicker onClick={handleInsertText} /> : null}

        {/* Section Header */}
        {section === 'slp' && <Heading mb={1}>Session Notes</Heading>}

        {/* Custom Header */}
        {header}

        <div
          className="modern-editor-container"
          onClick={handleEditorContainerClick}
        >
          {/* Toolbar */}
          {!readOnly && (
            <div className="modern-toolbar">
              {/* Block Type Dropdown */}
              <select
                className="block-type-dropdown"
                value={getCurrentBlockType()}
                onChange={(e) => handleBlockTypeChange(e.target.value)}
              >
                {BLOCK_TYPES.map((type) => (
                  <option key={type.style} value={type.style}>
                    {type.label}
                  </option>
                ))}
              </select>

              {/* Inline Style Controls */}
              <div className="toolbar-group">
                <button
                  type="button"
                  className={`modern-style-button ${isActive('bold') ? 'active' : ''}`}
                  onClick={toggleBold}
                  title="Bold"
                >
                  B
                </button>
                <button
                  type="button"
                  className={`modern-style-button ${isActive('italic') ? 'active' : ''}`}
                  onClick={toggleItalic}
                  title="Italic"
                >
                  I
                </button>
                <button
                  type="button"
                  className={`modern-style-button ${isActive('underline') ? 'active' : ''}`}
                  onClick={toggleUnderline}
                  title="Underline"
                >
                  U
                </button>
                <button
                  type="button"
                  className={`modern-style-button ${isActive('code') ? 'active' : ''}`}
                  onClick={toggleCode}
                  title="Code"
                >
                  &lt;/&gt;
                </button>
              </div>

              {/* List Controls */}
              <div className="toolbar-group">
                <button
                  type="button"
                  className={`modern-style-button ${isActive('bulletList') ? 'active' : ''}`}
                  onClick={toggleBulletList}
                  title="Bullet List"
                >
                  •
                </button>
                <button
                  type="button"
                  className={`modern-style-button ${isActive('orderedList') ? 'active' : ''}`}
                  onClick={toggleOrderedList}
                  title="Numbered List"
                >
                  1.
                </button>
              </div>

              {/* Undo/Redo Controls */}
              <div className="toolbar-group">
                <button
                  type="button"
                  className="modern-style-button"
                  onClick={undo}
                  title="Undo"
                  disabled={!canUndo}
                >
                  ↶
                </button>
                <button
                  type="button"
                  className="modern-style-button"
                  onClick={redo}
                  title="Redo"
                  disabled={!canRedo}
                >
                  ↷
                </button>
              </div>
            </div>
          )}

          {/* Editor Content */}
          {showPreview ? (
            <HtmlPreview />
          ) : (
            <EditorContent
              editor={editor}
              className="tiptap-editor"
              //data-placeholder="Start typing here..."
            />
          )}
        </div>
      </div>
    </>
  );
}
