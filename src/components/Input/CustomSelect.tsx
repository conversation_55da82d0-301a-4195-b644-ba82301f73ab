'use client';
import React from 'react';
// import Select from 'react-select';
import { Flex, Icon, Text, useBreakpointValue } from '@chakra-ui/react';
import CreatableSelect from 'react-select/creatable';
import { FaAsterisk } from 'react-icons/fa';
import { FormFieldError } from './FormFieldErrors';
// import { GroupBase } from 'react-select';

// type MyCreatableSelectProps = CreatableProps<any, false, GroupBase<any>>;

interface CustomSelectProps {
  options: Array<any> | undefined;
  value?: any;
  onChange: (value: { label: any; value: any } | any) => void;
  onCreateOption?: (value: any) => void;
  placeholder?: string;
  label?: string;
  isDisabled?: boolean;
  selectedOption?: any;
  setSelectedOption?: any;
  defaultValue?: any;
  controlStyle?: any;
  isMulti?: boolean;
  required?: boolean;
  showRequired?: boolean;
  touched?: Record<string, unknown>;
  errors?: Record<string, unknown>;
  name?: string;
  isClearable?: boolean;
  showNone?: boolean;
  border?: string;
  optionStyle?: any;
  components?: any;
  NoneLabel?: string;
}

export default function CustomSelect({
  options = [],
  onChange,
  placeholder,
  isDisabled,
  label,
  selectedOption,
  defaultValue,
  onCreateOption,
  controlStyle,
  isMulti,
  required,
  showRequired,
  name,
  errors,
  touched,
  isClearable,
  showNone = false,
  optionStyle,
  border = '1px solid #636D79',
  NoneLabel = 'None',
  ...rest
}: CustomSelectProps) {
  // const [selectedValue, setSelectedValue] = useState(value);
  const isDesktop = useBreakpointValue({ base: false, lg: true });
  const optionClone = [
    ...(showNone
      ? [
          {
            value: '',
            label: NoneLabel,
          },
        ]
      : []),
    ...options,
  ];
  const handleSelectChange = (selectedOption: { label: any; value: any }) => {
    // setSelectedValue(selectedOption);
    onChange(selectedOption); // Pass value to parent component
  };

  // console.log('selected Option', selectedOption);
  return (
    <div>
      {label && (
        <Flex alignItems={'center'}>
          <Text
            fontWeight={'500'}
            fontSize={'md'}
            mb={'0.375rem'}
            // color={'#474D66'}
          >
            {label}
          </Text>
          {(showRequired || required) && (
            <Icon ml={'.3rem'} mb={'.4rem'} boxSize={'.4rem'} color={'red.400'}>
              <FaAsterisk />
            </Icon>
          )}
        </Flex>
      )}
      <CreatableSelect
        id="select-custom"
        name={name}
        isMulti={isMulti}
        value={selectedOption}
        defaultValue={defaultValue}
        onChange={handleSelectChange}
        options={optionClone}
        placeholder={placeholder || 'Select an option'}
        isDisabled={isDisabled || false}
        onCreateOption={onCreateOption}
        isValidNewOption={() => false}
        required={required}
        styles={{
          control: (base) => ({
            ...base,
            width: '100%',
            minHeight: `${isDesktop ? '2.5rem' : '2.5rem'}`,
            borderRadius: `.5rem`,
            fontSize: `${isDesktop ? '1rem' : '1rem'}`,
            border: border,
            // zIndex: 1,
            ...controlStyle,
          }),
          placeholder: (base) => ({
            ...base,
            fontSize: '0.85rem',
            color: base.color,
          }),
          option: (base) => ({
            ...base,
            ...optionStyle,
          }),
        }}
        isClearable={isClearable}
        {...rest}
      />
      <FormFieldError errors={errors} touched={touched} name={name} />
    </div>
  );
}
