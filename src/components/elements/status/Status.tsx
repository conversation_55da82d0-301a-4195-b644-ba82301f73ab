import { Box, BoxProps } from '@chakra-ui/react';
import React, { useCallback } from 'react';
interface ColorScheme {
  bg: string;
  text: string;
  border?: string;
  dotColor?: string;
}

const getColor = (name: string | null): ColorScheme => {
  switch (name?.toLowerCase()) {
    case 'completed':
    case 'complete':
    case 'created':
    case 'paid':
    case 'redeemed':
      return {
        bg: '#ecfdf5',
        text: '#047857',
        border: '#a7f3d0',
        dotColor: '#34d399',
      };

    case 'incompleted':
    case 'incomplete':
    case 'partially_paid':
    case 'unredeemed':
      return {
        bg: '#fef3c7',
        text: '#92400e',
        border: '#fde68a',
        dotColor: '#fbbf24',
      };

    case 'partially refunded':
      return {
        bg: '#fef2f2',
        text: '#991b1b',
        border: '#fecaca',
        dotColor: '#ef4444',
      };

    case 'void':
    case 'awaiting_payment':
      return {
        bg: '#f9fafb',
        text: '#374151',
        border: '#d1d5db',
        dotColor: '#6b7280',
      };

    case 'refunded':
    case 'refund':
      return {
        bg: '#fef2f2',
        text: '#991b1b',
        border: '#fecaca',
        dotColor: '#ef4444',
      };

    case 'active':
    case 'actived':
      return {
        bg: '#eff6ff',
        text: '#1d4ed8',
        border: '#bfdbfe',
        dotColor: '#3b82f6',
      };

    case 'inactive':
      return {
        bg: '#f3f4f6',
        text: '#4b5563',
        border: '#d1d5db',
        dotColor: '#9ca3af',
      };

    case 'failed':
    case 'deleted':
    case 'flagged':
    case 'cancelled':
    case 'null':
      return {
        bg: '#fef2f2',
        text: '#dc2626',
        border: '#fecaca',
        dotColor: '#ef4444',
      };

    case 'inprogress':
      return {
        bg: '#fff7ed',
        text: '#c2410c',
        border: '#fed7aa',
        dotColor: '#fb923c',
      };

    default:
      return {
        bg: '#f9fafb',
        text: '#374151',
        border: '#e5e7eb',
        dotColor: '#9ca3af',
      };
  }
};

export default function Status({
  name,
  isBorder = true,
  isDot = true,
  ...props
}: { name?: string; isBorder?: boolean; isDot?: boolean } & BoxProps) {
  const colorKey = name?.toLowerCase() ?? 'null'; // use 'null' as key for empty
  const formattedName = name ? name.toUpperCase().replaceAll('_', ' ') : 'VOID'; // or 'NULL' if you prefer
  const colorScheme = useCallback(() => getColor(colorKey), [colorKey]);
  return (
    <Box
      display={'flex'}
      alignItems={'center'}
      justifyContent={'center'}
      rounded={'full'}
      gapX={'3'}
      border={isBorder ? '1px solid' : ''}
      borderColor={colorScheme().border}
      fontWeight={'600'}
      fontSize={'12px'}
      w={'fit'}
      px={'3'}
      title={formattedName}
      bg={colorScheme().bg}
      color={colorScheme().text}
      {...props}
    >
      {isDot && (
        <Box bg={colorScheme().dotColor} w={'2'} h={'2'} rounded={'full'} />
      )}
      {formattedName}
    </Box>
    // <Center
    //   bg={getColorFun()?.bg}
    //   rounded={'1rem'}
    //   py={'.15rem'}
    //   s
    //   px={'.2rem'}
    //   fontWeight={400}
    //   fontSize={'.8rem'}
    //   color={getColorFun()?.text}
    //   {...props}
    // >
    //   {formattedName}
    // </Center>
  );
}
