import { useGetAllClientsQuery } from '@/api/clients/get-all-clients';
import CustomSelect from '@/components/Input/CustomSelect';
import { Box } from '@chakra-ui/react';
import React, { useEffect, useMemo } from 'react';

const SelectContact = ({
  label,
  handleSelectClient,
  onReady,
  showNone = false,
  selectedClient,
  NoneLabel = 'None',
}: any) => {
  const [initialLoad, setInitialLoad] = React.useState(true);
  const { data: Clients } = useGetAllClientsQuery(
    { size: 1000 },
    'link-client',
    {
      enabled: true,
    }
  );
  const contactOptions = useMemo(() => {
    return Clients?.data?.map((item: any) => ({
      label: item?.display_name,
      value: item,
    }));
  }, [Clients]);

  useEffect(() => {
    if (contactOptions?.length && onReady && initialLoad) {
      onReady?.(contactOptions);
      setInitialLoad(false);
    }
  }, [contactOptions, initialLoad, onReady]);

  return (
    <div>
      <Box>
        <CustomSelect
          placeholder="Select Client"
          options={contactOptions}
          onChange={(option: any) => {
            handleSelectClient(option.value);
          }}
          label={label}
          showNone={showNone}
          NoneLabel={NoneLabel}
          value={contactOptions?.find(
            (item: any) => item.value.id === selectedClient
          )}
        />
      </Box>
    </div>
  );
};

export default SelectContact;
