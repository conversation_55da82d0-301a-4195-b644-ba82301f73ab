/* eslint-disable jsx-a11y/alt-text */
'use client';
import soapLogo from '@/assets/soapLogo.png';
import {
  Document,
  Image,
  Page,
  StyleSheet,
  Text,
  View,
} from '@react-pdf/renderer';
import moment from 'moment';
import { formatMoney } from '../format-money/FormatMoney';

export type TPDF = {
  name: string;
  email: string;
  invoice?: any;
  receiptNumber: string;
  date: string;
  dueDate: string;
  activity: string;
  memo: string;
  quantity: number;
  rate: number;
  balance: number;
  amountDue: number;
  transactions: any[];
  referral?: string;
};

export function PDFGenerator({
  name = '<PERSON><PERSON>',
  email = '<PERSON><EMAIL>',
  receiptNumber = '11004',
  date = '2024.11.20',
  dueDate = '2024.11.20',
  activity = 'Christina Hii - Assessment Report (2/2) 0.5 Hour Speech & Language Assessment  - Christina Hii - Speech-Language  Pathologist Reg. CSHBC #2887 (British Columbia)',
  memo = '',
  quantity = 1,
  rate = 130.0,
  // balance = 0.0,
  amountDue = 0.0,
  referral = '',
  invoice,
  transactions = [],
}: TPDF) {
  const raw = localStorage.getItem('UserState');
  const data = raw ? JSON.parse(raw) : null;
  const org = data?.UserState?.organization;
  console.log('invoice >>>>', invoice);

  // Function to calculate individual tax amounts from invoice items
  const calculateIndividualTaxes = () => {
    if (!invoice?.invoice_items) return [];

    const taxMap = new Map();

    invoice.invoice_items.forEach((item: any) => {
      if (item.taxes && Array.isArray(item.taxes)) {
        const itemSubtotal = Number(item.price) * Number(item.quantity || 1);

        item.taxes.forEach((tax: any) => {
          const taxId = tax.id;
          // Handle different tax data structures
          const taxName = tax.name || tax.tax_name || `Tax ${taxId}`;
          const taxRate = Number(tax.value || tax.tax_value || tax.rate) || 0;
          const taxAmount = (taxRate / 100) * itemSubtotal;

          if (taxMap.has(taxId)) {
            const existingTax = taxMap.get(taxId);
            existingTax.amount += taxAmount;
          } else {
            taxMap.set(taxId, {
              id: taxId,
              name: taxName,
              rate: taxRate,
              amount: taxAmount,
            });
          }
        });
      }
    });

    return Array.from(taxMap.values()).filter((tax) => tax.amount > 0);
  };

  const individualTaxes = calculateIndividualTaxes();
  console.log('billing_info', org?.billing_info);
  console.log('org', org);

  return (
    <Document>
      <Page size="A4" style={styles.container}>
        <View style={styles.header}>
          <View>
            {org?.logo_url ? (
              <Image src={org?.logo_url || soapLogo.src} style={styles.logo} />
            ) : (
              <Text>{org?.name}</Text>
            )}
            {/* <Image src={org?.logo_url || soapLogo.src} style={styles.logo} /> */}
          </View>

          <View style={styles.invoiceHeader}>
            <Text style={styles.title}>INVOICE</Text>
            <Text style={styles.orgName}>
              {org?.id === 1 ? 'Speak Fluent Inc.' : org?.name}
            </Text>
            {/* {organization?.id === 1 && (
              <>
                <Text style={styles.text}>390 Cherry St</Text>
                <Text style={styles.text}>Toronto ON M5A 0E2</Text>
                <Text style={styles.text}>+16473710880</Text>
                <Text style={styles.text}><EMAIL></Text>
                <Text style={styles.text}>www.speakfluent.ca</Text>
                <Text style={styles.text}>
                  GST/HST Registration No.: 783274905RT0001
                </Text>
              </>
            )} */}
            <Text style={styles.billingInfo}>
              {org?.id === 1
                ? `390 Cherry St
                   Toronto ON M5A 0E2
                   +16473710880
                   <EMAIL>
                   www.speakfluent.ca
                   GST/HST Registration No.: 783274905RT0001`
                : org?.billing_info
                  ? `
                ${org?.billing_info?.address || ''}
                ${org?.billing_info?.state?.name || ''} ${org?.billing_info?.city?.name || ''}
                ${org?.billing_info?.country?.name || ''}

                ${org?.billing_info?.phone || ''}
                ${org?.billing_info?.website || ''}
                `
                  : ''}
            </Text>
          </View>
        </View>
        <View style={styles.divider}></View>

        {/* <Text style={styles.receiptTitle}>SALES RECEIPT</Text> */}

        <View style={styles.billToSection}>
          <View style={styles.billToBox}>
            <Text style={styles.sectionTitle}>Bill to</Text>
            <Text style={[styles.text, { fontWeight: 'bold' }]}>{name}</Text>

            {!['null', null].includes(email) ? (
              <Text
                style={[
                  styles.text,
                  { marginTop: '10px' },
                  { color: '#2f3438' },
                ]}
              >
                {email}
              </Text>
            ) : (
              ''
            )}
          </View>

          <View style={styles.invoiceDetailsContainer}>
            <View style={styles.invoiceDetailRow}>
              <Text style={styles.invoiceDetailLabel}>Invoice Number:</Text>
              <Text style={styles.invoiceDetailValue}>{receiptNumber}</Text>
            </View>
            <View style={styles.invoiceDetailRow}>
              <Text style={styles.invoiceDetailLabel}>Invoice Date:</Text>
              <Text style={styles.invoiceDetailValue}>{date}</Text>
            </View>
            <View style={styles.invoiceDetailRow}>
              <Text style={styles.invoiceDetailLabel}>Payment Due:</Text>
              <Text style={styles.invoiceDetailValue}>{dueDate}</Text>
            </View>
            <View
              style={[styles.invoiceDetailRow, { backgroundColor: '#f2f4f7' }]}
            >
              <Text style={styles.invoiceDetailLabel}>Amount Due:</Text>
              <Text style={styles.invoiceDetailValue}>
                {/* ${amountDue?.toFixed(2)} */}
                {formatMoney(amountDue)}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.tableHeader}>
          <Text style={[styles.tableHeaderCell, { flex: 3 }]}>Items</Text>
          <Text style={[styles.tableHeaderCell, { textAlign: 'center' }]}>
            Quantity
          </Text>
          <Text style={[styles.tableHeaderCell, { textAlign: 'center' }]}>
            Price
          </Text>
          <Text style={[styles.tableHeaderCell, { textAlign: 'center' }]}>
            Amount
          </Text>
        </View>

        {invoice?.invoice_items?.length ? (
          <>
            {invoice?.invoice_items?.map((item: any, itemKey: any) => {
              return (
                <>
                  <View key={itemKey}>
                    <View
                      style={[
                        styles.tableRow,
                        {
                          borderBottomWidth: 0,
                          height: 'auto',
                          paddingTop: 10,
                        },
                      ]}
                    >
                      {/* <Text style={styles.tableCell}>{date}</Text> */}
                      <View style={[styles.activityCell]}>
                        <Text style={[styles.tableCell, styles.activityText]}>
                          {item?.product_name ||
                            item?.package_offering?.name ||
                            item?.services?.name}
                        </Text>
                      </View>
                      <Text style={[styles.tableCell, { textAlign: 'center' }]}>
                        {item?.quantity}
                      </Text>
                      <Text style={[styles.tableCell, { textAlign: 'right' }]}>
                        {/* ${item?.price.toFixed(2)} */}
                        {formatMoney(item?.price)}
                      </Text>
                      <Text style={[styles.tableCell, { textAlign: 'right' }]}>
                        {/* ${(item?.quantity * item?.price)?.toFixed(2)} */}

                        {formatMoney(item?.quantity * item?.price)}
                      </Text>
                    </View>
                    <View
                      style={[
                        styles.tableRow,
                        {
                          marginBottom: 5,
                        },
                      ]}
                    >
                      {/* <Text style={styles.tableCell}>{date}</Text> */}
                      <View style={[{ width: '40%' }]}>
                        <Text style={[styles.descriptionText]}>
                          {item?.description ||
                            item?.services?.description ||
                            item?.package_offering?.description}
                        </Text>
                      </View>
                    </View>
                    <View style={[{ height: 10 }]}></View>
                  </View>
                </>
              );
            })}
          </>
        ) : invoice?.services_purchases?.length ? (
          <>
            {invoice?.services_purchases?.map(
              (purchase: any, purchaseKey: any) => {
                return (
                  <>
                    <View key={purchaseKey}>
                      <View
                        style={[
                          styles.tableRow,
                          {
                            borderBottomWidth: 0,
                            height: 'auto',
                            paddingTop: 10,
                          },
                        ]}
                      >
                        {/* <Text style={styles.tableCell}>{date}</Text> */}
                        <View style={[styles.activityCell]}>
                          <Text style={[styles.tableCell, styles.activityText]}>
                            {purchase?.invoice_items?.product_name}
                          </Text>
                        </View>
                        <Text
                          style={[styles.tableCell, { textAlign: 'center' }]}
                        >
                          {purchase?.invoice_items?.quantity}
                        </Text>
                        <Text
                          style={[styles.tableCell, { textAlign: 'right' }]}
                        >
                          {formatMoney(purchase?.invoice_items?.price)}
                          {/* ${purchase?.invoice_items?.price.toFixed(2)} */}
                        </Text>
                        <Text
                          style={[styles.tableCell, { textAlign: 'right' }]}
                        >
                          {formatMoney(
                            purchase?.invoice_items?.quantity *
                              purchase?.invoice_items?.price
                          )}
                          {/* $
                          {(
                            purchase?.invoice_items?.quantity *
                            purchase?.invoice_items?.price
                          )?.toFixed(2)} */}
                        </Text>
                      </View>
                      <View
                        style={[
                          styles.tableRow,
                          {
                            marginBottom: 5,
                          },
                        ]}
                      >
                        {/* <Text style={styles.tableCell}>{date}</Text> */}
                        <View style={[{ width: '40%' }]}>
                          <Text style={[styles.descriptionText]}>
                            {purchase?.invoice_items?.description}
                          </Text>
                        </View>
                      </View>
                      <View style={[{ height: 10 }]}></View>
                    </View>
                  </>
                );
              }
            )}
          </>
        ) : invoice?.service_id ? (
          <>
            <View
              style={[
                styles.tableRow,
                { borderBottomWidth: 0, paddingTop: 10 },
              ]}
            >
              {/* <Text style={styles.tableCell}>{date}</Text> */}
              <View style={styles.activityCell}>
                <Text style={[styles.tableCell, styles.activityText]}>
                  {activity || invoice?.services?.name}
                </Text>
              </View>
              <Text style={[styles.tableCell, { textAlign: 'center' }]}>
                {quantity}
              </Text>
              <Text style={[styles.tableCell, { textAlign: 'right' }]}>
                {/* $
                {invoice?.services
                  ? invoice?.services?.price?.toFixed(2)
                  : rate?.toFixed(2)} */}

                {formatMoney(
                  invoice?.services ? invoice?.services?.price : rate
                )}
              </Text>
              <Text style={[styles.tableCell, { textAlign: 'right' }]}>
                {/* $
                {(
                  quantity *
                  (invoice?.services ? invoice?.services?.price : rate)
                )?.toFixed(2)} */}

                {formatMoney(
                  quantity *
                    (invoice?.services ? invoice?.services?.price : rate)
                )}
              </Text>
            </View>
            <View style={[styles.tableRow, { borderBottomWidth: 0 }]}>
              {/* <Text style={styles.tableCell}>{date}</Text> */}
              <View style={styles.activityCell}>
                <Text style={[styles.tableCell, styles.activityText]}>
                  {invoice?.services?.description || ''}
                </Text>
              </View>
            </View>
          </>
        ) : null}

        {/* <View style={styles.divider}></View> */}

        {referral === 'true' && (
          <View style={styles.tableRow}>
            <Text style={styles.tableCell}>{date}</Text>
            <View style={styles.activityCell}>
              <Text
                style={[styles.tableCell, { width: '80%', lineHeight: '20px' }]}
              >
                Free Referral Credit
              </Text>
            </View>
            <Text style={styles.tableCell}>{quantity}</Text>
            <Text style={[styles.tableCell, { paddingRight: '1px' }]}>
              {'-' + rate.toFixed(2)}
            </Text>
            <Text style={[styles.tableCell, { paddingRight: '5px' }]}>
              {invoice?.service_id
                ? (quantity * -rate)?.toFixed(2)
                : rate?.toFixed(2)}
            </Text>
          </View>
        )}

        {referral === 'true' ? (
          <View style={styles.totalsSection}>
            <Text style={styles.totalText}>Total: </Text>
            <Text style={[styles.totalAmount]}>
              {/* ${rate?.toFixed(2)} */}${(-rate + rate)?.toFixed(2)}
            </Text>
          </View>
        ) : (
          <View style={styles.totalsSection}>
            <View style={[styles.totalBox, { paddingTop: '5px' }]}>
              <View style={styles.totalBoxLeft}>
                {' '}
                <Text style={styles.totalText}>Subtotal: </Text>
              </View>
              <View style={styles.totalBoxRight}>
                <>
                  {invoice?.invoice_item_data?.length ? (
                    <Text style={[styles.totalAmount]}>
                      {formatMoney(
                        invoice?.invoice_item_data?.reduce(
                          (sum: number, item: any) => {
                            return (
                              sum +
                              Number(item.quantity || 0) *
                                Number(item.price || 0)
                            );
                          },
                          0
                        )
                      )}
                    </Text>
                  ) : invoice?.invoice_items?.length ? (
                    <Text style={[styles.totalAmount]}>
                      {formatMoney(
                        invoice?.invoice_items?.reduce(
                          (sum: number, item: any) => {
                            return (
                              sum +
                              Number(item.quantity || 0) *
                                Number(item.price || 0)
                            );
                          },
                          0
                        )
                      )}
                    </Text>
                  ) : invoice?.services_purchases?.length ? (
                    <Text style={[styles.totalAmount]}>
                      {formatMoney(
                        invoice?.services_purchases?.reduce(
                          (sum: number, item: any) => {
                            const itemTotal =
                              Number(item?.invoice_items?.quantity || 0) *
                              Number(item?.invoice_items?.price || 0);
                            return sum + itemTotal;
                          },
                          0
                        )
                      )}
                    </Text>
                  ) : (
                    <>
                      {invoice?.services ? (
                        <Text style={[styles.totalAmount]}>
                          {formatMoney(invoice?.services?.price * invoice?.qty)}
                        </Text>
                      ) : (
                        0
                      )}
                    </>
                  )}
                </>
              </View>
            </View>
            {/* Show discount if it exists */}
            {invoice?.discount?.value ? (
              <View style={[styles.totalBox, { paddingTop: '5px' }]}>
                <View style={styles.totalBoxLeft}>
                  <Text style={styles.totalText}>
                    Discount
                    {typeof invoice.discount === 'object' &&
                    invoice.discount.type === 'percentage'
                      ? `(${invoice.discount.value}%)`
                      : ''}
                    :
                  </Text>
                </View>
                <View style={styles.totalBoxRight}>
                  <Text style={styles.totalAmount}>
                    (
                    {formatMoney(
                      (() => {
                        if (
                          typeof invoice.discount === 'object' &&
                          invoice.discount.value > 0
                        ) {
                          if (invoice.discount.type === 'percentage') {
                            const subtotal =
                              invoice?.invoice_items?.reduce(
                                (sum: number, item: any) => {
                                  return (
                                    sum +
                                    Number(item.quantity || 0) *
                                      Number(item.price || 0)
                                  );
                                },
                                0
                              ) || rate;
                            return (
                              (subtotal * invoice.discount.value) /
                              100
                            ).toFixed(2);
                          } else {
                            return invoice.discount.value.toFixed(2);
                          }
                        } else if (
                          typeof invoice.discount === 'number' &&
                          invoice.discount > 0
                        ) {
                          return invoice.discount.toFixed(2);
                        }
                        return '0.00';
                      })()
                    )}
                    )
                  </Text>
                </View>
              </View>
            ) : null}

            {/* Show individual taxes if they exist */}
            {individualTaxes.length > 0 ? (
              <>
                {individualTaxes.map((tax: any) => (
                  <View
                    key={tax.id + '' + tax.name}
                    style={[styles.totalBox, { paddingTop: '5px' }]}
                  >
                    <View style={styles.totalBoxLeft}>
                      <Text style={styles.totalText}>
                        {tax.name} ({tax.rate}%):
                      </Text>
                    </View>
                    <View style={styles.totalBoxRight}>
                      <Text style={styles.totalAmount}>
                        {/* {`$${tax.amount.toFixed(2)}`} */}

                        {formatMoney(tax.amount)}
                      </Text>
                    </View>
                  </View>
                ))}
              </>
            ) : null}

            {/* Show total */}
            <View style={[styles.totalBox, { paddingTop: '5px' }]}>
              <View style={styles.totalBoxLeft}>
                <Text style={[styles.totalText, { fontWeight: 'bold' }]}>
                  Total:
                </Text>
              </View>
              <View style={styles.totalBoxRight}>
                <Text style={styles.totalAmount}>
                  {invoice?.invoice_item_data?.length ? (
                    <>
                      {formatMoney(
                        invoice?.invoice_item_data?.reduce(
                          (sum: number, item: any) => {
                            return (
                              sum +
                              Number(item.quantity || 0) *
                                Number(item.price || 0)
                            );
                          },
                          0
                        )
                      )}
                    </>
                  ) : invoice?.services ? (
                    <>
                      {formatMoney(
                        invoice?.services?.price * invoice?.qty +
                          Number(
                            invoice?.services?.taxes?.reduce(
                              (sum: number, tax: any) => {
                                return sum + Number(tax.amount || 0);
                              },
                              0
                            ) || 0
                          )
                      )}
                    </>
                  ) : (
                    // formatMoney(invoice?.services?.price * invoice?.qty)
                    <>
                      {`${
                        invoice?.service_id
                          ? formatMoney(quantity * rate)
                          : formatMoney(rate)
                      }`}
                    </>
                  )}
                </Text>
              </View>
            </View>

            {transactions.map((transaction, transactionKey) => {
              return (
                <>
                  <View
                    style={[styles.totalBox, { paddingTop: '5px' }]}
                    key={transactionKey}
                  >
                    <View style={styles.totalBoxLeft}>
                      <Text style={styles.totalText}>
                        Payment on{' '}
                        {moment(transaction?.transaction_date).format(
                          'MMMM D, YYYY'
                        )}{' '}
                        using{' '}
                        {transaction?.payment_method
                          ?.toLowerCase()
                          .replace(/_/g, ' ')}
                        :
                      </Text>
                    </View>

                    <View style={styles.totalBoxRight}>
                      <Text style={styles.totalAmount}>
                        {/* (${transaction?.amount?.toFixed(2)}) */}

                        {`(${formatMoney(transaction?.amount)})`}
                      </Text>
                    </View>
                  </View>
                </>
              );
            })}
            {/* <View style={styles.dividerPayment}></View> */}
          </View>
        )}
        <View style={styles.dividerPayment} />

        {/* Amount Due */}
        <View style={styles.totalsSectionAmountDue}>
          <View style={styles.totalBox}>
            <View style={styles.totalBoxLeft}>
              <Text style={[styles.totalText, { fontWeight: 'bold' }]}>
                Amount Due:
              </Text>
            </View>
            <View style={styles.totalBoxRight}>
              <Text style={[styles.totalAmount, { fontWeight: 'bold' }]}>
                {/* ${amountDue?.toFixed(2)} */}
                {formatMoney(amountDue)}
              </Text>
            </View>
          </View>
        </View>

        {/* memo */}
        {memo ? (
          <View
            style={[
              styles.memo,
              {
                marginBottom: '15px',
                paddingTop: '40px',
              },
            ]}
          >
            <Text style={{ fontWeight: 'bold' }}>Notes / Terms</Text>
            <Text
              style={[
                styles.memoText,
                { color: '#2f3438' },
                { fontWeight: 'thin' },
                { top: '.5rem' },
              ]}
            >
              {memo}
            </Text>
          </View>
        ) : null}
        {/* <View style={styles.memoDivider}></View> */}

        {/* Balance section */}
        {/* <View
          style={[
            styles.balanceSection,
            { width: '100%', marginBottom: '10px' },
          ]}
        >
          <Text style={styles.balanceText}>BALANCE DUE</Text>
          <Text style={styles.balanceAmount}>${balance?.toFixed(2)}</Text>
        </View> */}

        {/* memo */}
        {/* <View style={styles.billToSection}>
          <View>
            <Text style={styles.sectionTitle}>Memo</Text>
            <Text style={styles.text}>{memo}</Text>
          </View>
        </View> */}
        {/* <View style={{ marginTop: 10, flexDirection: 'row' }}>
          <Text style={styles.balanceText}>Memo</Text>
          <Text style={styles.balanceAmount}>{memo}</Text>
        </View> */}
        <View style={styles.footer}>
          <View style={[styles.soap]}>
            <Text>Powered by </Text>
            <Image src={soapLogo.src} style={styles.footerLogo} />
          </View>
        </View>
      </Page>
    </Document>
  );
}

const styles = StyleSheet.create({
  container: {
    fontSize: 10,
    paddingVertical: 40,
  },
  activityText: {
    width: '100%',
    lineHeight: 1.4, // Adjust for better readability
    flexWrap: 'wrap',
    wordBreak: 'break-word',
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 30,
    alignItems: 'flex-start',
    paddingHorizontal: 40,
  },
  logo: {
    width: 150,
    height: 50,
    objectFit: 'contain',
  },
  title: {
    textAlign: 'right',
    fontSize: 20,
    // fontWeight: 'semibold',
    marginBottom: 5,
  },
  invoiceHeader: {
    textAlign: 'right',
    alignItems: 'flex-end',
  },
  orgName: {
    textAlign: 'right',
    fontSize: 10,
    fontWeight: 'extrabold',
    marginBottom: 5,
  },
  divider: {
    height: 1,
    width: '100%',
    // borderWidth: '2px',
    borderColor: 'grey',
    marginBottom: 15,
    // borderTop: 1,
    backgroundColor: '#CBD5E0',
  },
  dividerPayment: {
    height: 2,
    backgroundColor: '#CBD5E0',
    marginVertical: 8,
    alignSelf: 'flex-end',
    width: '50%',
    minWidth: 300,
  },
  memoDivider: {
    height: 2,
    backgroundColor: '#CBD5E0',
    marginVertical: 8,
    alignSelf: 'flex-end',
    width: '100%',
  },
  text: {
    marginBottom: 3,
  },
  receiptTitle: {
    fontSize: 24,
    color: '#075985',
    marginBottom: 20,
    fontWeight: 'bold',
  },
  billToSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 40,
  },
  billToBox: {
    flex: 1,
  },
  billToBoxRight: {
    flex: 1,
    gap: 10,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  billingInfo: {
    textAlign: 'right',
    fontWeight: 'thin',
    color: '#2f3438',
  },
  descriptionText: {
    fontWeight: 'thin',
    color: '#2f3438',
  },
  sectionTitle: {
    // fontSize: 12,
    color: '#2f3438',
    // marginBottom: 5,
    // fontWeight: 'bold',
  },
  salesInfo: {
    flexDirection: 'row',
    gap: 10,
    width: '100%',
    flex: 1,
    justifyContent: 'flex-end',
  },
  salesTitle: {
    fontWeight: 'extrabold',
    width: 'auto',
    // textAlign: 'right',
  },
  salesTitleBox: {
    fontWeight: 'bold',
    justifyContent: 'flex-end',
    flex: 1,
  },
  salesData: {
    color: '#808080',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#4A5568',
    paddingVertical: 8,
    borderBottom: 1,
    borderBottomColor: '#3E5D78',
    paddingHorizontal: 40,
  },
  tableHeaderCell: {
    flex: 1,
    color: '#fff',
    fontWeight: 'bold',
  },
  activityCell: {
    flex: 3,
    // paddingRight: 5,
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 5,
    paddingHorizontal: 40,
    // borderBottomWidth: 1, // Ensure this applies to all rows except the first
    // borderBottomColor: '#CBD5E0',
  },
  tableCell: {
    flex: 1,
    color: '#2f3438',
  },
  totalsSection: {
    flexDirection: 'column',
    justifyContent: 'flex-end',
    marginTop: 10,
    paddingTop: 6,
    borderTopWidth: 2,
    borderTopColor: '#CBD5E0',
    borderTopStyle: 'solid',
    width: '100%',
    paddingHorizontal: 20,
  },
  totalsSectionAmountDue: {
    flexDirection: 'column',
    justifyContent: 'flex-end',
    width: '100%',
    paddingHorizontal: 20,
  },

  totalBox: {
    flexDirection: 'row',
    fontWeight: 'bold',
    justifyContent: 'flex-end',
    paddingHorizontal: 40,
    gap: 20,
  },
  totalText: {
    // marginRight: 20,
    fontWeight: 'thin',
    color: '#2f3438',
  },
  memo: {
    flexDirection: 'column',
    fontWeight: 'bold',
    justifyContent: 'flex-start',
    paddingHorizontal: 40,
  },
  totalAmount: {
    width: 80,
    fontWeight: 'thin',
    color: '#2f3438',
  },
  memoText: {
    width: '100%',
  },
  balanceSection: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 6,
    borderTop: 1,
    borderTopColor: '#E5E7EB',
    paddingTop: 10,
  },
  balanceText: {
    marginRight: 20,
    fontWeight: 'bold',
  },
  balanceAmount: {
    fontWeight: 'bold',
    flex: 1,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    // paddingHorizontal: 40,
  },
  footerLogo: {
    width: 50,
    height: 20,
    marginLeft: 5,
  },
  soap: {
    textAlign: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  invoiceDetailsContainer: {
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    flex: 1,
    // minWidth: 280,
  },
  invoiceDetailRow: {
    flexDirection: 'row',
    // gap: 6,
    alignItems: 'center',
    // width: 280,
    paddingBottom: 4,
  },
  invoiceDetailLabel: {
    fontSize: 11,
    fontWeight: 'bold',
    textAlign: 'right',
    color: '#2f3438',
  },
  invoiceDetailValue: {
    fontSize: 11,
    textAlign: 'left',
    color: '#2f3438',
    width: 100,
    paddingLeft: 20,
  },
  totalBoxLeft: {
    width: '75%',
    textAlign: 'right',
  },
  totalBoxRight: {
    width: '10%',
    textAlign: 'left',
  },
});
